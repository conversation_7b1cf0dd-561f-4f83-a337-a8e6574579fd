## ENV

**サンプル**

```
SDK_HOST_URL=https://storage.googleapis.com/unicorncart-chatbot-assets/stg
API_BASE_URL=https://chatbot.unicorncart.jp
ID_CHAT_BOT="stgchatbot"
ID_JS_SCRIPT="stgchatbot"
WEBSOCKET_HOST=wss://chatbot.unicorncart.jp/cable
ANALY_P_TOOLS=https://dev-api.uganalytics.net/analy/release/analycore.min.js
```

- `SDK_HOST_URL`:ファイルをダウンロードするディレクトリ
- `ANALY_P_TOOLS`: ログツール
- `WEBSOCKET_HOST`: WEBSOCKET 接続用の URL
- `API_BASE_URL`: API 接続用の URL
- `ID_CHAT_BOT`: チャットボット外部の `<div>` タグの ID
- `ID_JS_SCRIPT`:
  - 複数のボットを 1 つの LP で実行する場合、正しいシナリオとアカウントを取得するためのボット識別用の ID
  - 異なるビルドの名前付け

## スクリプト

```javascript
(function () {
  if (window !== window.parent) return;

  var widgetTag = document.createElement("script");
  widgetTag.src =
    "https://storage.googleapis.com/unicorncart-chatbot-assets/stg/stgchatbot.sdk.min.js?v=**********";
  widgetTag.async = true;
  widgetTag.id = "${ID_CHAT_BOT}";
  widgetTag.dataset.scenario = "7620b152-34dc-4a67-a53e-1442b3c5b6c8";
  widgetTag.dataset.account = "20adc23e-f2ea-49b7-9d17-4763fcece4c3";
  document.body.appendChild(widgetTag);
})();
```

## イベント

1. `UnicornCartChatbot.${ID_JS_SCRIPT}.open()`: チャットボットを開く
2. `UnicornCartChatbot.${ID_JS_SCRIPT}.close()`: チャットボットを閉じる
3. `UnicornCartChatbot.${ID_JS_SCRIPT}.handleResponse()`: レスポンスを処理する

(\*) _Đối với `ID_JS_SCRIPT` chỉ dùng cho function bật nhiều bot trên một LP _
