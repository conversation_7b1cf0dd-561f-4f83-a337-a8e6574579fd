CREATE TABLE `users` (
  `id` uuid PRIMARY KEY,
  `email` varchar(255),
  `encrypted_password` varchar(255),
  `user_type` int4,
  `created_at` timestamp,
  `updated_at` timestamp,
  `lock_version` int4
);

CREATE TABLE `tasks` (
  `id` uuid PRIMARY KEY,
  `class_name` varchar(255),
  `description` varchar(255),
  `created_at` timestamp,
  `updated_at` timestamp,
  `lock_version` int4
);

CREATE TABLE `scenarios` (
  `id` uuid PRIMARY KEY,
  `name` varchar(255),
  `description` varchar(255),
  `root_node_uid` varchar(255),
  `active` bool,
  `support_ui_enable` bool,
  `progress_bar_enable` bool,
  `match_value` varchar(255),
  `match_type` int4,
  `user_id` varchar2,
  `created_at` timestamp,
  `updated_at` timestamp,
  `lock_version` int4
);

CREATE TABLE `nodes` (
  `id` uuid PRIMARY KEY,
  `scenario_id` varchar(255),
  `node_type` int4,
  `label` varchar(255),
  `body` jsonb,
  `position` jsonb,
  `root_node` bool,
  `next_node_uid` varchar(255),
  `created_at` timestamp,
  `updated_at` timestamp,
  `lock_version` int4
);

CREATE TABLE `scenario_settings` (
  `id` uuid PRIMARY KEY,
  `name` varchar(255),
  `description` varchar(255),
  `active` bool,
  `setting_type` int4,
  `general_settings` jsonb,
  `theme_customize_settings` jsonb,
  `css_customize` jsonb,
  `javascript_customize` jsonb,
  `scenario_id` varchar(255),
  `created_at` timestamp,
  `updated_at` timestamp,
  `lock_version` int4
);

ALTER TABLE `scenarios` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

ALTER TABLE `nodes` ADD FOREIGN KEY (`scenario_id`) REFERENCES `scenarios` (`id`);

ALTER TABLE `scenario_settings` ADD FOREIGN KEY (`scenario_id`) REFERENCES `scenarios` (`id`);
