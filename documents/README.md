# Uc Chatbot ドキュメント

## フォルダ構成

フォルダ構成は以下の通りです:

```bash
documents/             # ドキュメントルートディレクトリ
|- apis/               # APIの説明フォルダ
|- assets/             # 画像アセット等を格納するフォルダ
|- database.md         # データベース情報フォルダ
|- nodes               # ノードの詳細
|- developer-guides/   # 開発者向けガイドフォルダ
|- features/           # システムの機能と動作の説明フォルダ
|- README.md

```

## 目次

1. [データベース構造](./database.md)
2. [API ガイド](./apis)
3. [ワークフロー](./features)
4. [開発者ガイド](./developer-guides)
5. [ノードの詳細](./nodes)
