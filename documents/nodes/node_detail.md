## Node Detail

<table>
  <tr>
    <th>Node Type</th>
    <th>body:type</th>
    <th>body:repeatable</th>
    <th>body:settings:type</th>
    <th>Validate</th>
  </tr>
  <tr>
    <td>message</td>
    <td></td>
    <td rowspan="7">
      true/false (Nodes with the repeatable attribute set to true can appear
      multiple times in the bot.)
    </td>
    <td>
      1. text<br />
      2. image<br />
      3. video<br />
      4. images
    </td>
    <td>yes</td>
  </tr>
  <tr>
    <td>input</td>
    <td>
      1. name <br />
      2. password <br />
      3. text <br />
      4. select <br />
      5. address <br />
      6. sex_and_birthday <br />
      7. credit_card<br />
      8. radio_button<br />
      9. radio_button_reselectable<br />
      10. tel_email_password<br />
      11. scheduled_delivery<br />
      12. checkbox<br />
      13. full_name<br />
      14. name_sex_birthday<br />
      15. name_birthday_tel_email<br />
      16. information
    </td>
    <td>
      1. input<br />
      2. select<br />
      3. radio <br />
      4. date
    </td>
    <td>yes</td>
  </tr>
  <tr>
    <td>condition</td>
    <td></td>
    <td></td>
    <td>no</td>
  </tr>
  <tr>
    <td>button</td>
    <td></td>
    <td>
      1. template <br />
      2. policy<br />
      3. button
    </td>
    <td>no</td>
  </tr>
  <tr>
    <td>html_tasks</td>
    <td></td>
    <td></td>
    <td>yes</td>
  </tr>
  <tr>
    <td>headless_tasks</td>
    <td></td>
    <td></td>
    <td>no</td>
  </tr>
  <tr>
    <td>set_value</td>
    <td></td>
    <td></td>
    <td>no</td>
  </tr>
</table>

## Setting Detail

| **Field** | **Description**                                                                                |
| --------- | ---------------------------------------------------------------------------------------------- |
| label     | A label to describe the parent field.                                                          |
| variable  | Variable name or identifier for the parent field                                               |
| layout    | Specifies how the parent field is divided into parts, which could be used for layout purposes. |
| settings  | Setting of **child fields**                                                                    |

## Child Fields

| **Field**   | **Description**                                                                 |
| ----------- | ------------------------------------------------------------------------------- |
| label       | A label to describe the child field.                                            |
| variable    | Variable name or identifier for the child field                                 |
| type        | Type of input for the child field (input, select....)                           |
| required    | Determines whether this field is required or not                                |
| placeholder | Placeholder text to provide an example or hint for the expected input.          |
| ratio       | Specifies how much space the child field occupies relative to the parent field. |

- Special fields

  - `headless_tasks`:

    ```json
    {
      "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
      "input": {
        "label": "",
        "nodeType": "headless_tasks",
        "body": {
          "settings": [
            {
              "task": {
                "className": "CrawlerQuantityService"
              },
              "inputs": {
                "url": {
                  "value": "",
                  "type": "string"
                },
                "product": {
                  "value": "",
                  "type": "string"
                },
                "sei": {
                  "value": "",
                  "type": "string"
                },
                "mei": {
                  "value": "",
                  "type": "string"
                }
              },
              "outputs": ["status", "data"]
            }
          ]
        }
      }
    }
    ```

    - **task:className**: Define which Selenium task is triggrer when called by this node. Class name is a name of the class in the task runner project.

    - **inputs**: is the input for the Selenium task, with `product`, `sei`, `mei`... as the variable names and `url` is the default value that needs to be set.

    - **outputs**: is the selenium crawler task output, with "status", "data" is the variable name.

    - **use**: This node is typically used after a button node or a confirm purchase upsell node.

  - `html_tasks`:

    ```json
    {
      "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
      "input": {
        "label": "",
        "nodeType": "html_tasks",
        "body": {
          "settings": [
            {
              "task": {
                "content": "<input your html script here>"
              }
            }
          ]
        }
      }
    }
    ```

    - **task:content**: The content of the JavaScript code includes actions (filling in information into forms, submitting forms)
    - **use**: This node is typically used after a button node or a confirm purchase upsell node.

  - `days_after_current`(\*): This represents the number of days from the current date to the selected date. For example, if days_after_current is "4", it means the selected date will be the current date plus 4 days.
  - `range_days`(\*): This is the time span (in days) for which you want to generate a series of dates starting from the selected date (the date after adding days_after_current). For example, if range_days is "18", it means that starting from the selected date (after adding days_after_current), you will have an 18-day period to choose a date.

(\*) _Only applicable to the variable scheduled_date_

## List Variables

| **Variable**           | **Description**                       |
| ---------------------- | ------------------------------------- |
| product                | Product                               |
| sei                    | 姓                                    |
| mei                    | 姓                                    |
| seifuri                | セイ                                  |
| meifuri                | セイ                                  |
| zipcode                | 郵便番号                              |
| prefectures            | 選択してください                      |
| address01              | Address 01                            |
| address02              | Address 02                            |
| address03              | Address 03                            |
| mail                   | メールアドレス                        |
| tel                    | 電話番号                              |
| tel1                   | 電話番号                              |
| tel2                   | 電話番号                              |
| tel3                   | 電話番号                              |
| password               | Password                              |
| sex                    | 性別                                  |
| cv_upsell_1            | CV Upsell 1                           |
| cv_upsell_2            | CV Upsell 2                           |
| coupon                 | クーポンコード                        |
| payment_method         | お支払い方法                          |
| day                    | 生年月日                              |
| month                  | 生年月日                              |
| year                   | 生年月日                              |
| credit_card            | クレジットカード                      |
| result_msg             | Result Message                        |
| result_msg_thank_offer | Result Message Thank Offer            |
| error                  | Error Handle Submit when payment fail |
| scheduled_date         | お届け日の指定                        |
| scheduled_time         | お届け時間の指定                      |
| agreement1             | 同意                                  |
| agreement2             | 同意                                  |
| agreement3             | 同意                                  |
| full_name              | お名前                                |
| full_name_kana         | フリガナ                              |
