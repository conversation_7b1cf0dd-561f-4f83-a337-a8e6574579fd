# Type Condition

## Description

This condition involves checking a variable's value against a specific condition, where the flow proceeds to the next node if the condition is met, otherwise it stops at this node

## Normal Flow

Admin Accesses Node Configuration:

- The admin accesses the node configuration interface in the chatbot admin page.

Selects Condition Node:

- The admin selects the option to add a new node.
- From the available node types, the admin selects the "condition" node.

Configures Node Settings:

- The admin locates the settings section for the condition node.
- In the settings, the admin specifies the type of condition node.
- The admin enters the necessary condition settings in the provided fields.

Defines Name Condition:

- The admin selects the option to add a name condition within the settings.
- The admin specifies the variable associated with the name condition (e.g., "payment_method").
- The admin assigns a specific value to the variable (e.g., "クレジット決済(手数料:無料)").

Saves Configuration:

- Once all settings are configured, the admin saves the node configuration.

Links Next Node:

- The admin specifies the next node(s) to which the flow should proceed after this condition node.
- The admin links the subsequent node(s) by providing their unique identifiers (UUIDs) in the "next_node_uid" field.

## Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "test create"
        nodeType: "condition"
        body: {
          settings: [
            { variable: "payment_method", value: "NP後払いwizRT(手数料:250円)" }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

## ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: {
        body: {
          type: "condition"
          settings: [
            { variable: "payment_method", value: "NP後払いwizRT(手数料:250円)" }
          ]
        }
        label: "//"
        nextNodeUid: ["bb9bf175-c1d1-4d7c-a0b5-73c179f2cfeb"]
        nodeType: "input"
      }
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      nodeId: "7c3986ba-05e7-4982-ba6d-a299882c625c"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
