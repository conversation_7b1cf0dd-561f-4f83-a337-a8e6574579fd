# Selenium チャットボットのドキュメント

## 1. Selenium のフロー

![Flow](../../../assets/submit_backend.png)

1. チャットボットでユーザーが「送信」をクリックすると、フォームのデータが Headless Tasks ノードに基づいてマッピングされます。
2. マッピングされたデータは WebSocket（WSS）を通じて Routing Service に転送されます。このサービスはデータを受信し、実行すべきタスクを特定して次の要求を送信します。
3. Backend サーバー内の Routing Service は REST API を介して Runner サーバー内の Task Runner を呼び出します。Runner サーバーは主要なタスク（ロジックの処理や定義されたタスクの実行など）を実行します。
4. タスク完了後、Task Runner は REST API を通じて Backend サーバーの Routing Service に応答を返します。
5. Routing Service は Task Runner からの応答を受け取り、処理して結果を ActionCable Channel に転送します。ActionCable は WebSocket（WSS）を利用して結果をチャットボットに送信します。
6. チャットボットは ActionCable Channel からの結果を受信し、ユーザーに Response Message を表示します。

この流れは、Selenium を使った自動化プロセスで、タスクの実行とチャネルとの通信が含まれています。

## 2. ノード `headless_tasks`

### JSON サンプル

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "",
    "nodeType": "headless_tasks",
    "body": {
      "settings": [
        {
          "task": {
            "class_name": "TestService"
          },
          "inputs": {
            "url": {
              "value": "https://stg.unicorncart.jp/lp?u=1_button_1_bot",
              "type": "string"
            },
            "sei": {
              "value": "",
              "type": "string"
            },
            "mei": {
              "value": "",
              "type": "string"
            },
            "seifuri": {
              "value": "",
              "type": "string"
            },
            "meifuri": {
              "value": "",
              "type": "string"
            },
            "zipcode": {
              "value": "",
              "type": "string"
            },
            "address02": {
              "value": "",
              "type": "string"
            },
            "tel1": {
              "value": "",
              "type": "string"
            },
            "tel2": {
              "value": "",
              "type": "string"
            },
            "tel3": {
              "value": "",
              "type": "string"
            },
            "mail": {
              "value": "",
              "type": "string"
            },
            "password": {
              "value": "",
              "type": "string"
            },
            "sex": {
              "value": "",
              "type": "string"
            },
            "day": {
              "value": "",
              "type": "string"
            },
            "month": {
              "value": "",
              "type": "string"
            },
            "year": {
              "value": "",
              "type": "string"
            },
            "payment_method": {
              "value": "",
              "type": "string"
            }
          },
          "outputs": {
            "data": {
              "message": "",
              "error": ""
            }
          }
        }
      ]
    }
  }
}
```

この JSON サンプルは、`headless_tasks` ノードでのタスクの設定を示しており、タスクの ID、名前、実行するアクションのパラメータなどを含んでいます。

- `task:className`: このノードによって呼び出されたときにトリガーされる Selenium タスクを指定します。クラス名は Task Runner プロジェクト内のクラス名です（API `tasks` を使用してタスク一覧を取得）。

- `inputs`: Selenium タスクの入力であり、`sei`、`mei` などの変数が含まれます。これらの変数は、各ノードに対応する値を表します。`url` を除き、`url` は Selenium が自動的にアクセスするウェブサイトのアドレス (URL) を表す特別なパラメータです。この変数は、Selenium がフォームの入力やウェブページ上の要素とのインタラクションを開始する場所を指定します。url はデフォルトで設定する必要があります。
- `outputs`: Selenium タスクのデータ収集の出力で、「data」という変数名には `message` と `error` が含まれます。

### API 作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          settings: [
            {
              task: { class_name: "TestService" }
              inputs: {
                url: { value: "", type: "string" }
                sei: { value: "", type: "string" }
                mei: { value: "", type: "string" }
                seifuri: { value: "", type: "string" }
              }
              outputs: { data: { message: "", error: "" } }
            }
          ]
        }
        label: null
        nodeType: "headless_tasks"
        rootNode: false
      }
      scenarioId: "dc7a0b27-d9ed-4d1d-8bc4-94a660d01076"
    }
  ) {
    message
  }
}
```

### リクエストボディの例

```json
{
  "input": {
    "body": {
      "settings": [
        {
          "task": { "class_name": "TestService" },
          "inputs": {
            "url": { "value": "", "type": "string" },
            "sei": { "value": "", "type": "string" },
            "mei": { "value": "", "type": "string" },
            "seifuri": { "value": "", "type": "string" }
          },
          "outputs": { "data": { "message": "", "error": "" } }
        }
      ]
    },
    "label": null,
    "nodeType": "headless_tasks",
    "rootNode": false
  },
  "scenarioId": "dc7a0b27-d9ed-4d1d-8bc4-94a660d01076"
}
```

### 例の結果

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully"
    }
  }
}
```

### API `tasks` をフェッチ

- クエリ

```graphql
query Tasks {
  tasks(input: { page: 1, perPage: 10, q: "{}" }) {
    metadata
    collection {
      className
      description
      id
    }
  }
}
```

- リクエストボディの例

```json
{
  "data": {
    "tasks": {
      "metadata": {
        "count": 3,
        "per_page": 10,
        "page": 1,
        "outset": 0,
        "pages": 1,
        "items": 3,
        "next": null,
        "prev": null,
        "from": 1,
        "to": 3
      },
      "collection": [
        {
          "className": "TestService",
          "description": null,
          "id": "cc10a48d-a040-4fda-b217-0b72c7add5c8"
        },
        {
          "className": "ExampleService",
          "description": null,
          "id": "af5ea7cc-1cac-44e6-8810-2b537807b119"
        },
        {
          "className": "1234",
          "description": null,
          "id": "20ada9f7-1603-4962-8987-8aba9e00d09e"
        }
      ]
    }
  }
}
```

## WebSocket から Routing Server へのデータ送信

### フロントエンド（JavaScript）のデータ送信関数

```javascript
const sendDataTaskRunnerToSystemMessageChannel = function (runnerData) {
  try {
    if (channel.value) {
      channel.value.perform("task_runner", runnerData);
    } else {
      console.log("Channel not initialized. Please connect WebSocket first.");
    }
  } catch (e) {
    console.log("Error:", e);
  }
};
```

- [File use](https://github.com/kero-chan/united-cart-chatbot/blob/1de33bcc65d98dd11b4c55b171119a825210c9c4/client/src/stores/scenario.js#L363)

- [File handle](https://github.com/kero-chan/united-cart-chatbot/blob/1de33bcc65d98dd11b4c55b171119a825210c9c4/client/src/ultilities/webSocket.js#L60-L70)

- 説明:

  - `runnerData` は、バックエンドに送信するタスクのデータを含むオブジェクトです。これには、処理するクラス名、入力データ、出力結果などが含まれます。
  - この関数は、WebSocket 接続が確立されているかどうかを確認し、接続されていれば**runnerData**を WebSocket 経由でバックエンドに送信します。
  - 接続されていない場合、WebSocket の接続が必要である旨のエラーメッセージを表示します。

### WebSocket から受信したデータを処理するバックエンド関数（Ruby）

```ruby
  def task_runner(data)
    ::Tasks::Executor.call(
      class_name: data["className"],
      inputs: data["inputs"],
      outputs: data["outputs"],
      user_id: params[:dataAccount],
      ssid: params[:ssid],
    )
  end
```

- [File handle](https://github.com/kero-chan/united-cart-chatbot/blob/1de33bcc65d98dd11b4c55b171119a825210c9c4/server/app/channels/system_message_channel.rb#L11-L19)

- 説明:

  - `data` は、フロントエンドから WebSocket 経由で送信されたデータで、次の情報が含まれています：
    - `className`: 実行するタスクを担当するクラス名（例: "MiraiSubmit"）。
    - `inputs`: タスクの実行に必要な入力データ（例: `sei`、`mei`。。）。
    - `outputs`: 実行後に期待される出力結果。
    - `user_id`: タスクを実行したユーザーの ID（WebSocket の接続パラメータから取得）。
    - `ssid`: セッション ID（WebSocket の接続パラメータから取得）。
  - この関数は、受け取ったデータを使って**Tasks::Executor.call**を呼び出し、指定されたクラス名に基づいてタスクを実行します。

## 3. API: `/api/v1/tasks/execute`

API は、クラス名（class_name）、入力パラメータ（inputs）、および出力（outputs）を含む入力パラメータを受け取ります。指定された class_name に基づいて、メソッドは対応する class_name を検索し、提供されたパラメータを使用してタスクを実行します。タスクが成功裏に実行された後、結果またはエラーは処理され、Backend Server に JSON 形式で返されます。

### API エンドポイント

#### URL

```
{TASK_RUNNER_URL}/api/v1/tasks/execute
```

#### 方法

`POST`

#### リクエストパラメータ

- `class_name` (文字列): 実行するタスクを表すクラスの名前。
- `inputs` (JSON 文字列): タスクに必要な入力パラメータを含む JSON 形式の文字列。
- `outputs` (JSON 文字列): 期待される出力結果を指定する JSON 形式の文字列。

#### 応答

```json
{
  "data": {
    "message": "Create CV Failed",
    "error": true
  }
}
```

### プロジェクトで API を使用する

- API ハンドル: [ファイル URL](../../../../task_runner/app/controllers/api/v1/tasks_controller.rb)

- API 使用: [ファイル URL](../../../../server/app/services/tasks/executor.rb)

## 4. タスクランナーサーバーの例サービス

- [サービスサンプル](../../../../task_runner/app/services/tasks/runner/mirai_submit.rb)

- [ベースサービス](../../../../task_runner/app/services/tasks/runner/shared_methods.rb)
