## 1. <PERSON><PERSON> Node Input Detail

- [<PERSON><PERSON> Seed](../../../../server/db/seeds/mirai_seed.rb)

- Node Name

  ![Image](../../../assets/name.png)

- Node Address

  ![Image](../../../assets/address.png)

- Node text Email Tel

  ![Image](../../../assets/email_tel.png)

- Node Payment Method

  ![Image](../../../assets/radio_button.png)

- Node CreditCard

  ![Image](../../../assets/credit_card.png)

- Node Check Offer

  ![Image](../../../assets/offer.png)

- Node Button

  ![Image](../../../assets/button.png)

## 2. Activity of the Scenario

![ScreenRecording2024-12-12at14 57 35-ezgif com-video-to-gif-converter](../../../assets/active_sceanrio.gif)

## 3. <PERSON><PERSON> Submit

Submitted using the `headless_task` server, so please check the information [here](./headlessTask.md) for more details.
