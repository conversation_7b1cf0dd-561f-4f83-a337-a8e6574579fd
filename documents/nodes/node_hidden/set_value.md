## Set Value

### リクエストボディの例

```json
{
  "input": {
    "body": {
      "repeatable": true,
      "settings": [
        {
          "variable": "payment_method",
          "value": "クレジット決済(手数料:無料)"
        }
      ]
    },
    "label": null,
    "nodeType": "set_value"
  },
  "scenarioId": "9bca046f-9073-400f-8c60-4cb770c0375e"
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "repeatable": true,
          "settings": [
            {
              "variable": "payment_method",
              "value": "クレジット決済(手数料:無料)"
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "set_value",
        "position": null,
        "rootNode": false,
        "uid": "7d5b854a-1499-4393-89d5-e1e728c5a1d8"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          repeatable: true
          settings: [
            { variable: "payment_method", value: "クレジット決済(手数料:無料)" }
          ]
        }
        label: null
        nodeType: "set_value"
      }
      scenarioId: "9bca046f-9073-400f-8c60-4cb770c0375e"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```
