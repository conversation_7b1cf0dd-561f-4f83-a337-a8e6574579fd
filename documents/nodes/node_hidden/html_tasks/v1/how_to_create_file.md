## ファイル `html_tasks` [サンプル](./sample.js)

## `html_tasks` クレートを作成する際の主なポイント

1. iframe の設定

- コードサンプル

  ```javascript
  const IFRAME_ID = "jimos_iframe";
  const IFRAME_SRC = window.parent.location.href;
  const DEFAULT_IFRAME_STYLE = {
    width: "900px",
    height: "600px",
    left: "0",
    position: "fixed",
    bottom: "0",
    borderRadius: "10px",
    margin: "10px",
    boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px",
    zIndex: "9999",
  };
  ```

- 目的: このコードは iframe 要素のいくつかの定数を設定します。

2. 関数作成 Iframe

- コードサンプル

  ```javascript
  async function createIframe() {
    const iframe = document.createElement("iframe");
    Object.assign(iframe, { id: IFRAME_ID, src: IFRAME_SRC });
    Object.assign(iframe.style, DEFAULT_IFRAME_STYLE);
    document.body.appendChild(iframe);
  }
  ```

- 目的：この関数は iframe 要素を動的に作成し、その src を現在のページの URL に設定し、定義済みのスタイルを適用して document.body に追加します。

3. 関数初期化

- コードサンプル

  ```javascript
  async function init() {
    document.getElementById(IFRAME_ID)?.remove();
    await createIframe();
    await createCv();
  }
  ```

- 目的：関数 init は、指定された ID（jimos_iframe）を持つ既存の iframe を削除し、その後、新しい iframe を作成して createCv 関数を呼び出して iframe を処理します。

4. 履歴書を作成する

- コードサンプル

  ```javascript
  async function createCv() {
    const ifm = document.querySelector("iframe#jimos_iframe");
    var ifmdoc = await pollIframeContentWithLimit(ifm);

    if (ifmdoc) {
      try {
        await main(ifm);
      } catch (error) {
        console.error("Error parsing JSON:", error.message);
      }
    } else {
      console.log("iframe not found");
    }
  }
  ```

- 目的: この関数は iframe が内容を読み込むのを待ち、その後 main 関数を呼び出してフォームの入力と iframe 内のナビゲーションを処理します。読み込み中にエラーが発生した場合は、エラーメッセージを記録します。

5. コンテンツ Iframe を確認する

- コードサンプル

  ```javascript
  function pollIframeContentWithLimit(
    doc,
    maxAttempts = 10,
    intervalMs = 1000
  ) {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const interval = setInterval(() => {
        const bodyContent = doc.contentDocument.body?.innerHTML || "";
        if (bodyContent.trim() !== "") {
          clearInterval(interval);
          resolve(doc.contentDocument);
        }

        attempts++;
        if (attempts >= maxAttempts) {
          clearInterval(interval);
          reject(
            new Error(
              `Iframe content not available after ${maxAttempts} attempts.`
            )
          );
        }
      }, intervalMs);
    });
  }
  ```

- 目的：この関数は、iframe の内容を定期的にチェックし、インタラクションの準備が整った時期を特定します。

6. 主な機能

- コードサンプル

  ```javascript
  async function main(ifm) {
    var ifmdoc = ifm.contentWindow.document;
    try {
      await first_click(ifm);
      var isPcPage = await _waitForExpectedPath("input");
      if (!isPcPage) {
        await parse_lp_error(ifmdoc);
        return;
      }

      console.log("フォーム入力ページに到達したため、処理に入ります");
      console.time("fill_data");
      await pc_post();
      console.timeEnd("fill_data");

      var isConfirmPage = await _waitForExpectedPath("confirm");
      if (!isConfirmPage) {
        await parse_lp_error(ifmdoc);
        return;
      }
      console.log("確認ページに到達しました");
      console.time("確認ページ");
      await confirm_page();
      console.timeEnd("確認ページ");

      var isThanksPage = await _waitForExpectedPath("finish");
      if (!isThanksPage) {
        await parse_lp_error(ifmdoc);
        return;
      }
      console.log("完了ページに到達しました");
      console.time("thanks_page");
      await thanks_page();
      console.timeEnd("thanks_page");
    } catch (error) {
      console.error("Error parsing JSON:", error.message);
    }
  }
  ```

- 目的: この関数はフォーム入力のプロセスを調整し、異なる段階（入力、確認、感謝）をチェックします。iframe を通じてフォームのフィールドに入力し、異なる段階を進むことで、適切な補助関数（pc_post、confirm_page、など）を呼び出します。

7. 他のいくつかの関数

- `_waitForElement`

  - コードサンプル

    ```javascript
    var _waitForElement = function (
      doc,
      selector,
      isMovedPage = false,
      delay = 250,
      tries = 40
    ) {
      const element = doc.querySelector(selector);
      if (element === null) {
        if (tries <= 0) {
          return Promise.reject(selector + " is null");
        }
        return new Promise((resolve) => setTimeout(resolve, delay)).then(() =>
          _waitForElement(doc, selector, isMovedPage, delay, tries - 1)
        );
      } else {
        return Promise.resolve(element);
      }
    };
    ```

  - 目的: この関数は、DOM 内で要素が表示されるまで、その存在を繰り返しチェックして待機します。見つからない場合は、最大試行回数に達するまで再試行を行います。

- `send_keys`

  - コードサンプル

    ```javascript
    var send_keys = (doc, selector, val) =>
      new Promise((resolve) => {
        var _target = doc.querySelector(selector);
        if (_target) {
          setTimeout(() => {
            _target.value = val;
            ["blur", "change", "input"].forEach((e) => {
              _target.dispatchEvent(new Event(e, { bubbles: true }));
            });
            resolve();
          }, 100);
        } else {
          resolve();
        }
      });
    ```

  - 的: 指定されたパスを含む URL になるまで、または最大試行回数に達するまで、iframe の URL を繰り返しチェックします。これは、iframe 内でページが要求通りに変更されたかどうかを待機して確認するための方法です。

- `_waitForExpectedPath`

  - コードサンプル

    ```javascript
    async function _waitForExpectedPath(path, interval = 100, attempt = 100) {
      return new Promise((resolve, reject) => {
        let currentAttempt = 0;

        function checkURL() {
          var ifm = document.querySelector("iframe#jimos_iframe");
          var ifmPath = ifm.contentWindow.location.href;

          if (currentAttempt >= attempt) {
            clearInterval(pollingInterval);
            reject(false);
          }
          if (ifmPath.includes(path)) {
            clearInterval(pollingInterval);
            resolve(true);
          } else {
            currentAttempt++;
          }
        }

        const pollingInterval = setInterval(checkURL, interval);
      });
    }
    ```

  - 目的: 指定された部分文字列（パス）が期待される URL に含まれるまで、または最大試行回数に達するまで、iframe の URL を定期的にチェックします。この方法は、iframe 内のページが正しく変更されたかどうかを確認するために使用されます。

- `send_selected_text`

  - コードサンプル

    ```javascript
    var send_selected_text = (doc, selector, val) =>
      new Promise((resolve) => {
        if (!val) {
          return;
        }
        var _target = doc.querySelector(selector);
        if (_target) {
          setTimeout(() => {
            Array.from(_target.options).filter((x) =>
              x.textContent.includes(val)
            )[0].selected = true;
            _target.dispatchEvent(new Event("change", { bubbles: true }));
            resolve();
          }, 100);
        } else {
          resolve();
        }
      });
    ```

- 目的: この関数は、val に対応するテキストを検索して、ドロップダウンリストから値を選択し、選択した値を更新するために change イベントをトリガーします。

8. 注意点

- LP の動作を明確に調査して、適切な関数を使用する必要があります。

  - 例として、[pc_post](https://github.com/kero-chan/united-cart-chatbot/blob/aad5ab3bdcc338b62d5a777eb54c89ddacad4a3e/documents/htmlTasks.js#L108-L179) 関数にて、

    - 以下のスクリプトを使用して、すべての要素が確実にロードされるまで待機し、その後データを入力します。

      ```javascript
      await _waitForElement(ifmdoc, "#main_column");
      ```

    - データをフォームに入力した後にサブミットボタンをクリックするため、次のスクリプトを使用して待機します。

      ```javascript
      await new Promise((resolve) => setTimeout(resolve, 500));
      ```

- 上記の関数に加えて、コードを最適化するためにカスタム関数を追加することができます。
