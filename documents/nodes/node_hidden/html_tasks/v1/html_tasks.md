# HTML TASK

## リクエストボディの例

```
input: {
  scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
  input: {
    label: ""
    nodeType: "html_tasks"
    body: {
      settings: [
        {
          task: {
            content: "<input your html script here>",
          },
        },
      ]
    }
  }
}
```

## 結果の例（ボット）

```json
{
  "message": "Successfully",
  "node": {
    "body": {
      "settings": [
        {
          "task": {
            "content": "<input your html script here>"
          }
        }
      ]
    },
    "label": "",
    "nextNodeUid": null,
    "nodeType": "html_tasks"
  }
}
```

## ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: ""
        nodeType: "html_tasks"
        body: {
          settings: [{ task: { content: "<input your html script here>" } }]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

## body:settings:task:content

### JavaScript のサンプル

[サンプル](./sample.js)

### ファイルの作成方法

[JavaScript ファイル作成の原則](./how_to_create_file.md)
