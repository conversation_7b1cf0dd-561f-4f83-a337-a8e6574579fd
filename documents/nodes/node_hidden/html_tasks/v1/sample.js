(function () {
  //  SETTING IFRAME
  const IFRAME_ID = "jimos_iframe";
  const IFRAME_SRC = window.parent.location.href;
  const DEFAULT_IFRAME_STYLE = {
    // テスト用
    // width: "150px",
    // height: "200px",
    // left: "0",
    // position: "fixed",
    // bottom: "0",
    // borderRadius: "10px",
    // margin: "10px",
    // boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px",
    // zIndex: "-999",
    // left: "-9999px",
    // position: "absolute",

    // テスト用
    width: "900px",
    height: "600px",
    left: "0",
    position: "fixed",
    bottom: "0",
    borderRadius: "10px",
    margin: "10px",
    boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px",
    zIndex: "9999",
  };
  //  INIT IFRAME
  init();

  async function init() {
    document.getElementById(IFRAME_ID)?.remove();
    await createIframe();
    await createCv();
  }

  async function createIframe() {
    const iframe = document.createElement("iframe");
    Object.assign(iframe, { id: IFRAME_ID, src: IFRAME_SRC });
    Object.assign(iframe.style, DEFAULT_IFRAME_STYLE);
    document.body.appendChild(iframe);
  }

  //  CREATE CV
  async function createCv() {
    const ifm = document.querySelector("iframe#jimos_iframe");
    var ifmdoc = await pollIframeContentWithLimit(ifm);

    if (ifmdoc) {
      try {
        await main(ifm);
      } catch (error) {
        console.error("Error parsing JSON:", error.message);
      }
    } else {
      console.log("iframe not found");
    }
  }

  // MAIN ACTIVITIES
  async function main(ifm) {
    var ifmdoc = ifm.contentWindow.document;
    try {
      // フォームページへ遷移する
      await first_click(ifm);

      // PCの場合
      var isPcPage = await _waitForExpectedPath("input");
      if (!isPcPage) {
        await parse_lp_error(ifmdoc);
        return;
      }

      console.log("フォーム入力ページに到達したため、処理に入ります");
      console.time("fill_data");
      await pc_post();
      console.timeEnd("fill_data");

      // 確認ページの場合

      var isConfirmPage = await _waitForExpectedPath("confirm");
      if (!isConfirmPage) {
        await parse_lp_error(ifmdoc);
        return;
      }
      console.log("確認ページに到達しました");
      console.time("確認ページ");
      await confirm_page();
      console.timeEnd("確認ページ");

      // 完了ページの場合
      var isThanksPage = await _waitForExpectedPath("finish");
      if (!isThanksPage) {
        await parse_lp_error(ifmdoc);
        return;
      }
      console.log("完了ページに到達しました");
      console.time("thanks_page");
      await thanks_page();
      console.timeEnd("thanks_page");
    } catch (error) {
      console.error("Error parsing JSON:", error.message);
    }
  }

  async function first_click(ifm) {
    ifm.contentWindow.selectShnTmp(4);
    console.log("フォームページへ遷移する関数を呼び出します");
  }

  async function pc_post(ifmdoc) {
    var ifm = document.querySelector("iframe#jimos_iframe");
    var ifmdoc = ifm.contentWindow.document;

    await _waitForElement(ifmdoc, "#main_column");
    console.log("フォーム入力処理に入ります");
    // PCの場合
    console.log("PCのフォーム入力処理に入ります");
    // 色選択
    send_selected_text(ifmdoc, "select[name='arrAttrId']", `v.product`);

    // 名前
    send_keys(ifmdoc, "#name01", `v.sei`);
    send_keys(ifmdoc, "#name02", `v.mei`);
    send_keys(ifmdoc, "#kana01", `v.seifuri`);
    send_keys(ifmdoc, "#kana02", `v.meifuri`);

    // 性別
    var sex = `v.sex`;
    sex = String(`v.sex`);
    if (sex === "男") {
      sex = "2";
    } else {
      sex = "1";
    }

    set_radio_input(ifmdoc, "input[name='sex']", sex);

    // 生年月日
    send_selected_text(ifmdoc, "#birth1", `v.year`);

    var month = String(`v.month`);
    var day = String(`v.day`);

    // 月が1桁の時
    if (month.length == 1) {
      month = "0" + month;
      console.log("一桁なので0を追加する");
    }

    // 日が1桁の時
    if (day.length == 1) {
      day = "0" + day;
      console.log("一桁なので0を追加する");
    }

    send_selected_text(ifmdoc, "#birth2", month);
    send_selected_text(ifmdoc, "#birth3", day);

    // address
    send_keys(ifmdoc, "#zipcode", `v.zipcode`);
    send_keys(ifmdoc, "#addr02", `v.address02`);
    send_keys(ifmdoc, "#addr03", `v.address03`);

    // tel
    send_keys(ifmdoc, "#tel", `v.tel`);

    // email
    send_keys(ifmdoc, "#mail_addr", `v.mail`);

    // お届け希望時間帯
    send_selected_text(
      ifmdoc,
      "select[name='haiso_ksya_tme_sitei_cde']",
      `v.scheduled_time`
    );

    // 最後の確認画面へ進むボタン
    await new Promise((resolve) => setTimeout(resolve, 500));
    ifmdoc.querySelector("#submitBtn").querySelector("a").click();
    console.log("このまま確認画面へのボタンをクリックします");
  }

  async function confirm_page() {
    var ifm = document.querySelector("iframe#jimos_iframe");
    var ifmdoc = ifm.contentWindow.document;

    console.log("確認ページの処理に入ります");
    await _waitForElement(ifmdoc, "#order_conf");

    var confirm_btn = ifmdoc.querySelector("#order_conf");
    // 確認ページを踏んだフラグ
    await confirm_btn.click();
  }

  async function thanks_page(ifmdoc) {
    var ifm = document.querySelector("iframe#jimos_iframe");
    var ifmdoc = ifm.contentWindow.document;
    console.log("完了ページに到達しました");

    await _waitForElement(ifmdoc, ".finish_notice");

    var thanks_text = ifmdoc.querySelector(".finish_notice");
    var textComplete = thanks_text.textContent;

    if (thanks_text.length !== 0) {
      // window.addEventListener("beforeunload", (event) => {
      // console.log("リロード前に注文完了です");
      const data = { message: `${textComplete}`, error: false };
      window.parent.UnicornCartChatbot.handleResponse(data);
      console.log(data);
    } else {
      console.log("処理に失敗しました");
      parse_lp_error(ifmdoc);
    }
  }

  async function parse_lp_error(ifmdoc) {
    console.log("エラー確認中です");
    await _waitForElement(ifmdoc, "#main");
    var alert_errors = ifmdoc.querySelector("#main");
    var mail_alert_errors = ifmdoc.querySelector("#mail_addr_error");
    var tel_alert_errors = ifmdoc.querySelector("#tel_error");

    if (alert_errors && alert_errors.textContent) {
      var textError = alert_errors.textContent;

      if (textError) {
        console.log("エラーあります");
        var data = { message: `${textError}`, error: true };
        window.parent.UnicornCartChatbot.handleResponse(data);
      }
    } else if (mail_alert_errors && mail_alert_errors.textContent) {
      var textError = mail_alert_errors.textContent;
      if (textError) {
        console.log("エラーあります");
        var data = { message: `${textError}`, error: true };
        window.parent.UnicornCartChatbot.handleResponse(data);
      }
    } else if (tel_alert_errors && tel_alert_errors.textContent) {
      var textError = tel_alert_errors.textContent;
      if (textError) {
        console.log("エラーあります");
        var data = { message: `${textError}`, error: true };
        window.parent.UnicornCartChatbot.handleResponse(data);
      }
    } else {
      console.log("エラーありません");
    }
  }

  //  FUNCTION HANDLE

  var send_keys = (doc, selector, val) =>
    new Promise((resolve) => {
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          _target.value = val;
          ["blur", "change", "input"].forEach((e) => {
            _target.dispatchEvent(new Event(e, { bubbles: true }));
          });
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  var send_selected_text = (doc, selector, val) =>
    new Promise((resolve) => {
      if (!val) {
        return;
      }
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          Array.from(_target.options).filter((x) =>
            x.textContent.includes(val)
          )[0].selected = true;
          _target.dispatchEvent(new Event("change", { bubbles: true }));
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  function set_radio_input(doc, selector, value) {
    var divElements = doc.querySelectorAll(selector);
    for (var i = 0; i < divElements.length; i++) {
      var target_radio = divElements[i];
      console.log(target_radio);

      if (target_radio.value == value) {
        target_radio.click();
        break;
      }
    }
  }

  var _waitForElement = function (
    doc,
    selector,
    isMovedPage = false,
    delay = 250,
    tries = 40
  ) {
    const element = doc.querySelector(selector);

    if (!window[`__${selector}`]) {
      window[`__${selector}`] = 0;
    }

    function _search() {
      return new Promise((resolve) => {
        window[`__${selector}`]++;
        setTimeout(resolve, delay);
      });
    }

    if (element === null) {
      if (window[`__${selector}`] >= tries) {
        window[`__${selector}`] = 0;
        return Promise.reject(selector + " is null");
      }
      if (isMovedPage) {
        var reIfm = document.querySelector("iframe#jimos_iframe");
        var reIfmdoc = reIfm.contentDocument;
        return _search().then(() =>
          _waitForElement(reIfmdoc, selector, isMovedPage, delay, tries)
        );
      }
      return _search().then(() =>
        _waitForElement(doc, selector, isMovedPage, delay, tries)
      );
    } else {
      window[`__${selector}`] = 0;
      if (isMovedPage) {
        return Promise.resolve(doc);
      }
      return Promise.resolve(element);
    }
  };

  async function _waitForExpectedPath(path, interval = 100, attempt = 100) {
    return new Promise((resolve, reject) => {
      let currentAttempt = 0;

      function checkURL() {
        var ifm = document.querySelector("iframe#jimos_iframe");
        var ifmPath = ifm.contentWindow.location.href;

        if (currentAttempt >= attempt) {
          clearInterval(pollingInterval);
          reject(false);
        }
        if (ifmPath.includes(path)) {
          clearInterval(pollingInterval);
          resolve(true);
        } else {
          currentAttempt++;
        }
      }

      const pollingInterval = setInterval(checkURL, interval);
    });
  }

  function pollIframeContentWithLimit(
    doc,
    maxAttempts = 10,
    intervalMs = 1000
  ) {
    return new Promise((resolve, reject) => {
      let attempts = 0;

      const interval = setInterval(() => {
        const bodyContent = doc.contentDocument.body?.innerHTML || "";

        if (bodyContent.trim() !== "") {
          clearInterval(interval);
          resolve(doc.contentDocument);
        }

        attempts++;
        console.log("attempts", attempts);
        if (attempts >= maxAttempts) {
          clearInterval(interval);
          reject(
            new Error(
              `Iframe content not available after ${maxAttempts} attempts.`
            )
          );
        }
      }, intervalMs);
    });
  }
})();
