# Tài liệu về Quy trình Tự động Điền Form

## Tổng quan

![image](../../../../assets/flow_diagram.png)

Quy trình này mô tả cách hệ thống tự động điền thông tin vào form đặt hàng trên trang web Orbis, từ trang đích (LP) đến các trang nhập thông tin cá nhân và thanh toán.

## C<PERSON><PERSON> thành phần chính

### Trang đích (LP)

- **Name**: Người dùng nhập tên
- **Postcode, address**: Người dùng nhập mã bưu điện và địa chỉ
- **Password**: Người dùng nhập mật khẩu
- **My Email**: Người dùng nhập email
- ...
- **Submit**: Người dùng nhấn nút gửi

### [HTML Task](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.vi.md)

- Thự<PERSON> thi khi người dùng nhấn nút Submit từ trang đích
- Tạo cookie `ug_ssId` với giá trị từ `UnicornCartChatbot.ssId`
- Chuyển hướng người dùng đến trang nhập thông tin cá nhân của Orbis

### [Trang nhập thông tin cá nhân (/order/personal_input/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.vi.md)

- Kiểm tra xem cookie `ug_ssId` có tồn tại không
- Nếu có, lấy dữ liệu từ API bằng `ug_ssId`
- Hiển thị overlay loading trong quá trình xử lý
- Tự động điền thông tin cá nhân vào form
- Gửi form để chuyển đến trang thanh toán

### [Trang thanh toán (/order/payment/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.vi.md)

- Hiển thị overlay loading trong quá trình xử lý
- Xóa dữ liệu form từ API
- Xóa cookie `ug_ssId`
- Ẩn overlay loading

## Quy trình hoạt động

1. **Trang đích (LP)**:

   - Người dùng điền thông tin cá nhân vào form
   - Khi nhấn nút Submit, HTML Task được kích hoạt

2. **[HTML Task](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.vi.md)**:

   - Lưu `ug_ssId` vào cookie
   - Chuyển hướng đến trang nhập thông tin cá nhân của Orbis

3. **[Trang nhập thông tin cá nhân](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.vi.md)**:

   - Kiểm tra cookie `ug_ssId`
   - Nếu tồn tại, lấy dữ liệu từ API
   - Tự động điền thông tin vào form
   - Gửi form để chuyển đến trang thanh toán
   - Nếu không tồn tại cookie, hiển thị form trống cho người dùng điền thủ công

4. **[Trang thanh toán](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.vi.md)**:
   - Xóa dữ liệu form từ API
   - Xóa cookie `ug_ssId`
   - Hoàn tất quy trình đặt hàng

## Lưu ý kỹ thuật

- Cookie `ug_ssId` được thiết lập cho tên miền `.orbis.co.jp` để có thể truy cập từ tất cả các tên miền phụ
- Script sử dụng fetch API để giao tiếp với backend
- Có các cơ chế xử lý lỗi trong trường hợp không thể lấy dữ liệu từ API
- Overlay loading được hiển thị để cải thiện trải nghiệm người dùng trong quá trình xử lý
