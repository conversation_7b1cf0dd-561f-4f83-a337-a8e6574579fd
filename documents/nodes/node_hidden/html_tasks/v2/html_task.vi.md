# Tài liệu về HTML Task

## Mã mẫu

```js
async function run() {
  document.cookie = `ug_ssId=${UnicornCartChatbot.ssId}; domain=.orbis.co.jp; path=/`;
  window.parent.location.href =
    "https://www.orbis.co.jp/order/transpage/?prdno1=96867&ug_ssId=" +
    UnicornCartChatbot.ssId;
}

run();
```

## Tổng quan

File `html_task.js` được lưu trong node `html_task` và thực hiện các hành động sau:

- Lưu trữ cookie `ug_ssId` trong trình duyệt của người dùng cho tên miền `.orbis.co.jp`.

- Chuyển hướng người dùng đến một URL cụ thể có chứa tham số truy vấn `ug_ssId`.

## Cách sử dụng:

- Sử dụng biến hoặc sự kiện `UnicornCartChatbot.ssId` để lấy ID phiên của người dùng từ chatbot.

- <PERSON><PERSON> trang đích (LP) và URL đích chuyển hướng phải thuộc cùng một tên miền gốc (ví dụ: `www.orbis.co.jp`, `pr.orbis.co.jp`) để cookie có thể truy cập được trên các tên miền phụ.

## Quy trình hoạt động:

1. Khi script được thực thi, nó sẽ tạo một cookie có tên `ug_ssId` với giá trị là ID phiên của người dùng.

2. Cookie này được thiết lập cho tên miền `.orbis.co.jp` và đường dẫn `/`, cho phép nó được truy cập từ bất kỳ trang nào trong tên miền đó.

3. Sau đó, script chuyển hướng người dùng đến trang đặt hàng với mã sản phẩm cụ thể (96867) và truyền ID phiên như một tham số URL.

4. Trang đích sẽ sử dụng ID phiên này để lấy thông tin người dùng và tự động điền vào form đặt hàng.

## Lưu ý kỹ thuật:

- Script này phải được chạy trong ngữ cảnh của trang web có quyền truy cập vào đối tượng `UnicornCartChatbot`.

- Việc thiết lập cookie với tên miền `.orbis.co.jp` (có dấu chấm ở đầu) cho phép cookie được chia sẻ giữa tất cả các tên miền phụ của orbis.co.jp.

- Tham số `path=/` đảm bảo cookie có thể truy cập được từ bất kỳ đường dẫn nào trong tên miền.

- Script sử dụng `window.parent.location.href` để chuyển hướng cửa sổ cha, điều này hữu ích khi script đang chạy trong một iframe.
