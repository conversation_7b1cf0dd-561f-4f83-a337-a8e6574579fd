# Documentation on HTML Task

## Sample Code

```js
async function run() {
  document.cookie = `ug_ssId=${UnicornCartChatbot.ssId}; domain=.orbis.co.jp; path=/`;
  window.parent.location.href =
    "https://www.orbis.co.jp/order/transpage/?prdno1=96867&ug_ssId=" +
    UnicornCartChatbot.ssId;
}

run();
```

## Overview

`html_task.js` file is saved in the `html_task` node and performs the following actions:

- Store the `ug_ssId` cookie in the user's browser for the `.orbis.co.jp` domain.

- Redirect the user to a specific URL containing the `ug_ssId` query parameter.

## How to Use:

- Use the variable or event `UnicornCartChatbot.ssId` to get the user's session ID from the chatbot.

- Both the landing page (LP) and the redirect destination URL must belong to the same root domain (e.g., `www.orbis.co.jp`, `pr.orbis.co.jp`) for the cookie to be accessible across subdomains.

## Workflow:

1. When the script is executed, it creates a cookie named `ug_ssId` with the value of the user's session ID.

2. This cookie is set for the `.orbis.co.jp` domain and the path `/`, allowing it to be accessed from any page within that domain.

3. The script then redirects the user to the order page with a specific product code (96867) and passes the session ID as a URL parameter.

4. The destination page will use this session ID to retrieve user information and automatically fill in the order form.

## Technical Notes:

- This script must be run in the context of a webpage that has access to the `UnicornCartChatbot` object.

- Setting the cookie with the domain `.orbis.co.jp` (with a dot at the beginning) allows the cookie to be shared across all subdomains of orbis.co.jp.

- The `path=/` parameter ensures the cookie is accessible from any path within the domain.

- The script uses `window.parent.location.href` to redirect the parent window, which is useful when the script is running in an iframe.
