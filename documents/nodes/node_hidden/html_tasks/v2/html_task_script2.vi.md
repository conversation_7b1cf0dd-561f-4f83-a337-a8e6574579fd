# Tài liệu về orbis_form.js

## Tổng quan

File orbis_form.js là một script tự động điền thông tin vào form đặt hàng trên trang web Orbis. Script này được thiết kế để hoạt động trên các trang đặt hàng cụ thể và tự động điền thông tin người dùng từ một API. Nó được nhúng trực tiếp vào form của trang đích (LP).

## Chức năng chính

### Khởi tạo form ([initializeForm](../../../../../client/src/automation/orbis.form.js#L2)):

- Kiểm tra đường dẫn hiện tại của trang web
- Đọc `ug_ssId` từ cookie
- Thực hiện các hành động khác nhau tùy thuộc vào đường dẫn

### <PERSON><PERSON> lý trang nhập thông tin cá nhân (/order/personal_input/):

- <PERSON><PERSON><PERSON> dữ liệu từ API bằng `ug_ssId`
- Hiển thị overlay loading trong quá trình xử lý
- Điền thông tin vào form sau khi nhận dữ liệu
- Ẩn overlay loading

### Xử lý trang thanh toán (/order/payment/):

- Hiển thị overlay loading trong quá trình xử lý
- Xóa dữ liệu form từ API
- Xóa cookie `ug_ssId`
- Ẩn overlay loading

### Các hàm tiện ích:

- [parseCookies](../../../../../client/src/automation/orbis.form.js#L72): Phân tích cookie thành đối tượng
- [setInputValue](../../../../../client/src/automation/orbis.form.js#L80): Đặt giá trị cho trường input và kích hoạt sự kiện
- [selectOption](../../../../../client/src/automation/orbis.form.js#L103): Chọn tùy chọn trong dropdown
- [selectRadio](../../../../../client/src/automation/orbis.form.js#L151): Chọn nút radio
- [triggerMouseEvents](../../../../../client/src/automation/orbis.form.js#L173): Kích hoạt các sự kiện chuột
- [waitForElements](../../../../../client/src/automation/orbis.form.js#L185): Đợi các phần tử xuất hiện trên trang
- [toggleCheckboxState](../../../../../client/src/automation/orbis.form.js#L214): Thay đổi trạng thái checkbox

### Điền form (populateForm):

- Điền thông tin cá nhân (họ, tên, furigana)
- Chọn giới tính
- Điền mã bưu điện

## Cách sử dụng

Script này được thiết kế để chạy tự động khi trang web được tải. Nó sẽ:

- Kiểm tra đường dẫn hiện tại
- Nếu đang ở trang nhập thông tin cá nhân và có `ug_ssId`, nó sẽ lấy dữ liệu từ API và điền vào form
- Nếu đang ở trang thanh toán, nó sẽ xóa dữ liệu form từ API và xóa cookie `ug_ssId`

## Lưu ý kỹ thuật

- Script sử dụng fetch API để giao tiếp với backend
- Các sự kiện DOM được kích hoạt để đảm bảo các trình xử lý sự kiện của trang web được gọi
- Có các độ trễ (timeout) để đảm bảo các phần tử đã được tải đầy đủ trước khi tương tác

## API Endpoints

- GET /api/v1/form_prefills/{ug_ssId}: Lấy dữ liệu điền form
- DELETE /api/v1/form_prefills/{ug_ssId}: Xóa dữ liệu điền form
