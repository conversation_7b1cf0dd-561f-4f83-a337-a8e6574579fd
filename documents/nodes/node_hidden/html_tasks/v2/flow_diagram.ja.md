# フォーム自動入力プロセスに関するドキュメント

## 概要

![image](../../../../assets/flow_diagram.png)

このプロセスは、ランディングページ（LP）から個人情報入力ページ、支払いページまで、Orbis ウェブサイトの注文フォームに情報を自動的に入力する方法を説明しています。

## 主要コンポーネント

### ランディングページ（LP）

- **Name**: ユーザーが名前を入力
- **Postcode, address**: ユーザーが郵便番号と住所を入力
- **Password**: ユーザーがパスワードを入力
- **My Email**: ユーザーがメールアドレスを入力
- ...
- **Submit**: ユーザーが送信ボタンをクリック

### [HTML タスク](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.ja.md)

- ランディングページから送信ボタンがクリックされたときに実行
- `UnicornCartChatbot.ssId`の値を持つ`ug_ssId`クッキーを作成
- ユーザーを Orbis の個人情報入力ページにリダイレクト

### [個人情報ページ (/order/personal_input/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.ja.md)

- `ug_ssId`クッキーが存在するかチェック
- 存在する場合、`ug_ssId`を使用して API からデータを取得
- 処理中にローディングオーバーレイを表示
- フォームに個人情報を自動入力
- フォームを送信して支払いページに進む

### [支払いページ (/order/payment/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.ja.md)

- 処理中にローディングオーバーレイを表示
- API からフォームデータを削除
- `ug_ssId`クッキーを削除
- ローディングオーバーレイを非表示

## 操作プロセス

1. **ランディングページ（LP）**:

   - ユーザーがフォームに個人情報を入力
   - 送信ボタンがクリックされると、HTML タスクが起動

2. **[HTML タスク](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.ja.md)**:

   - `ug_ssId`をクッキーに保存
   - Orbis の個人情報入力ページにリダイレクト

3. **[個人情報ページ](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.ja.md)**:

   - `ug_ssId`クッキーをチェック
   - 存在する場合、API からデータを取得
   - フォームに情報を自動入力
   - フォームを送信して支払いページに進む
   - クッキーが存在しない場合、ユーザーが手動で入力するための空のフォームを表示

4. **[支払いページ](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.ja.md)**:
   - API からフォームデータを削除
   - `ug_ssId`クッキーを削除
   - 注文プロセスを完了

## 技術的な注意点

- `ug_ssId`クッキーはすべてのサブドメインからアクセスできるように`.orbis.co.jp`ドメインに設定
- スクリプトはバックエンドと通信するために fetch API を使用
- API からデータを取得できない場合のエラー処理メカニズムがある
- 処理中のユーザー体験を向上させるためにローディングオーバーレイを表示
