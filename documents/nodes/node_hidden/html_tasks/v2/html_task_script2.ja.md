# orbis_form.js に関するドキュメント

## 概要

orbis_form.js ファイルは、Orbis のウェブサイト上の注文フォームに自動的に情報を入力するスクリプトです。このスクリプトは特定の注文ページで動作するように設計されており、API から取得したユーザー情報を自動的に入力します。ランディングページ（LP）のフォームに直接埋め込まれています。

## 主な機能

### フォームの初期化 ([initializeForm](../../../../../client/src/automation/orbis.form.js#L2)):

- ウェブサイトの現在のパスを確認
- <PERSON><PERSON> から`ug_ssId`を読み取る
- パスに応じて異なるアクションを実行

### 個人情報ページの処理 (/order/personal_input/):

- `ug_ssId`を使用して API からデータを取得
- 処理中にローディングオーバーレイを表示
- データ受信後にフォームに入力
- ローディングオーバーレイを非表示

### 支払いページの処理 (/order/payment/):

- 処理中にローディングオーバーレイを表示
- API からフォームデータを削除
- `ug_ssId`クッキーを削除
- ローディングオーバーレイを非表示

### ユーティリティ関数:

- [parseCookies](../../../../../client/src/automation/orbis.form.js#L73): Cookie をオブジェクトに解析
- [setInputValue](../../../../../client/src/automation/orbis.form.js#L82): 入力フィールドに値を設定し、イベントをトリガー
- [selectOption](../../../../../client/src/automation/orbis.form.js#L109): ドロップダウンでオプションを選択
- [selectRadio](../../../../../client/src/automation/orbis.form.js#L153): ラジオボタンを選択
- [triggerMouseEvents](../../../../../client/src/automation/orbis.form.js#L175): マウスイベントをトリガー
- [waitForElements](../../../../../client/src/automation/orbis.form.js#L186): ページ上に要素が表示されるのを待つ
- [toggleCheckboxState](../../../../../client/src/automation/orbis.form.js#L214): チェックボックスの状態を変更

### フォーム入力 ([populateForm](../../../../../client/src/automation/orbis.form.js#L234)):

- 個人情報（姓、名、フリガナ）を入力
- 性別を選択
- 郵便番号と住所を入力
- 連絡先情報（電話、メール）を入力
- パスワードを設定
- 生年月日を選択
- その他のオプションを設定
- 利用規約に同意
- フォームを送信

## 使用方法

このスクリプトはウェブページの読み込み時に自動的に実行されるように設計されています。次のように動作します：

- 現在のパスを確認
- 個人情報入力ページにいて、`ug_ssId`が存在する場合、API からデータを取得してフォームに入力
- 支払いページにいる場合、API からフォームデータを削除し、`ug_ssId`クッキーを削除

## 技術的な注意点

- スクリプトはバックエンドと通信するために fetch API を使用
- ウェブページのイベントハンドラーが呼び出されるように DOM イベントがトリガーされる
- 要素が操作前に完全に読み込まれるようにタイムアウトが設定されている
- スクリプトは API との通信における成功と失敗の両方のケースを処理

## API エンドポイント

- GET `/api/v1/form_prefills/{ug_ssId}`: フォームデータを取得
- DELETE `/api/v1/form_prefills/{ug_ssId}`: フォームデータを削除

## ローディングオーバーレイの表示

スクリプトはローディングオーバーレイを表示および非表示にする関数を提供します：

- [displayLoadingOverlay](../../../../../client/src/automation/orbis.form.js#L350): スピン効果のあるローディングオーバーレイを表示
- [hideLoadingOverlay](../../../../../client/src/automation/orbis.form.js#L380): ローディングオーバーレイを非表示
