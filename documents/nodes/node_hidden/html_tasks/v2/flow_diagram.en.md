# Documentation on Automated Form Filling Process

## Overview

![image](../../../../assets/flow_diagram.png)

This process describes how the system automatically fills in information on the Orbis website order form, from the landing page (LP) to the personal information and payment pages.

## Main Components

### Landing Page (LP)

- **Name**: User enters their name
- **Postcode, address**: User enters postal code and address
- **Password**: User enters password
- **My Email**: User enters email
- ...
- **Submit**: User clicks the submit button

### [HTML Task](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.en.md)

- Executes when the user clicks the Submit button from the landing page
- Creates a cookie `ug_ssId` with the value from `UnicornCartChatbot.ssId`
- Redirects the user to the Orbis personal information input page

### [Personal Information Page (/order/personal_input/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.en.md)

- Checks if the `ug_ssId` cookie exists
- If it exists, retrieves data from the API using the `ug_ssId`
- Displays a loading overlay during processing
- Automatically fills in personal information in the form
- Submits the form to proceed to the payment page

### [Payment Page (/order/payment/)](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.en.md)

- Displays a loading overlay during processing
- Deletes form data from the API
- Deletes the `ug_ssId` cookie
- Hides the loading overlay

## Operational Process

1. **Landing Page (LP)**:

   - User fills in personal information in the form
   - When the Submit button is clicked, the HTML Task is triggered

2. **[HTML Task](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task.en.md)**:

   - Saves the `ug_ssId` to a cookie
   - Redirects to the Orbis personal information input page

3. **[Personal Information Page](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.en.md)**:

   - Checks for the `ug_ssId` cookie
   - If it exists, retrieves data from the API
   - Automatically fills in information in the form
   - Submits the form to proceed to the payment page
   - If the cookie doesn't exist, displays an empty form for the user to fill in manually

4. **[Payment Page](../../../../../documents/nodes/node_hidden/html_tasks/v2/html_task_script2.en.md)**:
   - Deletes form data from the API
   - Deletes the `ug_ssId` cookie
   - Completes the order process

## Technical Notes

- The `ug_ssId` cookie is set for the `.orbis.co.jp` domain to be accessible from all subdomains
- The script uses the fetch API to communicate with the backend
- There are error handling mechanisms in case data cannot be retrieved from the API
- A loading overlay is displayed to improve user experience during processing
