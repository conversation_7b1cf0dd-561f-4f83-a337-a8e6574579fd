# Documentation on orbis_form.js

## Overview

The orbis_form.js file is a script that automatically fills in the order form on the Orbis website. This script is designed to work on specific order pages and automatically populates user information from an API. It is directly embedded into the landing page (LP) form.

## Main Functions

### Form Initialization ([initializeForm](../../../../../client/src/automation/orbis.form.js#L2)):

- Check the current path of the website
- Read the `ug_ssId` from cookies
- Perform different actions depending on the path

### Processing the Personal Information Page (/order/personal_input/):

- Retrieve data from the API using the `ug_ssId`
- Display a loading overlay during processing
- Fill in the form after receiving the data
- Hide the loading overlay

### Processing the Payment Page (/order/payment/):

Display a loading overlay during processing
Delete form data from the API
Delete the `ug_ssId` cookie
Hide the loading overlay

### Utility Functions:

- [parseCookies](../../../../../client/src/automation/orbis.form.js#L73): Parse cookies into an object
- [setInputValue](../../../../../client/src/automation/orbis.form.js#L82): Set values for input fields and trigger events
- [selectOption](../../../../../client/src/automation/orbis.form.js#L109): Select options in dropdowns
- [selectRadio](../../../../../client/src/automation/orbis.form.js#L153): Select radio buttons
- [triggerMouseEvents](../../../../../client/src/automation/orbis.form.js#L175): Trigger mouse events
- [waitForElements](../../../../../client/src/automation/orbis.form.js#L186): Wait for elements to appear on the page
- [toggleCheckboxState](../../../../../client/src/automation/orbis.form.js#L214): Change checkbox states

### Form Filling ([populateForm](../../../../../client/src/automation/orbis.form.js#L234)):

- Fill in personal information (last name, first name, furigana)
- Select gender
- Fill in postal code and address
- Fill in contact information (phone, email)
- Set password
- Select birth date
- Set other options
- Mark agreement with terms
- Submit the form

## How to Use

This script is designed to run automatically when the webpage loads. It will:

- Check the current path
- If on the personal information input page and a `ug_ssId` exists, it will retrieve data from the API and fill in the form
- If on the payment page, it will delete form data from the API and delete the `ug_ssId` cookie

## Technical Notes

- The script uses the fetch API to communicate with the backend
- DOM events are triggered to ensure the webpage's event handlers are called
- There are timeouts to ensure elements are fully loaded before interaction
- The script handles both success and failure cases when communicating with the API

## API Endpoints

- GET `/api/v1/form_prefills/{ug_ssId}`: Retrieve form data
- DELETE `/api/v1/form_prefills/{ug_ssId}`: Delete form data

## Loading Overlay Display

The script provides functions to display and hide a loading overlay:

- [displayLoadingOverlay](../../../../../client/src/automation/orbis.form.js#L350): Display a loading overlay with a spinning effect
- [hideLoadingOverlay](../../../../../client/src/automation/orbis.form.js#L380): Hide the loading overlay
