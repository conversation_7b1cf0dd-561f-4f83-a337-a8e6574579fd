# HTML タスクに関するドキュメント

## サンプルコード

```js
async function run() {
  document.cookie = `ug_ssId=${UnicornCartChatbot.ssId}; domain=.orbis.co.jp; path=/`;
  window.parent.location.href =
    "https://www.orbis.co.jp/order/transpage/?prdno1=96867&ug_ssId=" +
    UnicornCartChatbot.ssId;
}

run();
```

## 概要

`html_task.js`ファイルは`html_task`ノードに保存されており、以下の処理を実行します：

- ユーザーのブラウザに`.orbis.co.jp`ドメイン用の`ug_ssId`クッキーを保存します。

- ユーザーを`ug_ssId`クエリパラメータを含む特定の URL にリダイレクトします。

## 使用方法:

- チャットボットからユーザーのセッション ID を取得するために、変数またはイベント`UnicornCartChatbot.ssId`を使用します。

- クッキーがサブドメイン間でアクセス可能であるためには、ランディングページ（LP）とリダイレクト先の URL が同じルートドメイン（例：`www.orbis.co.jp`、`pr.orbis.co.jp`）に属している必要があります。

## ワークフロー:

1. スクリプトが実行されると、ユーザーのセッション ID の値を持つ`ug_ssId`という名前のクッキーが作成されます。

2. このクッキーは`.orbis.co.jp`ドメインとパス`/`に設定され、そのドメイン内のどのページからもアクセスできるようになります。

3. その後、スクリプトはユーザーを特定の商品コード（96867）を持つ注文ページにリダイレクトし、セッション ID を URL パラメータとして渡します。

4. 目的のページはこのセッション ID を使用してユーザー情報を取得し、注文フォームに自動的に入力します。

## 技術的な注意点:

- このスクリプトは`UnicornCartChatbot`オブジェクトにアクセスできるウェブページのコンテキストで実行する必要があります。

- ドメインを`.orbis.co.jp`（先頭にドットがある）に設定することで、クッキーが orbis.co.jp のすべてのサブドメイン間で共有できるようになります。

- `path=/`パラメータは、ドメイン内のどのパスからもクッキーにアクセスできるようにします。

- スクリプトは`window.parent.location.href`を使用して親ウィンドウをリダイレクトします。これはスクリプトが iframe 内で実行されている場合に便利です。
