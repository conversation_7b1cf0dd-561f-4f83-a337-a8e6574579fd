# フォルダ構成

```bash
apis/
├── modules                             # モジュールの詳細情報を格納するフォルダ
    ├── message.md                      # メッセージモジュール
    ├── button.md                       # ボタンモジュール
    ├── name                            # 名前モジュール
    ├── password.md                     # パスワードモジュール
    ├── text.md                         # テキストモジュール
    ├── select.md                       # セレクトモジュール
    ├── address.md                      # 住所モジュール
    ├── sex_and_birthday.md             # 性別と生年月日モジュール
    ├── credit_card.md                  # クレジットカードモジュール
    ├── radio_button.md                 # ラジオボタンモジュール
    ├── radio_button_reselectable.md    # 再選択可能なラジオボタンモジュール
    ├── tel_email_password.md           # 電話番号、メールアドレス、パスワードモジュール
    ├── name_sex_birthday               # 名前、性別、生年月日モジュール
    ├── scheduled_delivery.md           # 定期配達モジュール
    ├── checkbox.md                     # チェックボックスモジュール
    ├── full_name.md                    # フルネームモジュール
├── node_hidden                         # 非表示のノードに関する詳細情報
    ├── condition.md                    # 条件
    ├── html_tasks
        ├── v1                            # HTMLタスクに関するフォルダ
            ├── how_to_create_file.md   # HTMLタスクの説明
            ├── html_tasks.md           # HTMLタスクの説明
        ├── v2                            # HTMLタスクに関するフォルダ
            ├── flow_diagram.en.md      # HTMLタスクのフロー図
            ├── flow_diagram.ja.md      # HTMLタスクのフロー図
            ├── flow_diagram.vi.md      # HTMLタスクのフロー図
            ├── html_tasks.ja.md        # HTMLタスクの説明
            ├── html_tasks.vi.md        # HTMLタスクの説明
            ├── html_tasks.en.md        # HTMLタスクの説明
            ├── html_task_script2.ja.md          # 新しいフローの説明
            ├── html_task_script2.vi.md          # 新しいフローの説明
            ├── html_task_script2.en.md          # 新しいフローの説明
    ├── headless_tasks                  # ヘッドレスタスクに関するフォルダ
        ├── headless_tasks.md           # ヘッドレスタスクの説明
        ├── miraiSubmit.md              # Miraiへの送信タスク
    ├── set_value.md                    # 値設定モジュール
├── node_detail.md                      # ノードに関する詳細情報
├── node.md                             # プロジェクト内のすべてのノードの種類
└── README.md                           # プロジェクトの概要とガイド


```
