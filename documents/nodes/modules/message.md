## Messages

## Image

### Create Message Image

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "test create"
        nodeType: "message"
        body: {
          settings: [
            {
              type: "image"
              content: "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### Update Message Image

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: { label: "Test Message" }
      nodeId: "232f00c6-74f8-4315-91bf-1c16b30e51cf"
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
  }
}
```

### Destroy Message Image

```graphql
mutation AdminsS<PERSON>nar<PERSON>Nodes<PERSON><PERSON>roy {
  adminsScenariosNodes<PERSON><PERSON><PERSON>(
    input: {
      nodeId: "47553164-f6fc-4c13-9c91-d1cb67ff54fa"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
    }
  ) {
    message
  }
}
```

## Text

### Create Message Text

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: ""
        nodeType: "message"
        body: {
          settings: [
            {
              type: "text"
              content: "それでは受付を開始しますね。 まずはお客様の電話番号とメールアドレスを入力してください。"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

## Video

### Create Message Video

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: ""
        nodeType: "message"
        body: {
          settings: [
            {
              type: "video"
              content: "https://www.youtube.com/watch?v=AvGBB367W38&list=RD6q3gd47tTVw&index=23"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

## Images

### Create Message Images

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "d4a03ca4-8ad4-4f12-b4ab-b5a996a63d8b"
      input: {
        label: "test images"
        nodeType: "message"
        body: {
          settings: [
            {
              type: "images"
              content: "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png"
            }
            {
              type: "images"
              content: "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png"
            }
            {
              type: "images"
              content: "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### Update Message Images

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      nodeId: "bfef70c7-e289-4670-b39b-b268622dc02a"
      input: { label: "test" }
      scenarioId: "d4a03ca4-8ad4-4f12-b4ab-b5a996a63d8b"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

### Destroy Message Images

```graphql
mutation adminsScenariosNodesDestroy {
  adminsScenariosNodesDestroy(
    input: {
      nodeId: "bfef70c7-e289-4670-b39b-b268622dc02a"
      scenarioId: "d4a03ca4-8ad4-4f12-b4ab-b5a996a63d8b"
    }
  ) {
    message
  }
}
```
