## Information

### 1. UI Node

- UI は `support_ui_enable` が `false` のときに表示されます。

  ![Image](../../assets/information/information.png)

- UI はフラグ `support_ui_enable` が `true` のときに表示されます。

  ![Image](../../assets/information/information_support_ui.png)

### 2. `body:settings` JSON データのサンプル

```json
[
  {
    "variable": "full_name",
    "settings": [
      {
        "label": "お名前",
        "settings": [
          {
            "label": "姓",
            "variable": "sei",
            "required": true,
            "placeholder": "山田"
          },
          {
            "label": "名",
            "variable": "mei",
            "required": true,
            "placeholder": "太郎"
          }
        ]
      },
      {
        "label": "フリガナ",
        "settings": [
          {
            "label": "セイ",
            "variable": "seifuri",
            "required": true,
            "placeholder": "ヤマダ"
          },
          {
            "label": "メイ",
            "variable": "meifuri",
            "required": true,
            "placeholder": "タロウ"
          }
        ]
      }
    ]
  },
  {
    "label": "性別",
    "variable": "sex",
    "required": false
  },
  {
    "variable": "birthday",
    "label": "生年月日",
    "settings": [
      {
        "label": "日",
        "variable": "day",
        "required": false
      },
      {
        "label": "月",
        "variable": "month",
        "required": false
      },
      {
        "label": "年",
        "variable": "year",
        "required": false
      }
    ]
  },
  {
    "label": "住所",
    "variable": "address",
    "settings": [
      {
        "label": "郵便番号",
        "variable": "zipcode",
        "required": true,
        "placeholder": "1300000"
      },
      {
        "label": "都道府県名",
        "variable": "prefectures",
        "required": true
      },
      {
        "label": "市区町村名",
        "variable": "address01",
        "required": true,
        "placeholder": "市区町村名 (千代田区神田神保町)"
      },
      {
        "label": "丁目-番地-号",
        "variable": "address02",
        "required": true,
        "placeholder": "例：２０−１２３−３"
      },
      {
        "label": "建物名・号室 (任意)",
        "variable": "address03",
        "required": true
      }
    ]
  },
  {
    "label": "電話番号",
    "variable": "tel",
    "required": true,
    "placeholder": "例）09011112222"
  },
  {
    "label": "メールアドレス",
    "variable": "mail",
    "required": true,
    "placeholder": "例）<EMAIL>"
  }
]
```

### 3. API Create

- ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
      input: {
        nodeType: "input"
        body: {
          type: "information"
          settings: [
            {
              variable: "full_name"
              settings: [
                {
                  label: "お名前"
                  settings: [
                    {
                      label: "姓"
                      variable: "sei"
                      required: true
                      placeholder: "山田"
                    }
                    {
                      label: "名"
                      variable: "mei"
                      required: true
                      placeholder: "太郎"
                    }
                  ]
                }
                {
                  label: "フリガナ"
                  settings: [
                    {
                      label: "セイ"
                      variable: "seifuri"
                      required: true
                      placeholder: "ヤマダ"
                    }
                    {
                      label: "メイ"
                      variable: "meifuri"
                      required: true
                      placeholder: "タロウ"
                    }
                  ]
                }
              ]
            }
            { label: "性別", variable: "sex", required: false }
            {
              variable: "birthday"
              label: "生年月日"
              settings: [
                { label: "日", variable: "day", required: false }
                { label: "月", variable: "month", required: false }
                { label: "年", variable: "year", required: false }
              ]
            }
            {
              label: "住所"
              variable: "address"
              settings: [
                {
                  label: "郵便番号"
                  variable: "zipcode"
                  required: true
                  placeholder: "1300000"
                }
                { label: "都道府県名", variable: "prefectures", required: true }
                {
                  label: "市区町村名"
                  variable: "address01"
                  required: true
                  placeholder: "市区町村名 (千代田区神田神保町)"
                }
                {
                  label: "丁目-番地-号"
                  variable: "address02"
                  required: true
                  placeholder: "例：２０−１２３−３"
                }
                {
                  label: "建物名・号室 (任意)"
                  variable: "address03"
                  required: true
                }
              ]
            }
            {
              label: "メールアドレス"
              variable: "mail"
              required: true
              placeholder: "例）<EMAIL>"
            }
            {
              label: "電話番号"
              variable: "tel"
              required: true
              placeholder: "例）09011112222"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- リクエストボディの例

```
input: {
    scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
    input: {
    nodeType: "input"
    body: {
        type: "information"
        settings: [
        {
            variable: "full_name"
            settings: [
            {
                label: "お名前"
                settings: [
                {
                    label: "姓"
                    variable: "sei"
                    required: true
                    placeholder: "山田"
                }
                {
                    label: "名"
                    variable: "mei"
                    required: true
                    placeholder: "太郎"
                }
                ]
            }
            {
                label: "フリガナ"
                settings: [
                {
                    label: "セイ"
                    variable: "seifuri"
                    required: true
                    placeholder: "ヤマダ"
                }
                {
                    label: "メイ"
                    variable: "meifuri"
                    required: true
                    placeholder: "タロウ"
                }
                ]
            }
            ]
        }
        { label: "性別", variable: "sex", required: false }
        {
            variable: "birthday"
            label: "生年月日"
            settings: [
            { label: "日", variable: "day", required: false }
            { label: "月", variable: "month", required: false }
            { label: "年", variable: "year", required: false }
            ]
        }
        {
            label: "住所"
            variable: "address"
            settings: [
            {
                label: "郵便番号"
                variable: "zipcode"
                required: true
                placeholder: "1300000"
            }
            { label: "都道府県名", variable: "prefectures", required: true }
            {
                label: "市区町村名"
                variable: "address01"
                required: true
                placeholder: "市区町村名 (千代田区神田神保町)"
            }
            {
                label: "丁目-番地-号"
                variable: "address02"
                required: true
                placeholder: "例：２０−１２３−３"
            }
            {
                label: "建物名・号室 (任意)"
                variable: "address03"
                required: true
            }
            ]
        }
        {
            label: "メールアドレス"
            variable: "mail"
            required: true
            placeholder: "例）<EMAIL>"
        }
        {
            label: "電話番号"
            variable: "tel"
            required: true
            placeholder: "例）09011112222"
        }
        ]
    }
    }
}
```

- 結果の例

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "information",
          "settings": [
            {
              "variable": "full_name",
              "settings": [
                {
                  "label": "お名前",
                  "settings": [
                    {
                      "label": "姓",
                      "variable": "sei",
                      "required": true,
                      "placeholder": "山田"
                    },
                    {
                      "label": "名",
                      "variable": "mei",
                      "required": true,
                      "placeholder": "太郎"
                    }
                  ]
                },
                {
                  "label": "フリガナ",
                  "settings": [
                    {
                      "label": "セイ",
                      "variable": "seifuri",
                      "required": true,
                      "placeholder": "ヤマダ"
                    },
                    {
                      "label": "メイ",
                      "variable": "meifuri",
                      "required": true,
                      "placeholder": "タロウ"
                    }
                  ]
                }
              ]
            },
            {
              "label": "性別",
              "variable": "sex",
              "required": false
            },
            {
              "variable": "birthday",
              "label": "生年月日",
              "settings": [
                {
                  "label": "日",
                  "variable": "day",
                  "required": false
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": false
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": false
                }
              ]
            },
            {
              "label": "住所",
              "variable": "address",
              "settings": [
                {
                  "label": "郵便番号",
                  "variable": "zipcode",
                  "required": true,
                  "placeholder": "1300000"
                },
                {
                  "label": "都道府県名",
                  "variable": "prefectures",
                  "required": true
                },
                {
                  "label": "市区町村名",
                  "variable": "address01",
                  "required": true,
                  "placeholder": "市区町村名 (千代田区神田神保町)"
                },
                {
                  "label": "丁目-番地-号",
                  "variable": "address02",
                  "required": true,
                  "placeholder": "例：２０−１２３−３"
                },
                {
                  "label": "建物名・号室 (任意)",
                  "variable": "address03",
                  "required": true
                }
              ]
            },
            {
              "label": "メールアドレス",
              "variable": "mail",
              "required": true,
              "placeholder": "例）<EMAIL>"
            },
            {
              "label": "電話番号",
              "variable": "tel",
              "required": true,
              "placeholder": "例）09011112222"
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "aec14d42-deb7-40ff-96cf-f5ddf4914463"
      }
    }
  }
}
```

### 4. Api Update

- ミューテーションによる更新

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
      input: {
        nodeType: "input"
        body: {
          type: "information"
          settings: [
            {
              variable: "full_name"
              settings: [
                {
                  label: "お名前"
                  settings: [
                    {
                      label: "姓"
                      variable: "sei"
                      required: true
                      placeholder: "山田"
                    }
                    {
                      label: "名"
                      variable: "mei"
                      required: true
                      placeholder: "太郎"
                    }
                  ]
                }
                {
                  label: "フリガナ"
                  settings: [
                    {
                      label: "セイ"
                      variable: "seifuri"
                      required: true
                      placeholder: "ヤマダ"
                    }
                    {
                      label: "メイ"
                      variable: "meifuri"
                      required: true
                      placeholder: "タロウ"
                    }
                  ]
                }
              ]
            }
            { label: "性別", variable: "sex", required: false }
            {
              variable: "birthday"
              label: "生年月日"
              settings: [
                { label: "日", variable: "day", required: false }
                { label: "月", variable: "month", required: false }
                { label: "年", variable: "year", required: false }
              ]
            }
            {
              label: "住所"
              variable: "address"
              settings: [
                {
                  label: "郵便番号"
                  variable: "zipcode"
                  required: true
                  placeholder: "1300000"
                }
                { label: "都道府県名", variable: "prefectures", required: true }
                {
                  label: "市区町村名"
                  variable: "address01"
                  required: true
                  placeholder: "市区町村名 (千代田区神田神保町)"
                }
                {
                  label: "丁目-番地-号"
                  variable: "address02"
                  required: true
                  placeholder: "例：２０−１２３−３"
                }
                {
                  label: "建物名・号室 (任意)"
                  variable: "address03"
                  required: true
                }
              ]
            }
            {
              label: "メールアドレス"
              variable: "mail"
              required: true
              placeholder: "例）<EMAIL>"
            }
            {
              label: "電話番号"
              variable: "tel"
              required: true
              placeholder: "例）09011112222"
            }
          ]
        }
      }
      nodeId: "1555226a-97dd-4cec-8031-354364374c75"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- リクエストボディの例

```
input: {
    scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
    input: {
    nodeType: "input"
    body: {
        type: "information"
        settings: [
        {
            variable: "full_name"
            settings: [
            {
                label: "お名前"
                settings: [
                {
                    label: "姓"
                    variable: "sei"
                    required: true
                    placeholder: "山田"
                }
                {
                    label: "名"
                    variable: "mei"
                    required: true
                    placeholder: "太郎"
                }
                ]
            }
            {
                label: "フリガナ"
                settings: [
                {
                    label: "セイ"
                    variable: "seifuri"
                    required: true
                    placeholder: "ヤマダ"
                }
                {
                    label: "メイ"
                    variable: "meifuri"
                    required: true
                    placeholder: "タロウ"
                }
                ]
            }
            ]
        }
        { label: "性別", variable: "sex", required: false }
        {
            variable: "birthday"
            label: "生年月日"
            settings: [
            { label: "日", variable: "day", required: false }
            { label: "月", variable: "month", required: false }
            { label: "年", variable: "year", required: false }
            ]
        }
        {
            label: "住所"
            variable: "address"
            settings: [
            {
                label: "郵便番号"
                variable: "zipcode"
                required: true
                placeholder: "1300000"
            }
            { label: "都道府県名", variable: "prefectures", required: true }
            {
                label: "市区町村名"
                variable: "address01"
                required: true
                placeholder: "市区町村名 (千代田区神田神保町)"
            }
            {
                label: "丁目-番地-号"
                variable: "address02"
                required: true
                placeholder: "例：２０−１２３−３"
            }
            {
                label: "建物名・号室 (任意)"
                variable: "address03"
                required: true
            }
            ]
        }
        {
            label: "メールアドレス"
            variable: "mail"
            required: true
            placeholder: "例）<EMAIL>"
        }
        {
            label: "電話番号"
            variable: "tel"
            required: true
            placeholder: "例）09011112222"
        }
        ]
    }
    }
    nodeId: "1555226a-97dd-4cec-8031-354364374c75"
}
```

- 結果の例

```json
{
  "data": {
    "adminsScenariosNodesUpdate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "information",
          "settings": [
            {
              "variable": "full_name",
              "settings": [
                {
                  "label": "お名前",
                  "settings": [
                    {
                      "label": "姓",
                      "variable": "sei",
                      "required": true,
                      "placeholder": "山田"
                    },
                    {
                      "label": "名",
                      "variable": "mei",
                      "required": true,
                      "placeholder": "太郎"
                    }
                  ]
                },
                {
                  "label": "フリガナ",
                  "settings": [
                    {
                      "label": "セイ",
                      "variable": "seifuri",
                      "required": true,
                      "placeholder": "ヤマダ"
                    },
                    {
                      "label": "メイ",
                      "variable": "meifuri",
                      "required": true,
                      "placeholder": "タロウ"
                    }
                  ]
                }
              ]
            },
            {
              "label": "性別",
              "variable": "sex",
              "required": false
            },
            {
              "variable": "birthday",
              "label": "生年月日",
              "settings": [
                {
                  "label": "日",
                  "variable": "day",
                  "required": false
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": false
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": false
                }
              ]
            },
            {
              "label": "住所",
              "variable": "address",
              "settings": [
                {
                  "label": "郵便番号",
                  "variable": "zipcode",
                  "required": true,
                  "placeholder": "1300000"
                },
                {
                  "label": "都道府県名",
                  "variable": "prefectures",
                  "required": true
                },
                {
                  "label": "市区町村名",
                  "variable": "address01",
                  "required": true,
                  "placeholder": "市区町村名 (千代田区神田神保町)"
                },
                {
                  "label": "丁目-番地-号",
                  "variable": "address02",
                  "required": true,
                  "placeholder": "例：２０−１２３−３"
                },
                {
                  "label": "建物名・号室 (任意)",
                  "variable": "address03",
                  "required": true
                }
              ]
            },
            {
              "label": "メールアドレス",
              "variable": "mail",
              "required": true,
              "placeholder": "例）<EMAIL>"
            },
            {
              "label": "電話番号",
              "variable": "tel",
              "required": true,
              "placeholder": "例）09011112222"
            }
          ]
        },
        "label": "",
        "nextNodeUid": ["fd6df999-256a-4338-8412-529ffbe9dbce"],
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "1555226a-97dd-4cec-8031-354364374c75"
      }
    }
  }
}
```
