## Text

![Image](../../assets/text.png)

### リクエストボディの例

```json
{
  "input": {
    "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
    "input": {
      "label": "クーポン",
      "nodeType": "input",
      "body": {
        "type": "text",
        "settings": [
          {
            "label": "クーポンコード",
            "variable": "coupon",
            "type": "input",
            "required": true,
            "placeholder": "クーポンを入力"
          }
        ]
      }
    }
  }
}
```

### 結果の例（ボット）

```json
{
  "message": "Successfully",
  "node": {
    "body": {
      "type": "text",
      "settings": [
        {
          "label": "クーポンコード",
          "variable": "coupon",
          "type": "input",
          "required": true,
          "placeholder": "クーポンを入力"
        }
      ]
    },
    "label": "クーポン",
    "nextNodeUid": null,
    "nodeType": "input"
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: "クーポン"
        nodeType: "input"
        body: {
          type: "text"
          settings: [
            {
              label: "クーポンコード"
              variable: "coupon"
              type: "input"
              required: true
              placeholder: "クーポンを入力"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
