## Node Payment Method

### API Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "payment_method"
          settings: [
            {
              payment_method: {
                label: "支払方法"
                options: [
                  { text: "代金引換" }
                  { text: "Paygentクレジットカード一括" }
                ]
              }
              credit_card_form: {
                show_on: ["Paygentクレジットカード一括"]
                cvv: {
                  enabled: true
                  required: true
                  label: "セキュリティコード"
                  placeholder: "123"
                }
                brand: {
                  label: "カードブランド"
                  enabled: true
                  options: [
                    { text: "Visa" }
                    { text: "MasterCard" }
                    { text: "American Express" }
                  ]
                }
                name: {
                  label: "カード名義(ローマ字氏名)"
                  placeholder: "YAMADA HANAKO"
                  enabled: true
                  required: true
                }
                number: { label: "カード番号", placeholder: "****************" }
                expired: {
                  label: "有効期限"
                  month: { label: "月" }
                  year: { label: "年" }
                }
              }
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      scenarioId: "YOUR_SCENARIO_ID"
    }
  )
}
```

### Description

The Payment Method node combines payment method selection and credit card form in one node. It provides:

- Radio button selection for payment methods
- Conditional credit card form that appears only when credit card payment is selected

### Features

1. Payment Method Selection

   - Type: Radio buttons
   - Options can be customized (e.g., COD, Credit Card)
   - Variable name: `payment_method`

2. Credit Card Form
   - Only shows when specified payment method is selected
   - Configurable through `show_on` array
   - Includes all standard credit card fields:
     - Card number
     - Expiration date (month/year)
     - CVV
     - Card holder name
     - Card brand selection

### Configuration Options

- `payment_method`: Configure payment method options

  - `label`: Label for payment method selection (optional, if not provided, no label will be displayed)
  - `options`: Array of available payment methods

- `credit_card_form`: Credit card form settings
  - `show_on`: Array of payment method values that trigger form display
  - The credit card form will always display two fields: `カード番号` (Card Number) and `有効期限` (Expiration Date)
  - Optional fields can be configured:
    - `cvv`: Security code field settings
      - `enabled`: Whether to show the field
      - `required`: Whether the field is mandatory
      - `label`: Field label
      - `placeholder`: Input placeholder
    - `brand`: Card brand selection settings
      - `enabled`: Whether to show the field
      - `label`: Field label
      - `options`: Array of available card brands
    - `name`: Card holder name field settings
      - `enabled`: Whether to show the field
      - `required`: Whether the field is mandatory
      - `label`: Field label
      - `placeholder`: Input placeholder
    - `number`: Card number field settings (always shown)
      - `label`: Field label
      - `placeholder`: Input placeholder
    - `expired`: Expiration date fields settings (always shown)
      - `label`: Group label
      - `month`: Month field settings
        - `label`: Month field label
      - `year`: Year field settings
        - `label`: Year field label

### Field Properties

Note: For optional fields (cvv, brand, name):

- If `enabled` is false or not specified, the field will be hidden
- If `enabled` is false, `required` must also be false or omitted
- All fields support customization of labels and placeholders

### Sample UI

1. Initial View - Payment Method Selection

![selection](../../assets/payment_method/selection.png)

```json
{
  "type": "payment_method",
  "settings": [
    {
      "payment_method": {
        "label": "payment method",
        "options": [
          { "text": "代金引換" },
          { "text": "Paygentクレジットカード一括" }
        ]
      }
    }
  ]
}
```

2. Credit Card Form View (when credit card is selected)

![formCreditCard](../../assets/payment_method/form_credit_card.png)

```json
{
  "type": "payment_method",
  "settings": [
    {
      "payment_method": {
        "label": "payment method",
        "options": [
          { "text": "代金引換" },
          { "text": "Paygentクレジットカード一括" }
        ]
      },
      "credit_card_form": {
        "show_on": ["Paygentクレジットカード一括"],
        "cvv": {
          "enabled": true,
          "required": true,
          "label": "セキュリティコード",
          "placeholder": "123"
        },
        "brand": {
          "label": "カードブランド",
          "enabled": true,
          "options": [
            { "text": "Visa" },
            { "text": "MasterCard" },
            { "text": "American Express" }
          ]
        },
        "name": {
          "label": "カード名義(ローマ字氏名)",
          "placeholder": "YAMADA HANAKO",
          "enabled": true,
          "required": true
        },
        "number": {
          "label": "カード番号",
          "placeholder": "****************"
        },
        "expired": {
          "label": "有効期限",
          "month": { "label": "月" },
          "year": { "label": "年" }
        }
      }
    }
  ]
}
```

### Example result ( bot )

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "payment_method",
          "settings": [
            {
              "payment_method": {
                "label": "支払方法",
                "options": [
                  {
                    "text": "代金引換"
                  },
                  {
                    "text": "Paygentクレジットカード一括"
                  }
                ]
              },
              "credit_card_form": {
                "show_on": ["Paygentクレジットカード一括"],
                "cvv": {
                  "enabled": true,
                  "required": true,
                  "label": "セキュリティコード",
                  "placeholder": "123"
                },
                "brand": {
                  "label": "カードブランド",
                  "enabled": true,
                  "options": [
                    {
                      "text": "Visa"
                    },
                    {
                      "text": "MasterCard"
                    },
                    {
                      "text": "American Express"
                    }
                  ]
                },
                "name": {
                  "label": "カード名義(ローマ字氏名)",
                  "placeholder": "YAMADA HANAKO",
                  "enabled": true,
                  "required": true
                },
                "number": {
                  "label": "カード番号",
                  "placeholder": "****************"
                },
                "expired": {
                  "label": "有効期限",
                  "month": {
                    "label": "月"
                  },
                  "year": {
                    "label": "年"
                  }
                }
              }
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "5961401d-d7fd-4e3c-8028-3d94f90a20fb"
      }
    }
  }
}
```

### CSS Customization

You can customize the CSS using these ID selectors:

- Payment Method:
  - `#payment_method`
- Credit Card Form:
  - `#card_expired_month`
  - `#card_expired_year`
  - `#card_name`
  - `#card_cvv`
  - `#card_number`
  - `#card_brand`
