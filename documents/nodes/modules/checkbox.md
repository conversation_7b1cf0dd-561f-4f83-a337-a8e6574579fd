## CheckBox

![CheckBox](../../assets/checkbox.png)

### リクエストボディの例

```json
{
  "input": {
    "label": "テスト",
    "nodeType": "input",
    "body": {
      "type": "checkbox",
      "settings": [
        {
          "variable": "agreement1",
          "type": "checkbox",
          "required": false,
          "label": "ご優待等、お得な情報の受取に同意。"
        },
        {
          "variable": "agreement2",
          "type": "checkbox",
          "required": true,
          "label": "未成年者である場合は法定代理人の同意を得ています。<a href=\"https://shop.rmh.co.jp/info/customer_term\" target=\"_blank\">利用規約</a>に同意。<a href=\"https://yomite.co.jp/tokushoho/\" target=\"_blank\">特定商取引法に基づく表示</a>を確認しました。"
        }
      ]
    },
    "rootNode": true
  }
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "checkbox",
          "settings": [
            {
              "variable": "agreement1",
              "type": "checkbox",
              "required": false,
              "label": "ご優待等、お得な情報の受取に同意。"
            },
            {
              "variable": "agreement2",
              "type": "checkbox",
              "required": true,
              "label": "未成年者である場合は法定代理人の同意を得ています。<a href=\"https://shop.rmh.co.jp/info/customer_term\" target=\"_blank\">利用規約</a>に同意。<a href=\"https://yomite.co.jp/tokushoho/\" target=\"_blank\">特定商取引法に基づく表示</a>を確認しました。"
            }
          ]
        },
        "label": "テスト",
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": true,
        "uid": "e08c589b-15cd-4595-ad95-9954bc393631"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "f5604da1-3244-402a-bac7-7998357bb4e0"
      input: {
        label: "テスト"
        nodeType: "input"
        body: {
          type: "checkbox"
          settings: [
            {
              variable: "agreement1"
              type: "checkbox"
              required: false
              label: "ご優待等、お得な情報の受取に同意。"
            }
            {
              variable: "agreement2"
              type: "checkbox"
              required: true
              label:
               "未成年者である場合は法定代理人の同意を得ています。<a href="https://shop.rmh.co.jp/info/customer_term" target="_blank">利用規約</a>に同意。<a href="https://yomite.co.jp/tokushoho/" target="_blank">特定商取引法に基づく表示</a>を確認しました。"
            }
          ]
        }
        rootNode: true
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```
