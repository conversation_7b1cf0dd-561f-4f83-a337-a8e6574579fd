## NameBirthdayTelEmail

### 1. UI Node

- `support_ui_enable` is `false`

  ![Image](../../assets/name_birthday_tel_email/name_birthday_tel_email.png)

- `support_ui_enable` is `true`

  ![Image](../../assets/name_birthday_tel_email/name_birthday_tel_email_support_ui.png)

### 2. `body:settings` sample

```json
[
  {
    "label": "お名前",
    "settings": [
      {
        "label": "姓",
        "variable": "sei",
        "required": true,
        "placeholder": "山田"
      },
      {
        "label": "名",
        "variable": "mei",
        "required": true,
        "placeholder": "太郎"
      }
    ]
  },
  {
    "label": "フリガナ",
    "settings": [
      {
        "label": "セイ",
        "variable": "seifuri",
        "required": true,
        "placeholder": "ヤマダ"
      },
      {
        "label": "メイ",
        "variable": "meifuri",
        "required": true,
        "placeholder": "タロウ"
      }
    ]
  },
  {
    "label": "生年月日",
    "settings": [
      {
        "label": "月",
        "variable": "day",
        "required": true
      },
      {
        "label": "月",
        "variable": "month",
        "required": true
      },
      {
        "label": "年",
        "variable": "year",
        "required": true
      }
    ]
  },
  {
    "label": "性別",
    "variable": "tel",
    "required": true,
    "placeholder": "09012345678"
  },
  {
    "label": "性別",
    "variable": "mail",
    "required": true,
    "placeholder": "例）<EMAIL>"
  }
]
```

### 3. API Create

- Mutation

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "name_birthday_tel_email"
          settings: [
            {
              label: "お名前"
              settings: [
                {
                  label: "姓"
                  variable: "sei"
                  required: true
                  placeholder: "山田"
                }
                {
                  label: "名"
                  variable: "mei"
                  required: true
                  placeholder: "太郎"
                }
              ]
            }
            {
              label: "フリガナ"
              settings: [
                {
                  label: "セイ"
                  variable: "seifuri"
                  required: true
                  placeholder: "ヤマダ"
                }
                {
                  label: "メイ"
                  variable: "meifuri"
                  required: true
                  placeholder: "タロウ"
                }
              ]
            }
            {
              label: "生年月日"
              settings: [
                { label: "月", variable: "day", required: true }
                { label: "月", variable: "month", required: true }
                { label: "年", variable: "year", required: true }
              ]
            }
            {
              label: "性別"
              variable: "tel"
              required: true
              placeholder: "09012345678"
            }
            {
              label: "性別"
              variable: "mail"
              required: true
              placeholder: "例）<EMAIL>"
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- Input

```
{
  input: {
      body: {
          type: "name_birthday_tel_email"
          settings: [
              {
                  label: "お名前"
                  settings: [
                      { label: "姓", variable: "sei", required: true, placeholder: "山田" }
                      { label: "名", variable: "mei", required: true, placeholder: "太郎" }
                  ]
              }
              {
                  label: "フリガナ"
                  settings: [
                      {
                          label: "セイ"
                          variable: "seifuri"
                          required: true
                          placeholder: "ヤマダ"
                      }
                      {
                          label: "メイ"
                          variable: "meifuri"
                          required: true
                          placeholder: "タロウ"
                      }
                  ]
              }
              {
                  label: "生年月日"
                  settings: [
                      { label: "月", variable: "day", required: true }
                      { label: "月", variable: "month", required: true }
                      { label: "年", variable: "year", required: true }
                  ]
              }
              {
                  label: "性別"
                  variable: "tel"
                  required: true
                  placeholder: "09012345678"
              }
              {
                  label: "性別"
                  variable: "mail"
                  required: true
                  placeholder: "例）<EMAIL>"
              }
          ]
      }
      label: null
      nodeType: "input"
  }
  scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
}
```

- Output

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "name_birthday_tel_email",
          "settings": [
            {
              "label": "お名前",
              "settings": [
                {
                  "label": "姓",
                  "variable": "sei",
                  "required": true,
                  "placeholder": "山田"
                },
                {
                  "label": "名",
                  "variable": "mei",
                  "required": true,
                  "placeholder": "太郎"
                }
              ]
            },
            {
              "label": "フリガナ",
              "settings": [
                {
                  "label": "セイ",
                  "variable": "seifuri",
                  "required": true,
                  "placeholder": "ヤマダ"
                },
                {
                  "label": "メイ",
                  "variable": "meifuri",
                  "required": true,
                  "placeholder": "タロウ"
                }
              ]
            },
            {
              "label": "生年月日",
              "settings": [
                {
                  "label": "月",
                  "variable": "day",
                  "required": true
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": true
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": true
                }
              ]
            },
            {
              "label": "性別",
              "variable": "tel",
              "required": true,
              "placeholder": "09012345678"
            },
            {
              "label": "性別",
              "variable": "mail",
              "required": true,
              "placeholder": "例）<EMAIL>"
            }
          ]
        },
        "label": null,
        "nextNodeUid": [],
        "nodeType": "input",
        "position": null,
        "rootNode": true,
        "uid": "19d5a3f7-335b-4e8c-b89f-712da408d3b9"
      }
    }
  }
}
```

### 4. Api Update

- Mutation

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: {
        body: {
          type: "name_birthday_tel_email"
          settings: [
            {
              label: "お名前"
              settings: [
                {
                  label: "姓"
                  variable: "sei"
                  required: true
                  placeholder: "山田"
                }
                {
                  label: "名"
                  variable: "mei"
                  required: true
                  placeholder: "太郎"
                }
              ]
            }
            {
              label: "フリガナ"
              settings: [
                {
                  label: "セイ"
                  variable: "seifuri"
                  required: true
                  placeholder: "ヤマダ"
                }
                {
                  label: "メイ"
                  variable: "meifuri"
                  required: true
                  placeholder: "タロウ"
                }
              ]
            }
            {
              label: "生年月日"
              settings: [
                { label: "月", variable: "day", required: true }
                { label: "月", variable: "month", required: true }
                { label: "年", variable: "year", required: true }
              ]
            }
            {
              label: "性別"
              variable: "tel"
              required: true
              placeholder: "09012345678"
            }
            {
              label: "性別"
              variable: "mail"
              required: true
              placeholder: "例）<EMAIL>"
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
      nodeId: "19d5a3f7-335b-4e8c-b89f-712da408d3b9"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- Input

```
{
  input: {
      body: {
          type: "name_birthday_tel_email"
          settings: [
              {
                  label: "お名前"
                  settings: [
                      { label: "姓", variable: "sei", required: true, placeholder: "山田" }
                      { label: "名", variable: "mei", required: true, placeholder: "太郎" }
                  ]
              }
              {
                  label: "フリガナ"
                  settings: [
                      {
                          label: "セイ"
                          variable: "seifuri"
                          required: true
                          placeholder: "ヤマダ"
                      }
                      {
                          label: "メイ"
                          variable: "meifuri"
                          required: true
                          placeholder: "タロウ"
                      }
                  ]
              }
              {
                  label: "生年月日"
                  settings: [
                      { label: "月", variable: "day", required: true }
                      { label: "月", variable: "month", required: true }
                      { label: "年", variable: "year", required: true }
                  ]
              }
              {
                  label: "性別"
                  variable: "tel"
                  required: true
                  placeholder: "09012345678"
              }
              {
                  label: "性別"
                  variable: "mail"
                  required: true
                  placeholder: "例）<EMAIL>"
              }
          ]
      }
      label: null
      nodeType: "input"
  }
  scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
  nodeId: "19d5a3f7-335b-4e8c-b89f-712da408d3b9"
}
```

- Output

```json
{
  "data": {
    "adminsScenariosNodesUpdate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "name_birthday_tel_email",
          "settings": [
            {
              "label": "お名前",
              "settings": [
                {
                  "label": "姓",
                  "variable": "sei",
                  "required": true,
                  "placeholder": "山田"
                },
                {
                  "label": "名",
                  "variable": "mei",
                  "required": true,
                  "placeholder": "太郎"
                }
              ]
            },
            {
              "label": "フリガナ",
              "settings": [
                {
                  "label": "セイ",
                  "variable": "seifuri",
                  "required": true,
                  "placeholder": "ヤマダ"
                },
                {
                  "label": "メイ",
                  "variable": "meifuri",
                  "required": true,
                  "placeholder": "タロウ"
                }
              ]
            },
            {
              "label": "生年月日",
              "settings": [
                {
                  "label": "月",
                  "variable": "day",
                  "required": true
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": true
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": true
                }
              ]
            },
            {
              "label": "性別",
              "variable": "tel",
              "required": true,
              "placeholder": "09012345678"
            },
            {
              "label": "性別",
              "variable": "mail",
              "required": true,
              "placeholder": "例）<EMAIL>"
            }
          ]
        },
        "label": null,
        "nextNodeUid": [],
        "nodeType": "input",
        "position": null,
        "rootNode": true,
        "uid": "19d5a3f7-335b-4e8c-b89f-712da408d3b9"
      }
    }
  }
}
```

### 5. Some Variations

- Does not contain `email`

  ![Image](../../assets/name_birthday_tel_email/name_birthday_tel.png)

```json
[
  {
    "label": "お名前",
    "settings": [
      {
        "label": "姓",
        "variable": "sei",
        "required": true,
        "placeholder": "山田"
      },
      {
        "label": "名",
        "variable": "mei",
        "required": true,
        "placeholder": "太郎"
      }
    ]
  },
  {
    "label": "フリガナ",
    "settings": [
      {
        "label": "セイ",
        "variable": "seifuri",
        "required": true,
        "placeholder": "ヤマダ"
      },
      {
        "label": "メイ",
        "variable": "meifuri",
        "required": true,
        "placeholder": "タロウ"
      }
    ]
  },
  {
    "label": "生年月日",
    "settings": [
      {
        "label": "月",
        "variable": "day",
        "required": true
      },
      {
        "label": "月",
        "variable": "month",
        "required": true
      },
      {
        "label": "年",
        "variable": "year",
        "required": true
      }
    ]
  },
  {
    "label": "性別",
    "variable": "tel",
    "required": true,
    "placeholder": "09012345678"
  }
]
```

- Does not contain `day`

  ![Image](../../assets/name_birthday_tel_email/name_tel.png)

```json
[
  {
    "label": "お名前",
    "settings": [
      {
        "label": "姓",
        "variable": "sei",
        "required": true,
        "placeholder": "山田"
      },
      {
        "label": "名",
        "variable": "mei",
        "required": true,
        "placeholder": "太郎"
      }
    ]
  },
  {
    "label": "フリガナ",
    "settings": [
      {
        "label": "セイ",
        "variable": "seifuri",
        "required": true,
        "placeholder": "ヤマダ"
      },
      {
        "label": "メイ",
        "variable": "meifuri",
        "required": true,
        "placeholder": "タロウ"
      }
    ]
  },
  {
    "label": "生年月日",
    "settings": [
      {
        "label": "月",
        "variable": "month",
        "required": true
      },
      {
        "label": "年",
        "variable": "year",
        "required": true
      }
    ]
  },
  {
    "label": "性別",
    "variable": "tel",
    "required": true,
    "placeholder": "09012345678"
  }
]
```
