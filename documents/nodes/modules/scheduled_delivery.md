## Scheduled Delivery

![FullNamne](../../assets/full_name.png)

### リクエストボディの例

```json
{
  "input": {
    "nodeType": "input",
    "body": {
      "type": "scheduled_delivery",
      "settings": [
        {
          "label": "お届け日の指定",
          "variable": "scheduled_date",
          "type": "select",
          "required": true,
          "days_after_current": "4",
          "range_days": "10"
        },
        {
          "label": "お届け時間の指定",
          "variable": "scheduled_time",
          "type": "radio",
          "required": true,
          "options": [
            { "text": "に配達" },
            { "text": "09:00 ~ 11:00" },
            { "text": "11:00 ~ 13:00" },
            { "text": "12:00 ~ 13:00" },
            { "text": "14:00 ~ 16:00" }
          ]
        }
      ]
    }
  }
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "scheduled_delivery",
          "settings": [
            {
              "label": "お届け日の指定",
              "variable": "scheduled_date",
              "type": "select",
              "required": true,
              "days_after_current": "4",
              "range_days": "10"
            },
            {
              "label": "お届け時間の指定",
              "variable": "scheduled_time",
              "type": "radio",
              "required": true,
              "options": [
                {
                  "text": "に配達"
                },
                {
                  "text": "09:00 ~ 11:00"
                },
                {
                  "text": "11:00 ~ 13:00"
                },
                {
                  "text": "12:00 ~ 13:00"
                },
                {
                  "text": "14:00 ~ 16:00"
                }
              ]
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "d709a513-b7be-4d62-ad00-61103d74891c"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "c811735d-5390-4023-a43f-e5d8df7e41dd"
      input: {
        nodeType: "input"
        body: {
          type: "scheduled_delivery"
          settings: [
            {
              label: "お届け日の指定"
              variable: "scheduled_date"
              type: "select"
              required: true
              days_after_current: "4"
              range_days: "10"
            }
            {
              label: "お届け時間の指定"
              variable: "scheduled_time"
              type: "radio"
              required: true
              options: [
                { text: "に配達" }
                { text: "09:00 ~ 11:00" }
                { text: "11:00 ~ 13:00" }
                { text: "12:00 ~ 13:00" }
                { text: "14:00 ~ 16:00" }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```
