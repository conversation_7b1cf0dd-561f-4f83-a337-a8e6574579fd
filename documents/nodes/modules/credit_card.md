## Node Credit

### API Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "credit_card"
          settings: [
            {
              variable: "credit_card"
              cvv: {
                enabled: true
                required: true
                label: "セキュリティコード"
                placeholder: "123"
              }
              brand: {
                label: "カードブランド"
                enabled: true
                options: [
                  { text: "Visa" }
                  { text: "MasterCard" }
                  { text: "American Express" }
                ]
              }
              name: {
                label: "カード名義(ローマ字氏名)"
                placeholder: "YAMADA HANAKO"
                enabled: true
                required: true
              }
              number: { label: "カード番号", placeholder: "4897XXXXXXXXXXXX" }
              expired: {
                label: "有効期限"
                month: { label: "月" }
                year: { label: "年" }
              }
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      scenarioId: "10c03bad-6674-42a8-b016-2611640b292e"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- The CreditCard node will always display two fields: `クレジットカード番号` (Credit Card Number) and `有効期限` (Expiration Date). Fields such as `カード名義` (Cardholder Name), `セキュリティコード` (Security Code), and `カードブランド` (Card Brand) can be configured to be shown or hidden. To control the visibility and input requirements for these fields, the corresponding keys (`name`, `cvv`, `brand`) can be used along with the following configuration properties:

  - `enabled` (boolean): Determines whether the field is displayed on the form.
  - `required` (boolean): Specifies whether the field is mandatory for the user to fill out. If `enabled` is true, then `required` can be either `true` or `false`. However, if `enabled` is `false`, then `required` must be `false`, or the field can be omitted, and it will be implicitly considered as `false`.
  - `options`: Provides a list of valid values for the `brand` field (only applicable to the `brand` key).
  - `label`: Label of the input field.
  - `placeholder`: Placeholder of the input field.

- You can customize the CSS based on the following ID selectors:

  - `#card_expired_month`
  - `#card_expired_year`
  - `#card_name`
  - `#card_cvv`
  - `#card_number`
  - `#card_brand`
    With these IDs, you can easily apply styles to the corresponding fields in the credit card form by using `javascript_customize` in `scenario_settings`. This allows you to customize the appearance and behavior of the fields, providing a flexible and easily adjustable user experience.

### Sample UI

- ① クレジットカード番号 + 有効期限

![creditCard](../../assets/credit_card_1.png)

```json
{
  "type": "credit_card",
  "settings": [
    {
      "cvv": {
        "label": "セキュリティコード",
        "enabled": false,
        "required": false,
        "placeholder": "123"
      },
      "name": {
        "label": "カード名義(ローマ字氏名)",
        "enabled": false,
        "required": false,
        "placeholder": "MORI TARO"
      },
      "brand": {
        "label": "カードブランド",
        "enabled": false,
        "options": [
          {
            "text": "Visa"
          },
          {
            "text": "MasterCard"
          },
          {
            "text": "American Express"
          }
        ]
      },
      "number": {
        "label": "カード番号",
        "placeholder": "12341234xxxxxxxx"
      },
      "expired": {
        "year": {
          "label": "年"
        },
        "label": "",
        "month": {
          "label": "月"
        }
      },
      "variable": "credit_card"
    }
  ]
}
```

- ② クレジットカード番号 + カード名義 + 有効期限

![creditCard](../../assets/credit_card_2.png)

```json
{
  "type": "credit_card",
  "settings": [
    {
      "cvv": {
        "label": "セキュリティコード",
        "enabled": false,
        "required": false,
        "placeholder": "123"
      },
      "name": {
        "label": "カード名義(ローマ字氏名)",
        "enabled": true,
        "required": false,
        "placeholder": "MORI TARO"
      },
      "brand": {
        "label": "カードブランド",
        "enabled": false,
        "options": [
          {
            "text": "Visa"
          },
          {
            "text": "MasterCard"
          },
          {
            "text": "American Express"
          }
        ]
      },
      "number": {
        "label": "カード番号",
        "placeholder": "12341234xxxxxxxx"
      },
      "expired": {
        "year": {
          "label": "年"
        },
        "label": "",
        "month": {
          "label": "月"
        }
      },
      "variable": "credit_card"
    }
  ]
}
```

- ③ クレジットカード番号 + カード名義 + 有効期限 +セキュリティコード

![creditCard](../../assets/credit_card_3.png)

```json
{
  "type": "credit_card",
  "settings": [
    {
      "cvv": {
        "label": "セキュリティコード",
        "enabled": true,
        "required": true,
        "placeholder": "123"
      },
      "name": {
        "label": "カード名義(ローマ字氏名)",
        "enabled": true,
        "required": false,
        "placeholder": "MORI TARO"
      },
      "brand": {
        "label": "カードブランド",
        "enabled": false,
        "options": [
          {
            "text": "Visa"
          },
          {
            "text": "MasterCard"
          },
          {
            "text": "American Express"
          }
        ]
      },
      "number": {
        "label": "カード番号",
        "placeholder": "12341234xxxxxxxx"
      },
      "expired": {
        "year": {
          "label": "年"
        },
        "label": "",
        "month": {
          "label": "月"
        }
      },
      "variable": "credit_card"
    }
  ]
}
```

- ④ カードブランド + クレジットカード番号 + 有効期限 + セキュリティコード

![creditCard](../../assets/credit_card_4.png)

```json
{
  "type": "credit_card",
  "settings": [
    {
      "cvv": {
        "label": "セキュリティコード",
        "enabled": true,
        "required": true,
        "placeholder": "123"
      },
      "name": {
        "label": "カード名義(ローマ字氏名)",
        "enabled": true,
        "required": false,
        "placeholder": "MORI TARO"
      },
      "brand": {
        "label": "カードブランド",
        "enabled": true,
        "options": [
          {
            "text": "Visa"
          },
          {
            "text": "MasterCard"
          },
          {
            "text": "American Express"
          }
        ]
      },
      "number": {
        "label": "NumberCard",
        "placeholder": "12341234xxxxxxxx"
      },
      "expired": {
        "year": {
          "label": "年"
        },
        "label": "",
        "month": {
          "label": "月"
        }
      },
      "variable": "credit_card"
    }
  ]
}
```

### リクエストボディの例

```
input: {
  input: {
      body: {
          type: "credit_card"
          settings: [
              {
                  variable: "credit_card"
                  cvv: {
                      enabled: true
                      required: true
                      label: "セキュリティコード"
                      placeholder: "123"
                  }
                  brand: {
                      label: "カードブランド"
                      enabled: true
                      options: [{ text: "Visa" }, { text: "MasterCard" }, { text: "American Express" }]
                  }
                  name: {
                      label: "カード名義(ローマ字氏名)"
                      placeholder: "YAMADA HANAKO"
                      enabled: true
                      required: true
                  }
                  number: { label: "カード番号", placeholder: "4897XXXXXXXXXXXX" }
                  expired: {
                      label: "有効期限"
                      month: { label: "月" }
                      year: { label: "年" }
                  }
              }
          ]
      }
      label: null
      nodeType: "input"
  }
  scenarioId: "10c03bad-6674-42a8-b016-2611640b292e"
}
```

### Example result ( bot )

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "credit_card",
          "settings": [
            {
              "variable": "credit_card",
              "cvv": {
                "enabled": true,
                "required": true,
                "label": "セキュリティコード",
                "placeholder": "123"
              },
              "brand": {
                "label": "カードブランド",
                "enabled": true,
                "options": [
                  {
                    "text": "Visa"
                  },
                  {
                    "text": "MasterCard"
                  },
                  {
                    "text": "American Express"
                  }
                ]
              },
              "name": {
                "label": "カード名義(ローマ字氏名)",
                "placeholder": "YAMADA HANAKO",
                "enabled": true,
                "required": true
              },
              "number": {
                "label": "カード番号",
                "placeholder": "4897XXXXXXXXXXXX"
              },
              "expired": {
                "label": "有効期限",
                "month": {
                  "label": "月"
                },
                "year": {
                  "label": "年"
                }
              }
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "3f691d35-98e0-4fce-bef8-354ab0ef051c"
      }
    }
  }
}
```
