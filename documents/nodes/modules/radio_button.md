## RadioButton

![FullNamne](../../assets/radio_button_re.png)

### リクエストボディの例

```json
{
  "input": {
    "input": {
      "nodeType": "input",
      "body": {
        "type": "radio_button",
        "settings": [
          {
            "variable": "cv_upsell_1",
            "type": "radio",
            "required": true,
            "options": [
              { "text": "2本無料プレゼントに申し込む" },
              { "text": "申し込まない" }
            ]
          }
        ]
      },
      "nextNodeUid": []
    },
    "scenarioId": "15890ec2-a6f8-49e3-95d9-24e5c0c3dbe5"
  }
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "radio_button",
          "settings": [
            {
              "variable": "cv_upsell_1",
              "type": "radio",
              "required": true,
              "options": [
                {
                  "text": "2本無料プレゼントに申し込む"
                },
                {
                  "text": "申し込まない"
                }
              ]
            }
          ]
        },
        "label": null,
        "nextNodeUid": [],
        "nodeType": "input",
        "uid": "9bf8af27-bcc0-42db-b68a-6d66f3c828ea"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        nodeType: "input"
        body: {
          type: "radio_button"
          settings: [
            {
              variable: "cv_upsell_1"
              type: "radio"
              required: true
              options: [
                { text: "2本無料プレゼントに申し込む" }
                { text: "申し込まない" }
              ]
            }
          ]
        }
        nextNodeUid: []
      }
      scenarioId: "15890ec2-a6f8-49e3-95d9-24e5c0c3dbe5"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
  }
}
```
