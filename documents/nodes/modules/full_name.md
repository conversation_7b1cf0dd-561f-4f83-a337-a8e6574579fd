## Full Name

![FullNamne](../../assets/full_name.png)

### リクエストボディの例

```json
{
  "input": {
    "input": {
      "body": {
        "type": "full_name",
        "settings": [
          {
            "label": "お名前",
            "variable": "full_name",
            "type": "input",
            "required": true,
            "placeholder": "綺麗 花子"
          },
          {
            "label": "フリガナ",
            "variable": "full_name_kana",
            "type": "input",
            "required": true,
            "placeholder": "キレイ ハナコ"
          }
        ]
      },
      "label": "お名前",
      "nodeType": "input"
    },
    "scenarioId": "ffb63a32-e056-421d-a1a2-66d3acf30c7c"
  }
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "full_name",
          "settings": [
            {
              "label": "お名前",
              "variable": "full_name",
              "type": "input",
              "required": true,
              "placeholder": "綺麗 花子"
            },
            {
              "label": "フリガナ",
              "variable": "full_name_kana",
              "type": "input",
              "required": true,
              "placeholder": "キレイ ハナコ"
            }
          ]
        },
        "label": "お名前",
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "002830f8-c702-4f1a-8513-df50fc494e7f"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "full_name"
          settings: [
            {
              label: "お名前"
              variable: "full_name"
              type: "input"
              required: true
              placeholder: "綺麗 花子"
            }
            {
              label: "フリガナ"
              variable: "full_name_kana"
              type: "input"
              required: true
              placeholder: "キレイ ハナコ"
            }
          ]
        }
        label: "お名前"
        nodeType: "input"
      }
      scenarioId: "ffb63a32-e056-421d-a1a2-66d3acf30c7c"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```
