## Sex And Birthday

### リクエストボディの例

```json
{
  "input": {
    "scenarioId": "45b91ef4-def3-4d04-b081-3b2ba90f264a",
    "input": {
      "label": "住所",
      "nodeType": "input",
      "body": {
        "type": "address",
        "settings": [
          {
            "label": "郵便番号",
            "variable": "zipcode",
            "type": "input",
            "required": true,
            "placeholder": "1300000"
          },
          {
            "label": "都道府県名",
            "variable": "prefectures",
            "required": true,
            "type": "select"
          },
          {
            "label": "市区町村名",
            "variable": "address01",
            "type": "input",
            "required": true,
            "placeholder": "市区町村名 (千代田区神田神保町)"
          },
          {
            "label": "丁目-番地-号",
            "variable": "address02",
            "type": "input",
            "required": true,
            "placeholder": "例：２０−１２３−３"
          },
          {
            "label": "建物名・号室 (任意)",
            "variable": "address03",
            "type": "input",
            "required": true,
            "placeholder": "例：中野坂上サンブライトツインビル１４階"
          }
        ]
      }
    }
  }
}
```

### 結果の例（ボット）

```json
{
  "node": {
    "body": {
      "type": "sex_and_birthday",
      "settings": [
        {
          "label": "性別",
          "variable": "sex",
          "type": "radio",
          "required": false,
          "options": [{ "text": "男" }, { "text": "女" }]
        },
        {
          "label": "生年月日",
          "type": "date",
          "layout": "horizontal",
          "ratios": 3,
          "settings": [
            {
              "label": "日",
              "variable": "day",
              "type": "select",
              "ratio": 1,
              "required": false
            },
            {
              "label": "月",
              "variable": "month",
              "type": "select",
              "ratio": 1,
              "required": false
            },
            {
              "label": "年",
              "variable": "year",
              "type": "select",
              "ratio": 1,
              "required": false
            }
          ]
        }
      ]
    },
    "label": "",
    "nextNodeUid": null,
    "nodeType": "input"
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: ""
        nodeType: "input"
        body: {
          type: "sex_and_birthday"
          settings: [
            {
              label: "性別"
              variable: "sex"
              type: "radio"
              required: false
              options: [{ text: "男" }, { text: "女" }]
            }
            {
              label: "生年月日"
              type: "date"
              layout: "horizontal"
              ratios: 3
              settings: [
                {
                  label: "日"
                  variable: "day"
                  type: "select"
                  ratio: 1
                  required: false
                }
                {
                  label: "月"
                  variable: "month"
                  type: "select"
                  ratio: 1
                  required: false
                }
                {
                  label: "年"
                  variable: "year"
                  type: "select"
                  ratio: 1
                  required: false
                }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
