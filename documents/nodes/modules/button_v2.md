## Button V2

UI Sample

![image](../../assets/radio/button_v2.png)

## Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2fe87380-9561-415f-9425-9e149aad2f3b"
      input: {
        nodeType: "button_v2"
        body: {
          settings: [
            {
              variable: "agreement1"
              content: "<a href=\"\">個人情報のお取り扱い</a>個人情報のお取り扱いについて同意します。"
              type: "checkbox"
              required: true
            }
            { content: "資料を受け取る", type: "button" }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

## Input

```
{
    scenarioId: "2fe87380-9561-415f-9425-9e149aad2f3b"
    input: {
        nodeType: "button_v2"
        body: {
            settings: [
                {
                    variable: "agreement1"
                    content: "<a href=\"\">個人情報のお取り扱い</a>個人情報のお取り扱いについて同意します。"
                    type: "checkbox"
                    required: true
                }
                { content: "資料を受け取る", type: "button" }
            ]
        }
    }
}
```

## Response

```Json
{
  "data": {
      "adminsScenariosNodesCreate": {
          "clientMutationId": null,
          "message": "Successfully",
          "node": {
              "body": {
                  "settings": [
                      {
                          "variable": "agreement1",
                          "content": "<a href=\"\">個人情報のお取り扱い</a>個人情報のお取り扱いについて同意します。",
                          "type": "checkbox",
                          "required": true
                      },
                      {
                          "content": "資料を受け取る",
                          "type": "button"
                      }
                  ]
              },
              "label": null,
              "nextNodeUid": null,
              "nodeType": "button_v2",
              "position": null,
              "rootNode": false,
              "uid": "9f39f641-e9c0-4c37-8efd-f6c5506e00d9"
          }
      }
  }
}
```
