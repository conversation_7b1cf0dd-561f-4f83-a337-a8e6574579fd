## Tel Email Password

![FullNamne](../../assets/tel_email_password.png)

### リクエストボディの例

```json
{
  "input": {
    "body": {
      "type": "tel_email_password",
      "settings": [
        {
          "label": "生年月日",
          "type": "input",
          "layout": "horizontal",
          "ratios": 3,
          "settings": [
            {
              "variable": "tel1",
              "type": "input",
              "ratio": 1,
              "required": true,
              "placeholder": "03"
            },
            {
              "variable": "tel2",
              "type": "input",
              "ratio": 1,
              "required": true,
              "placeholder": "0000"
            },
            {
              "variable": "tel3",
              "type": "input",
              "ratio": 1,
              "required": true,
              "placeholder": "0000"
            }
          ]
        },
        {
          "label": "メールアドレス",
          "variable": "mail",
          "type": "input",
          "required": true,
          "placeholder": "例）<EMAIL>"
        },
        {
          "label": "パスワード",
          "variable": "password",
          "type": "input",
          "required": true,
          "placeholder": "パスワード"
        }
      ]
    },
    "nodeType": "input"
  },
  "scenarioId": "c811735d-5390-4023-a43f-e5d8df7e41dd"
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "tel_email_password",
          "settings": [
            {
              "label": "生年月日",
              "type": "input",
              "layout": "horizontal",
              "ratios": 3,
              "settings": [
                {
                  "variable": "tel1",
                  "type": "input",
                  "ratio": 1,
                  "required": true,
                  "placeholder": "03"
                },
                {
                  "variable": "tel2",
                  "type": "input",
                  "ratio": 1,
                  "required": true,
                  "placeholder": "0000"
                },
                {
                  "variable": "tel3",
                  "type": "input",
                  "ratio": 1,
                  "required": true,
                  "placeholder": "0000"
                }
              ]
            },
            {
              "label": "メールアドレス",
              "variable": "mail",
              "type": "input",
              "required": true,
              "placeholder": "例）<EMAIL>"
            },
            {
              "label": "パスワード",
              "variable": "password",
              "type": "input",
              "required": true,
              "placeholder": "パスワード"
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "8da74caf-ba14-4f25-918f-917942f2e8d4"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "tel_email_password"
          settings: [
            {
              label: "生年月日"
              type: "input"
              layout: "horizontal"
              ratios: 3
              settings: [
                {
                  variable: "tel1"
                  type: "input"
                  ratio: 1
                  required: true
                  placeholder: "03"
                }
                {
                  variable: "tel2"
                  type: "input"
                  ratio: 1
                  required: true
                  placeholder: "0000"
                }
                {
                  variable: "tel3"
                  type: "input"
                  ratio: 1
                  required: true
                  placeholder: "0000"
                }
              ]
            }
            {
              label: "メールアドレス"
              variable: "mail"
              type: "input"
              required: true
              placeholder: "例）<EMAIL>"
            }
            {
              label: "パスワード"
              variable: "password"
              type: "input"
              required: true
              placeholder: "パスワード"
            }
          ]
        }
        nodeType: "input"
      }
      scenarioId: "c811735d-5390-4023-a43f-e5d8df7e41dd"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```
