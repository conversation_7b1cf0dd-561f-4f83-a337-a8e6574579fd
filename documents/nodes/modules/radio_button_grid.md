## Radio Button Grid

- UI sample

  ![UI](../../assets/radio/radio_button_grid.png)

- New flag enable_edit_button: When the flag `enable_edit_button` is set to `true`, after selecting an option, the UI will change as shown below:

  ![UI](../../assets/radio/radio_button_grid_enable_edit_button.png)

## Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2fe87380-9561-415f-9425-9e149aad2f3b"
      input: {
        nodeType: "input"
        body: {
          type: "radio_button_grid"
          settings: [
            {
              variable: "age"
              required: true
              enable_edit_button: true
              options: [
                { text: "20代" }
                { text: "30代" }
                { text: "40代" }
                { text: "50代" }
                { text: "60代" }
                { text: "70代" }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

## Input

```
{
    scenarioId: "2fe87380-9561-415f-9425-9e149aad2f3b"
    input: {
        nodeType: "input"
        body: {
            type: "radio_button_grid"
            settings: [
                {
                    variable: "age"
                    required: true
                    enable_edit_button: true
                    options: [
                        { text: "20代" }
                        { text: "30代" }
                        { text: "40代" }
                        { text: "50代" }
                        { text: "60代" }
                        { text: "70代" }
                    ]
                }
            ]
        }
    }
}
```

## Response

```Json
{
    "data": {
        "adminsScenariosNodesCreate": {
            "message": "Successfully",
            "node": {
                "body": {
                    "type": "radio_button_grid",
                    "settings": [
                        {
                            "variable": "age",
                            "required": true,
                            "enable_edit_button": true,
                            "options": [
                                {
                                    "text": "20代"
                                },
                                {
                                    "text": "30代"
                                },
                                {
                                    "text": "40代"
                                },
                                {
                                    "text": "50代"
                                },
                                {
                                    "text": "60代"
                                },
                                {
                                    "text": "70代"
                                }
                            ]
                        }
                    ]
                },
                "label": null,
                "nextNodeUid": null,
                "nodeType": "input",
                "position": null,
                "rootNode": false,
                "uid": "89f5f751-315f-4f20-9df6-dd60351f8631"
            }
        }
    }
}
```
