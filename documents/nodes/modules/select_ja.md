# Select Module ドキュメント

## 概要

Select Moduleは、ユーザーがドロップダウンリストから1つのオプションを選択できるようにします。このモジュールは2種類のデータをサポートしています：
- **Static Options**: 事前に定義された固定オプションリスト
- **Dynamic Options**: クローラーを通じてAPIから動的に読み込まれるオプションリスト

## 目次

- [🚀 基本的な使用方法](#-基本的な使用方法)
  - [Static Options](#static-options)
  - [クローラーを使用したDynamic Options](#クローラーを使用したdynamic-options)
- [📋 フィールドリファレンス](#-フィールドリファレンス)
  - [Static Optionsのフィールド](#static-optionsのフィールド)
  - [Crawler Dataのフィールド](#crawler-dataのフィールド)
  - [UI Textsのフィールド](#ui-textsのフィールド)
- [✅ バリデーションルール](#-バリデーションルール)
- [💡 実用例](#-実用例)
- [❌ エラーハンドリング](#-エラーハンドリング)

## 🚀 基本的な使用方法

### Static Options

変更されない固定オプションセットがある場合に使用します。

**メリット:**
- シンプルで設定が簡単
- 高いパフォーマンス（API呼び出し不要）
- あまり変更されないデータに適している

**使用場面:**
- 支払い方法リスト
- 国/都道府県リスト
- その他の固定オプション

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "支払い方法",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "支払い方法を選択してください",
          "variable": "payment_method",
          "type": "select",
          "required": true,
          "options": [
            { "text": "クレジットカード（手数料無料）" },
            { "text": "銀行振込（手数料：¥250）" },
            { "text": "コンビニ決済（手数料：¥300）" }
          ]
        }
      ]
    }
  }
}
```

### クローラーを使用したDynamic Options

オプションをAPIエンドポイントから動的に読み込む必要がある場合に使用します。

**メリット:**
- データが常に最新
- 外部データソースとの柔軟な連携
- 様々なAPIとの統合が可能

**使用場面:**
- データベースからの商品リスト
- 頻繁に変更されるデータ
- サードパーティシステムとの統合

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "動的オプション",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "オプションを選択してください",
          "variable": "dynamic_option",
          "type": "select",
          "required": true,
          "crawler_data": {
            "task": {
              "class_name": "YourCrawlerClass" // 必須：クローラークラス名
            },
            "inputs": {
              "url": {
                "value": "https://example.com/api/options" // 必須：有効なURL
              }
            },
            "outputs": {
              "data": {
                "message": "データが正常に読み込まれました", // 必須
                "error": "オプションを読み込めませんでした", // 必須
                "optionValues": [] // 必須：読み込まれたデータが格納される
              }
            }
          },
          "ui_texts": {
            // オプション：UIテキストのカスタマイズ
            "button": "オプションを読み込む", // デフォルト: "データを取得する"
            "loading": "読み込み中...", // デフォルト: "読み込み中..."
            "error": "オプション読み込みエラー", // デフォルト: "データの取得に失敗しました。入力内容をご確認ください。"
            "success": "オプションが読み込まれました", // デフォルト: "データが正常に取得されました"
            "options_suffix": "オプション" // デフォルト: "オプション"
          },
          "options": [] // crawler_data使用時は空にする必要があります
        }
      ]
    }
  }
}
```

## 📋 フィールドリファレンス

### Static Optionsのフィールド

| フィールド | データ型 | 必須 | 説明 |
| -------- | -------- | ---- | ---- |
| label    | String   | はい | セレクトの上に表示されるラベル |
| variable | String   | はい | フォーム送信用の変数名 |
| type     | String   | はい | "select"である必要があります |
| required | Boolean  | いいえ | フィールドが必須かどうか（デフォルト: false） |
| options  | Array    | はい* | `{text: string}`オブジェクトの配列 |

> **注意:** `crawler_data`を使用しない場合、`options`は必須です

### Crawler Dataのフィールド

`crawler_data`を使用する場合、以下のフィールドが必須です：

| フィールド | データ型 | 必須 | 説明 |
| -------- | -------- | ---- | ---- |
| crawler_data | Object | はい | クローラー設定のコンテナ |
| - task | Object | はい | タスク設定 |
| - class_name | String | はい | 使用するクローラークラス名 |
| - inputs | Object | はい | クローラーの入力パラメータ |
| - url | Object | はい | URL設定 |
| - value | String | はい | オプションデータを取得するURL（有効なURLである必要があります） |
| - outputs | Object | はい | 出力設定 |
| - data | Object | はい | レスポンスのデータ構造 |
| - message | String | はい | オプション読み込み時の成功メッセージ |
| - error | String | はい | 読み込み失敗時のエラーメッセージ |
| - optionValues | Array | はい | 読み込まれたオプションが格納される（初期は空） |
| ui_texts | Object | いいえ | UIテキストのカスタマイズ（すべてのフィールドはオプション） |
| options | Array | いいえ | crawler_data使用時は空配列である必要があります |

### UI Textsのフィールド

UIテキスト文字列のカスタマイズ（すべてのフィールドはオプション）：

| フィールド | データ型 | デフォルト（JP） | 説明 |
| -------- | -------- | --------------- | ---- |
| button | String | "データを取得する" | データ読み込みボタンのテキスト |
| loading | String | "読み込み中..." | オプション読み込み中に表示されるテキスト |
| error | String | "データの取得に失敗しました。入力内容をご確認ください。" | 読み込み失敗時のエラーメッセージ |
| success | String | "データが正常に取得されました" | オプション読み込み成功時の成功メッセージ |
| options_suffix | String | "オプション" | 読み込まれたオプション数の後に表示されるテキスト |

## ✅ バリデーションルール

### Static Options

1. **`label`** - 空でない文字列である必要があります
2. **`variable`** - 有効な変数名である必要があります（英数字とアンダースコア）
3. **`type`** - "select"である必要があります
4. **`options`** - 空でない配列である必要があります
5. **各オプション** - `text`プロパティ（文字列）を持つオブジェクトである必要があります

### Crawler Data

1. **`crawler_data`** - 必須でオブジェクトである必要があります
2. **`crawler_data.task`** - 必須で`class_name`を含む必要があります
3. **`crawler_data.inputs.url.value`** - 必須で有効なURLである必要があります
4. **`crawler_data.outputs.data`** - 必須で以下を含む必要があります：
   - `message` (文字列)
   - `error` (文字列)
   - `optionValues` (配列、データが入力される)
5. **`options`** - `crawler_data`使用時は空配列である必要があります
6. **`ui_texts`** - （提供される場合）許可されたキーのみを含むことができます

### 重要な注意事項

⚠️ **`options`と`crawler_data`を同時に使用することはできません**
⚠️ **`crawler_data`のURLは正しい形式のデータを返す必要があります**
⚠️ **クローラークラスが存在し、正しく設定されている必要があります**

## 💡 実用例

### Static Optionsでノードを作成

```graphql
mutation CreateSelectNode {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: "支払い方法"
        nodeType: "input"
        body: {
          type: "select"
          settings: [
            {
              label: "支払い方法を選択してください"
              variable: "payment_method"
              type: "select"
              required: true
              options: [
                { text: "クレジットカード（手数料無料）" }
                { text: "銀行振込（手数料：¥250）" }
                { text: "コンビニ決済（手数料：¥300）" }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      id
      label
      nodeType
      body
    }
  }
}
```

### Crawler Dataでノードを更新

```graphql
mutation UpdateNodeWithCrawler {
  adminsScenariosNodesUpdate(
    input: {
      nodeId: "7c3986ba-05e7-4982-ba6d-a299882c625c"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "動的オプション"
        nodeType: "input"
        body: {
          type: "select"
          settings: [
            {
              label: "オプションを選択してください"
              variable: "dynamic_option"
              type: "select"
              required: true
              crawler_data: {
                task: { class_name: "YourCrawlerClass" }
                inputs: { url: { value: "https://example.com/api/options" } }
                outputs: {
                  data: {
                    message: "データが正常に読み込まれました"
                    error: "オプションを読み込めませんでした"
                    optionValues: []
                  }
                }
              }
              ui_texts: {
                button: "オプションを読み込む"
                loading: "読み込み中..."
                error: "オプション読み込みエラー"
                success: "オプションが読み込まれました"
                options_suffix: "オプション"
              }
              options: []
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      id
      label
      nodeType
      body
    }
  }
}
```

## ❌ エラーハンドリング

### エラーレスポンス構造

バリデーションが失敗した場合、以下の構造でエラーが返されます：

```json
{
  "errors": [
    {
      "message": "Validation failed",
      "extensions": {
        "code": "INVALID_INPUT",
        "details": {
          "setting_0.crawler_data.task": ["can't be blank"],
          "setting_0.crawler_data.inputs.url.value": ["must be a valid URL"],
          "setting_0.options": ["must be empty when crawler_data is present"]
        }
      }
    }
  ]
}
```

### よくあるエラー

| エラー | 原因 | 解決策 |
|-------|------|--------|
| `can't be blank` | 必須フィールドが不足 | 必要なフィールドをすべて確認して追加 |
| `must be a valid URL` | 無効なURL | URLが正しい形式（http/https）であることを確認 |
| `must be empty when crawler_data is present` | `options`と`crawler_data`の両方が存在 | どちらか一方のみを使用 |
| `invalid variable name` | 無効な変数名 | 英数字とアンダースコアのみを使用 |

## 🔧 ベストプラクティス

### 1. 適切なタイプの選択

```mermaid
graph TD
    A[Select Moduleが必要？] --> B{データは変更される？}
    B -->|いいえ| C[Static Options]
    B -->|はい| D[Dynamic Options]
    C --> E[JSONでoptionsを定義]
    D --> F[crawler_dataを設定]
```

### 2. パフォーマンス最適化

- **Static Options**: あまり変更されないデータに使用（< 50オプション）
- **Dynamic Options**: 頻繁に変更されるデータや大きなデータに使用
- **キャッシュ**: APIが遅い場合はdynamic optionsのキャッシュを検討

### 3. UXガイドライン

- **明確なラベル**: 内容を正確に説明するラベルを使用
- **エラーハンドリング**: 常に意味のあるエラーメッセージを提供
- **ローディング状態**: ui_textsを使用してローディング状態をカスタマイズ
- **バリデーション**: 必須フィールドには`required: true`を設定

### 4. セキュリティ考慮事項

- **URL検証**: crawler_dataのURLを常に検証
- **入力サニタイゼーション**: APIからのデータがサニタイズされていることを確認
- **エラーメッセージ**: エラーメッセージで機密情報を公開しない

## 📚 関連ドキュメント

- [Input Module概要](../input.md)
- [クローラー設定](../../crawler/README.md)
- [フォームバリデーション](../../validation/README.md)
- [UIカスタマイゼーション](../../ui/README.md)

---

**最終更新:** 2025-07-01
**バージョン:** 2.0
**作成者:** 開発チーム
