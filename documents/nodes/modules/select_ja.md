# Select Module ドキュメント

## 概要

Select Module は、ユーザーがドロップダウンリストから 1 つのオプションを選択できるようにします。このモジュールは 2 種類のデータをサポートしています：

- **Static Options**: 事前に定義された固定オプションリスト
- **Dynamic Options**: クローラーを通じて API から動的に読み込まれるオプションリスト

## 目次

- [使用方法](#使用方法)
  - [静的オプション (Static Options)](#静的オプション-static-options)
  - [クローラーを使用した動的オプション](#クローラーを使用した動的オプション)
- [フィールドリファレンス](#フィールドリファレンス)
  - [静的オプションのフィールド](#静的オプションのフィールド)
  - [クローラーデータのフィールド](#クロラーデータのフィールド)
  - [UI テキストのカスタマイズ](#uiテキストのカスタマイズ)
- [データ検証ルール](#データ検証ルール)
- [エラーハンドリング](#エラーハンドリング)

## 使用方法

### 静的オプション (Static Options)

![image](../../assets/select_static.png)

変更されない固定オプションセットがある場合に使用します。

**メリット:**

- シンプルで設定が簡単
- 高いパフォーマンス（API 呼び出し不要）
- あまり変更されないデータに適している

**使用場面:**

- 支払い方法リスト
- 国/都道府県リスト
- その他の固定オプション

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "Payment Method",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "支払い方法を選択してください",
          "variable": "payment_method",
          "type": "select",
          "required": true,
          "options": [
            { "text": "クレジットカード（手数料無料）" },
            { "text": "銀行振込（手数料：¥250）" },
            { "text": "コンビニ決済（手数料：¥300）" }
          ]
        }
      ]
    }
  }
}
```

### クローラーを使用した動的オプション

![image](../../assets/select_before_crawler.png)

![image](../../assets/select_crawler_success.png)

![image](../../assets/select_crawler_failed.png)

オプションを API エンドポイントから動的に読み込む必要がある場合に使用します。

##### メリット

- データが常に最新
- 外部データソースとの柔軟な連携
- 様々な API との統合が可能

##### 使用場面

- データベースからの商品リスト
- 頻繁に変更されるデータ
- サードパーティシステムとの統合

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "動的オプション",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "オプションを選択してください",
          "variable": "dynamic_option",
          "type": "select",
          "required": true,
          "crawler_data": {
            "task": {
              "class_name": "YourCrawlerClass" // 必須：クローラークラス名
            },
            "inputs": {
              "url": {
                "value": "https://example.com/api/options" // 必須：有効なURL
              }
            },
            "outputs": {
              "data": {
                "message": "データが正常に読み込まれました", // 必須
                "error": "オプションを読み込めませんでした", // 必須
                "optionValues": [] // 必須：読み込まれたデータが格納される
              }
            }
          },
          "ui_texts": {
            // オプション：UIテキストのカスタマイズ
            "button": "オプションを読み込む", // デフォルト: "データを取得する"
            "loading": "読み込み中...", // デフォルト: "読み込み中..."
            "error": "オプション読み込みエラー", // デフォルト: "データの取得に失敗しました。入力内容をご確認ください。"
            "success": "オプションが読み込まれました", // デフォルト: "データが正常に取得されました"
            "options_suffix": "オプション" // デフォルト: "オプション"
          },
          "options": [] // crawler_data使用時は空にする必要があります
        }
      ]
    }
  }
}
```

##### select crawler の動作フロー

1. ユーザーが 「オプションを読み込み」 ボタンをクリックすると、クライアントから Bot サーバーへ WebSocket メッセージ が送信されます。

2. Bot サーバーはこのメッセージを受け取り、処理のために `task runner` サーバー へ転送します。

3. `task runner` は `crawler automation` クラスを呼び出して、必要なデータを収集します。

4. データ収集後、`task runner` は結果を Bot サーバーに返します。

5. 最終的に、Bot サーバーはそのデータを WebSocket 経由でクライアントへ送り、画面に表示します。

- 基本的には、この仕組みは `headless_task` の動作とよく似ています。
  違いとしては：

  - `select with crawler`：データを取得するだけ

  - `headless_task`：データを入力してフォームを送信するという点です。

## フィールドリファレンス

### 静的オプションのフィールド

| フィールド | データ型 | 必須   | 説明                                          |
| ---------- | -------- | ------ | --------------------------------------------- |
| label      | String   | はい   | セレクトの上に表示されるラベル                |
| variable   | String   | はい   | フォーム送信用の変数名                        |
| type       | String   | はい   | "select"である必要があります                  |
| required   | Boolean  | いいえ | フィールドが必須かどうか（デフォルト: false） |
| options    | Array    | はい\* | `{text: string}`オブジェクトの配列            |

> **注意:** `crawler_data`を使用しない場合、`options`は必須です

### クローラーデータのフィールド

`crawler_data`を使用する場合、以下のフィールドが必須です：

| フィールド     | データ型 | 必須   | 説明                                                              |
| -------------- | -------- | ------ | ----------------------------------------------------------------- |
| crawler_data   | Object   | はい   | クローラー設定のコンテナ                                          |
| - task         | Object   | はい   | タスク設定                                                        |
| - class_name   | String   | はい   | 使用するクローラークラス名                                        |
| - inputs       | Object   | はい   | クローラーの入力パラメータ                                        |
| - outputs      | Object   | はい   | 出力設定                                                          |
| - url          | Object   | はい   | URL 設定                                                          |
| - value        | String   | はい   | オプションデータを取得する URL（有効な URL である必要があります） |
| - type         | String   | はい   | データ型                                                          |
| - data         | Object   | はい   | レスポンスのデータ構造                                            |
| - message      | String   | はい   | オプション読み込み時の成功メッセージ                              |
| - error        | String   | はい   | 読み込み失敗時のエラーメッセージ                                  |
| - optionValues | Array    | はい   | 読み込まれたオプションが格納される（初期は空）                    |
| ui_texts       | Object   | いいえ | UI テキストのカスタマイズ（すべてのフィールドはオプション）       |

### UI テキストのカスタマイズ

UI テキスト文字列のカスタマイズ（すべてのフィールドはオプション）：

| フィールド     | データ型 | デフォルト（JP）                                         | 説明                                             |
| -------------- | -------- | -------------------------------------------------------- | ------------------------------------------------ |
| button         | String   | "データを取得する"                                       | データ読み込みボタンのテキスト                   |
| loading        | String   | "読み込み中..."                                          | オプション読み込み中に表示されるテキスト         |
| error          | String   | "データの取得に失敗しました。入力内容をご確認ください。" | 読み込み失敗時のエラーメッセージ                 |
| success        | String   | "データが正常に取得されました"                           | オプション読み込み成功時の成功メッセージ         |
| options_suffix | String   | "オプション"                                             | 読み込まれたオプション数の後に表示されるテキスト |

## データ検証ルール

### 静的オプション

1. **`label`** - 空でない文字列である必要があります
2. **`variable`** - 有効な変数名である必要があります（英数字とアンダースコア）
3. **`type`** - "select"である必要があります
4. **`options`** - 空でない配列である必要があります
5. **各オプション** - `text`プロパティ（文字列）を持つオブジェクトである必要があります

#### Crawler Data

1. **`crawler_data`** - 必須でオブジェクトである必要があります
2. **`crawler_data.task`** - 必須で`class_name`を含む必要があります
3. **`crawler_data.inputs.url.value`** - 必須で有効な URL である必要があります
4. **`crawler_data.outputs.data`** - 必須で以下を含む必要があります：
   - `message` (文字列)
   - `error` (文字列)
   - `optionValues` (配列、データが入力される)
5. **`options`** - `crawler_data`使用時は空配列である必要があります
6. **`crawler_data.ui_texts`** - （提供される場合）許可されたキーのみを含むことができます

### 重要な注意点

- **`options`と`crawler_data`を同時に使用することはできません。crawler_data を使用する場合、`options`は空にする必要があります**
- **`crawler_data`の URL は正しい形式のデータを返す必要があります**
- **クローラークラスが存在し、正しく設定されている必要があります**

#### Static Options でノードを作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "0338af0c-d425-45f5-bc72-6e5973898be0"
      input: {
        nodeType: "input"
        label: null
        body: {
          type: "select"
          settings: [
            {
              label: "設置希望日"
              variable: "scheduled_date"
              type: "select"
              required: true
              options: [
                { text: "クレジットカード（手数料無料）" }
                { text: "銀行振込（手数料：¥250）" }
                { text: "コンビニ決済（手数料：¥300）" }
              ]
            }
          ]
        }
      }
    }
  ) {
    clientMutationId
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

#### Crawler Data でノードを作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "0338af0c-d425-45f5-bc72-6e5973898be0"
      input: {
        nodeType: "input"
        label: null
        body: {
          type: "select"
          settings: [
            {
              label: "設置希望日"
              variable: "scheduled_date"
              type: "select"
              required: true
              options: []
              crawler_data: {
                task: { class_name: "HawaiiWaterDesiredInstallationDate" }
                inputs: {
                  url: {
                    value: "https://www.hawaiiwater.co.jp/regist/index"
                    type: "string"
                  }
                  addressee: { value: "", type: "string" }
                  zipcode: { value: "", type: "string" }
                  prefectures: { value: "", type: "string" }
                }
                outputs: { data: { message: "", error: "", optionValues: [] } }
                ui_texts: {
                  button: "データを取得する"
                  loading: "読み込み中..."
                  error: "データの取得に失敗しました。入力内容をご確認ください。"
                  success: "データが正常に取得されました"
                  options_suffix: "オプション"
                }
              }
            }
          ]
        }
      }
    }
  ) {
    clientMutationId
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

## エラーハンドリング

### エラーレスポンス構造

バリデーションが失敗した場合、以下の構造でエラーが返されます：

```json
{
  "errors": [
    {
      "message": "Create Failed",
      "locations": [
        {
          "line": 2,
          "column": 5
        }
      ],
      "path": ["adminsScenariosNodesCreate"],
      "status": "unprocessable_entity",
      "code": 422,
      "errors": {
        "setting.task": ["Task を入力してください"],
        "setting.inputs": ["Inputs を入力してください"],
        "setting.outputs": ["Outputs を入力してください"],
        "setting.ui_texts": ["Ui texts 無効なキーが含まれています: load2wing"]
      },
      "reset_data": false
    }
  ],
  "data": {
    "adminsScenariosNodesCreate": null
  }
}
```
