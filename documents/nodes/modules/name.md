## ノード名は、2 つのノードタイプから成ります:

_`support_ui_enable` はシナリオ内で設定されています。_

### ノード名

- JSON データのサンプル

```json
"node": {
   "body": {
       "type": "name",
       "settings": [
           {
               "label": "お名前",
               "layout": "horizontal",
               "ratios": 2,
               "settings": [
                   {
                       "label": "姓",
                       "variable": "sei",
                       "type": "input",
                       "required": true,
                       "placeholder": "山田",
                       "ratio": 1
                   },
                   {
                       "label": "名",
                       "variable": "mei",
                       "type": "input",
                       "required": true,
                       "placeholder": "太郎",
                       "ratio": 1
                   }
               ]
           },
           {
               "label": "フリガナ",
               "layout": "horizontal",
               "ratios": 2,
               "settings": [
                   {
                       "label": "セイ",
                       "variable": "seifuri",
                       "type": "input",
                       "required": true,
                       "placeholder": "ヤマダ",
                       "ratio": 1
                   },
                   {
                       "label": "メイ",
                       "variable": "meifuri",
                       "type": "input",
                       "required": true,
                       "placeholder": "タロウ",
                       "ratio": 1
                   }
               ]
           }
       ]
   },
   "label": "お名前",
   "nextNodeUid": null,
   "nodeType": "input"
}
```

- [URL](../../../client/src/components/modules/Name.vue)ファイルハンドル

1.  UI は `support_ui_enable` が `false` のときに表示されます。

- ライブラリ[wanakana](https://github.com/WaniKani/WanaKana)を使用して、ユーザーの入力をそのまま変換します。

![Image](../../assets/name.png)

2. UI はフラグ `support_ui_enable` が `true` のときに表示されます。

- API GetKanaName を使用して名前を変換します: ユーザーが入力した後、「名前を交換」ボタンをクリックします => API `GetKanaName`を呼び出します => レスポンス（`first`、`firstKana`、`last`、`lastKana`）を返します => クライアントが処理して入力フィールドに設定します。
  ![Image](../../assets/name_sp_ui.png)

### ノードフルネーム

- JSON データのサンプル

```json
{
  "node": {
    "body": {
      "type": "full_name",
      "settings": [
        {
          "label": "お名前",
          "variable": "full_name",
          "type": "input",
          "required": true,
          "placeholder": "綺麗 花子"
        },
        {
          "label": "フリガナ",
          "variable": "full_name_kana",
          "type": "input",
          "required": true,
          "placeholder": "キレイ ハナコ"
        }
      ]
    },
    "label": "お名前",
    "nextNodeUid": null,
    "nodeType": "input",
    "position": null,
    "rootNode": false,
    "uid": "002830f8-c702-4f1a-8513-df50fc494e7f"
  }
}
```

- [URL](../../../client/src/components/modules/FullName.vue)ファイルハンドル

1. UI は `support_ui_enable` が `false` のときに表示されます。

- ライブラリ [vanilla-autokana](https://github.com/ryo-utsunomiya/vanilla-autokana) を使用して名前を変換します。

![Image](../../assets/full_name.png)

2. UI はフラグ `support_ui_enable` が `true` のときに表示されます。

- API GetKanaName を使用して名前を変換します: ユーザーが入力した後、「名前を交換」ボタンをクリックします => API `GetKanaName`を呼び出します => レスポンス（`first`、`firstKana`、`last`、`lastKana`）を返します => クライアントが処理して入力フィールドに設定します。

![Image](../../assets/full_name_sp_ui.png)

## API GetKanaName

- `support_ui_enable` が `true` の UI (ノードフルネーム、ノード名) に関して、ライブラリ [vanilla-autokana](https://github.com/ryo-utsunomiya/vanilla-autokana) または [wanakana](https://github.com/WaniKani/WanaKana) のみを使用する場合、変換できないため、サーバー側でライブラリ [natto](https://github.com/buruzaemon/natto) を使用して処理する必要があります。

- ライブラリには、いくつかの名前を正しく変換できない制限があります

- API 変換は、[このリンク](../../../server/app/graphql/resolvers/convert_name/get_kana.rb)のコードに基づいています。
- クラスは、[このリンク](../../../server/app/models/convert_name.rb)のコードに基づいています。

- 使用例

```graphql
query GetKanaName {
  getKanaName(fullName: "テスト　福田") {
    first
    firstKana
    last
    lastKana
  }
}
```

- レスポンスサンプル

```json
{
  "data": {
    "getKanaName": {
      "first": "福田",
      "firstKana": "フクダ",
      "last": "テスト",
      "lastKana": "テスト"
    }
  }
}
```

## Name

### リクエストボディの例

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "お名前",
    "nodeType": "input",
    "body": {
      "type": "name",
      "settings": [
        {
          "label": "お名前",
          "layout": "horizontal",
          "ratios": 2,
          "settings": [
            {
              "label": "姓",
              "variable": "sei",
              "type": "input",
              "required": true,
              "placeholder": "山田",
              "ratio": 1
            },
            {
              "label": "名",
              "variable": "mei",
              "type": "input",
              "required": true,
              "placeholder": "太郎",
              "ratio": 1
            }
          ]
        },
        {
          "label": "フリガナ",
          "layout": "horizontal",
          "ratios": 2,
          "settings": [
            {
              "label": "セイ",
              "variable": "seifuri",
              "type": "input",
              "required": true,
              "placeholder": "ヤマダ",
              "ratio": 1
            },
            {
              "label": "メイ",
              "variable": "meifuri",
              "type": "input",
              "required": true,
              "placeholder": "タロウ",
              "ratio": 1
            }
          ]
        }
      ]
    }
  }
}
```

### 結果の例（ボット）

```json
{
  "body": {
    "type": "name",
    "settings": [
      {
        "label": "お名前",
        "layout": "horizontal",
        "ratios": 2,
        "settings": [
          {
            "label": "姓",
            "variable": "sei",
            "type": "input",
            "required": true,
            "placeholder": "山田",
            "ratio": 1
          },
          {
            "label": "名",
            "variable": "mei",
            "type": "input",
            "required": true,
            "placeholder": "太郎",
            "ratio": 1
          }
        ]
      },
      {
        "label": "フリガナ",
        "layout": "horizontal",
        "ratios": 2,
        "settings": [
          {
            "label": "セイ",
            "variable": "seifuri",
            "type": "input",
            "required": true,
            "placeholder": "ヤマダ",
            "ratio": 1
          },
          {
            "label": "メイ",
            "variable": "meifuri",
            "type": "input",
            "required": true,
            "placeholder": "タロウ",
            "ratio": 1
          }
        ]
      }
    ]
  },
  "label": "お名前",
  "nextNodeUid": null,
  "nodeType": "input"
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: "お名前"
        nodeType: "input"
        body: {
          type: "name"
          settings: [
            {
              label: "お名前"
              layout: "horizontal"
              ratios: 2
              settings: [
                {
                  label: "姓"
                  variable: "sei"
                  type: "input"
                  required: true
                  placeholder: "山田"
                  ratio: 1
                }
                {
                  label: "名"
                  variable: "mei"
                  type: "input"
                  required: true
                  placeholder: "太郎"
                  ratio: 1
                }
              ]
            }
            {
              label: "フリガナ"
              layout: "horizontal"
              ratios: 2
              settings: [
                {
                  label: "セイ"
                  variable: "seifuri"
                  type: "input"
                  required: true
                  placeholder: "ヤマダ"
                  ratio: 1
                }
                {
                  label: "メイ"
                  variable: "meifuri"
                  type: "input"
                  required: true
                  placeholder: "タロウ"
                  ratio: 1
                }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: {
        body: {
          type: "name"
          settings: [
            { label: "お名前", layout: "horizontal", ratios: 2, options: [] }
          ]
        }
        label: "//"
        nextNodeUid: ["bb9bf175-c1d1-4d7c-a0b5-73c179f2cfeb"]
        nodeType: "input"
      }
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      nodeId: "7c3986ba-05e7-4982-ba6d-a299882c625c"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
