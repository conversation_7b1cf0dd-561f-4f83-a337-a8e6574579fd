## NameSexBirthday

### 1. UI Node

- `support_ui_enable` is `false`

  ![Image](../../assets/name_sex_birthday.png)

- `support_ui_enable` is `true`

  ![Image](../../assets/name_sex_birthday_sp_ui.png)

### 2. `body:settings` sample

```json
[
  {
    "label": "",
    "settings": [
      {
        "label": "姓",
        "variable": "sei",
        "required": true,
        "placeholder": "山田"
      },
      {
        "label": "名",
        "variable": "mei",
        "required": true,
        "placeholder": "太郎"
      }
    ]
  },
  {
    "label": "",
    "settings": [
      {
        "label": "セイ",
        "variable": "seifuri",
        "required": true,
        "placeholder": "ヤマダ"
      },
      {
        "label": "メイ",
        "variable": "meifuri",
        "required": true,
        "placeholder": "タロウ"
      }
    ]
  },
  {
    "label": "性別",
    "variable": "sex",
    "required": true
  },
  {
    "label": "",
    "settings": [
      {
        "label": "日",
        "variable": "day",
        "required": true
      },
      {
        "label": "月",
        "variable": "month",
        "required": true
      },
      {
        "label": "年",
        "variable": "year",
        "required": true
      }
    ]
  }
]
```

### 3. API Create

- Mutation

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        body: {
          type: "name_sex_birthday"
          settings: [
            {
              label: "お名前"
              settings: [
                {
                  label: "姓"
                  variable: "sei"
                  required: true
                  placeholder: "山田"
                }
                {
                  label: "名"
                  variable: "mei"
                  required: true
                  placeholder: "太郎"
                }
              ]
            }
            {
              label: "フリガナ"
              settings: [
                {
                  label: "セイ"
                  variable: "seifuri"
                  required: true
                  placeholder: "ヤマダ"
                }
                {
                  label: "メイ"
                  variable: "meifuri"
                  required: true
                  placeholder: "タロウ"
                }
              ]
            }
            { label: "性別", variable: "sex", required: true }
            {
              label: "生年月日"
              settings: [
                { label: "日", variable: "day", required: true }
                { label: "月", variable: "month", required: true }
                { label: "年", variable: "year", required: true }
              ]
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      scenarioId: "1d5fd9c3-18f8-44ca-b2f4-be7b7cced49c"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- Input

```
{
  input: {
    body: {
      type: "name_sex_birthday"
      settings: [
        {
          label: "お名前"
          settings: [
            {
              label: "姓"
              variable: "sei"
              required: true
              placeholder: "山田"
            }
            {
              label: "名"
              variable: "mei"
              required: true
              placeholder: "太郎"
            }
          ]
        }
        {
          label: "フリガナ"
          settings: [
            {
              label: "セイ"
              variable: "seifuri"
              required: true
              placeholder: "ヤマダ"
            }
            {
              label: "メイ"
              variable: "meifuri"
              required: true
              placeholder: "タロウ"
            }
          ]
        }
        { label: "性別", variable: "sex", required: true }
        {
          label: "生年月日"
          settings: [
            { label: "日", variable: "day", required: true }
            { label: "月", variable: "month", required: true }
            { label: "年", variable: "year", required: true }
          ]
        }
      ]
    }
    label: null
    nodeType: "input"
  }
  scenarioId: "1d5fd9c3-18f8-44ca-b2f4-be7b7cced49c"
}
```

- Output

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "name_sex_birthday",
          "settings": [
            {
              "label": "お名前",
              "settings": [
                {
                  "label": "姓",
                  "variable": "sei",
                  "required": true,
                  "placeholder": "山田"
                },
                {
                  "label": "名",
                  "variable": "mei",
                  "required": true,
                  "placeholder": "太郎"
                }
              ]
            },
            {
              "label": "フリガナ",
              "settings": [
                {
                  "label": "セイ",
                  "variable": "seifuri",
                  "required": true,
                  "placeholder": "ヤマダ"
                },
                {
                  "label": "メイ",
                  "variable": "meifuri",
                  "required": true,
                  "placeholder": "タロウ"
                }
              ]
            },
            {
              "label": "性別",
              "variable": "sex",
              "required": true
            },
            {
              "label": "生年月日",
              "settings": [
                {
                  "label": "日",
                  "variable": "day",
                  "required": true
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": true
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": true
                }
              ]
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "b024f8a6-ea8a-4dbf-8b4f-e6910c01e45c"
      }
    }
  }
}
```

### 4. Api Update

- Mutation

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: {
        body: {
          type: "name_sex_birthday"
          settings: [
            {
              label: ""
              settings: [
                {
                  label: "姓"
                  variable: "sei"
                  required: true
                  placeholder: "山田"
                }
                {
                  label: "名"
                  variable: "mei"
                  required: true
                  placeholder: "太郎"
                }
              ]
            }
            {
              label: "フリガナ"
              settings: [
                {
                  label: "セイ"
                  variable: "seifuri"
                  required: true
                  placeholder: "ヤマダ"
                }
                {
                  label: "メイ"
                  variable: "meifuri"
                  required: true
                  placeholder: "タロウ"
                }
              ]
            }
            { label: "性別", variable: "sex", required: true }
            {
              label: "生年月日"
              settings: [
                { label: "日", variable: "day", required: true }
                { label: "月", variable: "month", required: true }
                { label: "年", variable: "year", required: true }
              ]
            }
          ]
        }
        label: null
        nodeType: "input"
      }
      nodeId: "0e2df8e2-e410-45d1-a6b8-8bf16a894083"
      scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

- Input

```
{
  input: {
    body: {
      type: "name_sex_birthday"
      settings: [
        {
          label: ""
          settings: [
            {
              label: "姓"
              variable: "sei"
              required: true
              placeholder: "山田"
            }
            {
              label: "名"
              variable: "mei"
              required: true
              placeholder: "太郎"
            }
          ]
        }
        {
          label: "フリガナ"
          settings: [
            {
              label: "セイ"
              variable: "seifuri"
              required: true
              placeholder: "ヤマダ"
            }
            {
              label: "メイ"
              variable: "meifuri"
              required: true
              placeholder: "タロウ"
            }
          ]
        }
        { label: "性別", variable: "sex", required: true }
        {
          label: "生年月日"
          settings: [
            { label: "日", variable: "day", required: true }
            { label: "月", variable: "month", required: true }
            { label: "年", variable: "year", required: true }
          ]
        }
      ]
    }
    label: null
    nodeType: "input"
  }
  nodeId: "0e2df8e2-e410-45d1-a6b8-8bf16a894083"
  scenarioId: "30fb6c29-e12e-42ff-9fa5-cdc4144b8c56"
}
```

- Output

```json
{
  "data": {
    "adminsScenariosNodesUpdate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "name_sex_birthday",
          "settings": [
            {
              "label": "",
              "settings": [
                {
                  "label": "姓",
                  "variable": "sei",
                  "required": true,
                  "placeholder": "山田"
                },
                {
                  "label": "名",
                  "variable": "mei",
                  "required": true,
                  "placeholder": "太郎"
                }
              ]
            },
            {
              "label": "フリガナ",
              "settings": [
                {
                  "label": "セイ",
                  "variable": "seifuri",
                  "required": true,
                  "placeholder": "ヤマダ"
                },
                {
                  "label": "メイ",
                  "variable": "meifuri",
                  "required": true,
                  "placeholder": "タロウ"
                }
              ]
            },
            {
              "label": "性別",
              "variable": "sex",
              "required": true
            },
            {
              "label": "生年月日",
              "settings": [
                {
                  "label": "日",
                  "variable": "day",
                  "required": true
                },
                {
                  "label": "月",
                  "variable": "month",
                  "required": true
                },
                {
                  "label": "年",
                  "variable": "year",
                  "required": true
                }
              ]
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["4714e265-543d-4cdf-b6d5-eeb636d28163"],
        "nodeType": "input",
        "position": null,
        "rootNode": false,
        "uid": "0e2df8e2-e410-45d1-a6b8-8bf16a894083"
      }
    }
  }
}
```
