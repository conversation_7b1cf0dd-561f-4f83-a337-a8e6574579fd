## Password

### リクエストボディの例

```
input: {
      scenarioId: "a4d862bb-7136-43de-82df-c60192168a65"
      input: {
        nodeType: "input"
        body: {
          type: "password"
          settings: [
              {
                label: "Password"
                variable: "password"
                type: "input"
                required: true
              }
          ]}
      }
    }
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "type": "password",
          "settings": [
            {
              "label": "Password",
              "variable": "password",
              "type": "input",
              "required": true
            }
          ]
        },
        "nextNodeUid": null,
        "nodeType": "input"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "a4d862bb-7136-43de-82df-c60192168a65"
      input: {
        label: "Password"
        nodeType: "input"
        body: {
          type: "password"
          settings: [
            {
              label: "Password"
              variable: "password"
              type: "input"
              required: false
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: {
        label: "Password"
        nodeType: "input"
        body: {
          type: "password"
          settings: [
            {
              label: "Password"
              variable: "password"
              type: "input"
              required: true
            }
          ]
        }
      }
      nodeId: "d1e08cbf-219a-4b05-a8f9-b7bfae6ef264"
      scenarioId: "a4d862bb-7136-43de-82df-c60192168a65"
    }
  ) {
    clientMutationId
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
  }
}
```
