## Radio Button Reselectable

![FullNamne](../../assets/radio_button.png)

### リクエストボディの例

```json
{
  "input": {
    "nodeType": "input",
    "body": {
      "repeatable": true,
      "type": "radio_button_reselectable",
      "settings": [
        {
          "variable": "payment_method",
          "type": "radio",
          "required": true,
          "options": [
            { "text": "NP 後払い wizRT(手数料:250 円)" },
            { "text": "クレジット決済(手数料:無料)" }
          ]
        }
      ]
    },
    "nextNodeUid": []
  },
  "scenarioId": "45b91ef4-def3-4d04-b081-3b2ba90f264a"
}
```

### 結果の例（ボット）

```json
{
  "data": {
    "adminsScenariosNodesCreate": {
      "message": "Successfully",
      "node": {
        "body": {
          "repeatable": true,
          "type": "radio_button_reselectable",
          "settings": [
            {
              "variable": "payment_method",
              "type": "radio",
              "required": true,
              "options": [
                {
                  "text": "NP後払いwizRT(手数料:250円)"
                },
                {
                  "text": "クレジット決済(手数料:無料)"
                }
              ]
            }
          ]
        },
        "label": null,
        "nextNodeUid": [],
        "nodeType": "input",
        "uid": "6294d591-e259-4a38-bf9d-2b92965b2ffe"
      }
    }
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      input: {
        nodeType: "input"
        body: {
          repeatable: true
          type: "radio_button_reselectable"
          settings: [
            {
              variable: "payment_method"
              type: "radio"
              required: true
              options: [
                { text: "NP後払いwizRT(手数料:250円)" }
                { text: "クレジット決済(手数料:無料)" }
              ]
            }
          ]
        }
        nextNodeUid: []
      }
      scenarioId: "45b91ef4-def3-4d04-b081-3b2ba90f264a"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
  }
}
```
