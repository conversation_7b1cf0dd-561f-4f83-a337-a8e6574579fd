# Node Button

## Template

![Evidence](../../assets/button.png)

### Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "test create"
        nodeType: "button"
        body: {
          settings: [
            {
              type: "template"
              content: {
                title: "内容確認（枠内をスクロールして最後までご確認ください）\n"
                body: "◆<b>注文内容</b>◆\n<b>【商品名】</b> {{product}}\n<b>【お名前】</b> {{sei}}{{mei}}\n<b>【お名前（フリガナ）】</b> {{seifuri}}{{meifuri}}\n<b>【郵便番号】</b> {{zipcode}}\n<b>【住所】</b> {{prefectures}}{{address01}}{{address02}}\n<b>【電話番号】</b> {{tel}}\n<b>【メールアドレス】</b> {{mail}}\n<b>【お支払方法】</b> {{payment_method}}\n"
              }
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
    adminsScenariosNodesUpdate(
        input: {
            input: {
                   label: "test create"
                   nodeType: "button"
                   body: {
                          settings: [
                       {
              type: "template"
              content: {
                 title:"内容確認（枠内をスクロールして最後までご確認ください）\n",
                 body: "◆<b>注文内容</b>◆\n<b>【商品名】</b> {{product}}\n<b>【お名前】</b> {{sei}}{{mei}}\n<b>【お名前（フリガナ）】</b> {{seifuri}}{{meifuri}}\n<b>【郵便番号】</b> {{zipcode}}\n<b>【住所】</b> {{prefectures}}{{address01}}{{address02}}\n<b>【電話番号】</b> {{tel}}\n<b>【メールアドレス】</b> {{mail}}\n<b>【お支払方法】</b> {{payment_method}}\n"}
            }
          ]
            }
            nodeId:"47553164-f6fc-4c13-9c91-d1cb67ff54fa"
            scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
        }
    ) {
        message
        node {
            body
            label
            nextNodeUid
            nodeType
            position
            rootNode
            uid
        }
    }
}

```

### Mutation Destroy

```graphql
mutation AdminsScenariosNodesDestroy {
  adminsScenariosNodesDestroy(
    input: {
      nodeId: "47553164-f6fc-4c13-9c91-d1cb67ff54fa"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
    }
  ) {
    message
  }
}
```

## Policy

![Evidence](../../assets/button_policy.png)

### Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "test create"
        nodeType: "button"
        body: {
          settings: [
            {
              type: "policy"
              content: {
                 title:"個人情報保護方針",
                 body: "第1条(会員)

        1. 「会員」とは、当社が定める手続に従い本規約に同意の上、入会の申し込みを行う個人をいいます。
        2. 「会員情報」とは、会員が当社に開示した会員の属性に関する情報および会員の取引に関する履歴等の情報をいいます。
        3. 本規約は、すべての会員に適用され、登録手続時および登録後にお守りいただく規約です。
        4.顧客情報や履歴を管理するにあたって、登録されたお客様を会員と呼称します。
        5. 会員は、本規約に加え、弊社が管理運営する通信販売用ウェブサイトに記載された各種利用条件(各種手数料の有無、金額、配送方法等を含みますが、これらに限られません。)に従うものとし、これらに変更がある場合には、変更後の各種利用条件がウェブサイトに表示された後は、変更後の条件に従うものとします。"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
    adminsScenariosNodesUpdate(
        input: {
           input: {
              label: "test create"
              nodeType: "button"
              body: {
              settings: [
              {
              type: "policy"
              content: {
                 title:"個人情報保護方針",
                 body: "第1条(会員)

        1. 「会員」とは、当社が定める手続に従い本規約に同意の上、入会の申し込みを行う個人をいいます。
        2. 「会員情報」とは、会員が当社に開示した会員の属性に関する情報および会員の取引に関する履歴等の情報をいいます。
        3. 本規約は、すべての会員に適用され、登録手続時および登録後にお守りいただく規約です。
        4.顧客情報や履歴を管理するにあたって、登録されたお客様を会員と呼称します。
        5. 会員は、本規約に加え、弊社が管理運営する通信販売用ウェブサイトに記載された各種利用条件(各種手数料の有無、金額、配送方法等を含みますが、これらに限られません。)に従うものとし、これらに変更がある場合には、変更後の各種利用条件がウェブサイトに表示された後は、変更後の条件に従うものとします。"
            }
          ]
        }
      }
            nodeId:"47553164-f6fc-4c13-9c91-d1cb67ff54fa"
            scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
        }
    ) {
        message
        node {
            body
            label
            nextNodeUid
            nodeType
            position
            rootNode
            uid
        }
    }
}
```

### Mutation Destroy

```graphql
mutation AdminsScenariosNodesDestroy {
  adminsScenariosNodesDestroy(
    input: {
      nodeId: "47553164-f6fc-4c13-9c91-d1cb67ff54fa"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
    }
  ) {
    message
  }
}
```

## Button

### Mutation Create

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        nodeType: "button"
        body: { settings: [{ type: "button", content: "購入確認画面へ" }] }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### ミューテーションによる更新


```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      nextNodeUid: []
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      nodeId: "47553164-f6fc-4c13-9c91-d1cb67ff54fa"
      input: {
        nodeType: "button"
        body: { settings: [{ type: "button", content: "Button" }] }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### Mutation Destroy

```graphql
mutation AdminsScenariosNodesDestroy {
  adminsScenariosNodesDestroy(
    input: {
      nodeId: "47553164-f6fc-4c13-9c91-d1cb67ff54fa"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
    }
  ) {
    message
  }
}
```

## Combine

![Evidence](../../assets/button.png)

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        nodeType: "button"
        body: {
          settings: [
            {
              type: "template"
              content: {
                 title:"内容確認（枠内をスクロールして最後までご確認ください）\n",
                 body: "◆<b>注文内容</b>◆\n<b>【商品名】</b> {{product}}\n<b>【お名前】</b> {{sei}}{{mei}}\n<b>【お名前（フリガナ）】</b> {{seifuri}}{{meifuri}}\n<b>【郵便番号】</b> {{zipcode}}\n<b>【住所】</b> {{prefectures}}{{address01}}{{address02}}\n<b>【電話番号】</b> {{tel}}\n<b>【メールアドレス】</b> {{mail}}\n<b>【お支払方法】.                                                </b> {{payment_method}}\n"}
            }
           {
              type: "policy"
              content: {
                 title:"個人情報保護方針",
                 body: "第1条(会員)

                            1. 「会員」とは、当社が定める手続に従い本規約に同意の上、入会の申し込みを行う個人をいいます。
                            2. 「会員情報」とは、会員が当社に開示した会員の属性に関する情報および会員の取引に関する履歴等の情報をいいます。
                            3. 本規約は、すべての会員に適用され、登録手続時および登録後にお守りいただく規約です。
                            4.顧客情報や履歴を管理するにあたって、登録されたお客様を会員と呼称します。
                            5. 会員は、本規約に加え、弊社が管理運営する通信販売用ウェブサイトに記載された各種利用条件(各種手数料の有無、金額、配送方法等を含みますが、これらに限られません。)に従うものとし、これらに変更がある場合には、変更後の各種利用条件がウェブサイトに表示された後は、変更後の条件に従うものとします。"
            }
            {
              type: "button"
              content: "購入確認画面へ"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
