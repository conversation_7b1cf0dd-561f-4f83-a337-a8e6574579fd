## Address

![Addresses](../../assets/address.png)

### リクエストボディの例

```json
{
  "input": {
    "scenarioId": "45b91ef4-def3-4d04-b081-3b2ba90f264a",
    "input": {
      "label": "住所",
      "nodeType": "input",
      "body": {
        "type": "address",
        "settings": [
          {
            "label": "郵便番号",
            "variable": "zipcode",
            "type": "input",
            "required": true,
            "placeholder": "1300000"
          },
          {
            "label": "都道府県名",
            "variable": "prefectures",
            "required": true,
            "type": "select"
          },
          {
            "label": "市区町村名",
            "variable": "address01",
            "type": "input",
            "required": true,
            "placeholder": "市区町村名 (千代田区神田神保町)"
          },
          {
            "label": "丁目-番地-号",
            "variable": "address02",
            "type": "input",
            "required": true,
            "placeholder": "例：２０−１２３−３"
          },
          {
            "label": "建物名・号室 (任意)",
            "variable": "address03",
            "type": "input",
            "required": true,
            "placeholder": "例：中野坂上サンブライトツインビル１４階"
          }
        ]
      }
    }
  }
}
```

### 結果の例（ボット）

```json
{
  "message": "Successfully",
  "node": {
    "body": {
      "type": "address",
      "settings": [
        {
          "label": "郵便番号",
          "variable": "zipcode",
          "type": "input",
          "required": true,
          "placeholder": "1300000"
        },
        {
          "label": "都道府県名",
          "variable": "prefectures",
          "required": true,
          "type": "select"
        },
        {
          "label": "市区町村名",
          "variable": "address01",
          "type": "input",
          "required": true,
          "placeholder": "市区町村名 (千代田区神田神保町)"
        },
        {
          "label": "丁目-番地-号",
          "variable": "address02",
          "type": "input",
          "required": true,
          "placeholder": "例：２０−１２３−３"
        },
        {
          "label": "建物名・号室 (任意)",
          "variable": "address03",
          "type": "input",
          "required": true,
          "placeholder": "例：中野坂上サンブライトツインビル１４階"
        }
      ]
    },
    "label": "住所",
    "nextNodeUid": null,
    "nodeType": "input"
  }
}
```

### ミューテーションによる作成

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "45b91ef4-def3-4d04-b081-3b2ba90f264a"
      input: {
        label: "住所"
        nodeType: "input"
        body: {
          type: "address"
          settings: [
            {
              label: "郵便番号"
              variable: "zipcode"
              type: "input"
              required: true
              placeholder: "1300000"
            }
            {
              label: "都道府県名"
              variable: "prefectures"
              required: true
              type: "select"
            }
            {
              label: "市区町村名"
              variable: "address01"
              type: "input"
              required: true
              placeholder: "市区町村名 (千代田区神田神保町)"
            }
            {
              label: "丁目-番地-号"
              variable: "address02"
              type: "input"
              required: true
              placeholder: "例：２０−１２３−３"
            }
            {
              label: "建物名・号室 (任意)"
              variable: "address03"
              type: "input"
              required: true
              placeholder: "例：中野坂上サンブライトツインビル１４階"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```
