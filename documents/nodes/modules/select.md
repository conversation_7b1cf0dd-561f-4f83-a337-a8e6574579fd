# Select Module

The select module allows users to choose one option from a dropdown list. It supports both static options and dynamic options loaded from a crawler.

## Table of Contents

- [Basic Usage](#basic-usage)
- [Field Reference](#field-reference)
  - [Static Options](#static-options-fields)
  - [Dynamic Options with <PERSON><PERSON><PERSON>](#crawler-data-fields)
  - [UI Texts](#ui-texts-fields)
- [Validation Rules](#validation-rules)
- [Examples](#examples)

## Basic Usage

### Static Options

Use this when you have a fixed set of options that don't change.

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "Payment Method",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "Select Payment Method",
          "variable": "payment_method",
          "type": "select",
          "required": true,
          "options": [
            { "text": "Credit Card (No Fee)" },
            { "text": "Bank Transfer (Fee: ¥250)" },
            { "text": "Convenience Store (Fee: ¥300)" }
          ]
        }
      ]
    }
  }
}
```

### Dynamic Options with <PERSON>rawler

Use this when options need to be loaded dynamically from an API endpoint.

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "Dynamic Options",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "Select an Option",
          "variable": "dynamic_option",
          "type": "select",
          "required": true,
          "crawler_data": {
            "task": {
              "class_name": "YourCrawlerClass" // Required
            },
            "inputs": {
              "url": {
                // Required
                "value": "https://example.com/api/options" // Required, must be valid URL
              }
            },
            "outputs": {
              "data": {
                // Required
                "message": "Data loaded successfully", // Required
                "error": "Failed to load options", // Required
                "optionValues": [] // Required, will be populated with options
              }
            }
          },
          "ui_texts": {
            // Optional
            "button": "Load Options", // Default: "データを取得する"
            "loading": "Loading...", // Default: "読み込み中..."
            "error": "Error loading options", // Default: "データの取得に失敗しました。入力内容をご確認ください。"
            "success": "Options loaded", // Default: "データが正常に取得されました"
            "options_suffix": "options" // Default: "オプション"
          },
          "options": [] // Must be empty when using crawler_data
        }
      ]
    }
  }
}
```

## Field Reference

### Static Options Fields

| Field    | Type    | Required | Description                                    |
| -------- | ------- | -------- | ---------------------------------------------- |
| label    | String  | Yes      | The label displayed above the select           |
| variable | String  | Yes      | Variable name for form submission              |
| type     | String  | Yes      | Must be "select"                               |
| required | Boolean | No       | Whether the field is required (default: false) |
| options  | Array   | Yes\*    | Array of `{text: string}` objects              |

### Crawler Data Fields

When using `crawler_data`, these fields are required:

| Field          | Type   | Required | Description                                       |
| -------------- | ------ | -------- | ------------------------------------------------- |
| crawler_data   | Object | Yes      | Container for crawler configuration               |
| - task         | Object | Yes      | Task configuration                                |
| - class_name   | String | Yes      | Name of the crawler class to use                  |
| - inputs       | Object | Yes      | Input parameters for the crawler                  |
| - url          | Object | Yes      | URL configuration                                 |
| - value        | String | Yes      | The URL to fetch options from (must be valid URL) |
| - outputs      | Object | Yes      | Output configuration                              |
| - data         | Object | Yes      | Data structure for the response                   |
| - message      | String | Yes      | Success message to display when options load      |
| - error        | String | Yes      | Error message to display if loading fails         |
| - optionValues | Array  | Yes      | Will contain the loaded options (initially empty) |
| ui_texts       | Object | No       | Custom UI text strings (all fields optional)      |
| options        | Array  | No       | Must be empty array when using crawler_data       |

### UI Texts Fields

Customize the UI text strings (all fields are optional):

| Field          | Type   | Default (JP)                                             | Description                                    |
| -------------- | ------ | -------------------------------------------------------- | ---------------------------------------------- |
| button         | String | "データを取得する"                                       | Text for the load button                       |
| loading        | String | "読み込み中..."                                          | Text shown while loading options               |
| error          | String | "データの取得に失敗しました。入力内容をご確認ください。" | Error message when loading fails               |
| success        | String | "データが正常に取得されました"                           | Success message when options load successfully |
| options_suffix | String | "オプション"                                             | Text shown after the number of loaded options  |

## Validation Rules

### Static Options

1. `label` must be a non-empty string
2. `variable` must be a valid variable name (alphanumeric + underscore)
3. `type` must be "select"
4. `options` must be a non-empty array
5. Each option must be an object with a `text` property (string)

### Crawler Data

1. `crawler_data` is required and must be an object
2. `crawler_data.task` is required and must contain `class_name`
3. `crawler_data.inputs.url.value` is required and must be a valid URL
4. `crawler_data.outputs.data` is required and must contain:
   - `message` (string)
   - `error` (string)
   - `optionValues` (array, will be populated)
5. `options` must be an empty array when using `crawler_data`
6. `ui_texts` (if provided) can only contain the allowed keys

## Examples

### Create Node with Static Options

```graphql
mutation CreateSelectNode {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
      input: {
        label: "Payment Method"
        nodeType: "input"
        body: {
          type: "select"
          settings: [
            {
              label: "Select Payment Method"
              variable: "payment_method"
              type: "select"
              required: true
              options: [
                { text: "Credit Card (No Fee)" }
                { text: "Bank Transfer (Fee: ¥250)" }
              ]
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      id
      label
      nodeType
      body
    }
  }
}
```

### Update Node with Crawler Data

```graphql
mutation UpdateNodeWithCrawler {
  adminsScenariosNodesUpdate(
    input: {
      nodeId: "7c3986ba-05e7-4982-ba6d-a299882c625c"
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "Dynamic Options"
        nodeType: "input"
        body: {
          type: "select"
          settings: [
            {
              label: "Select an Option"
              variable: "dynamic_option"
              type: "select"
              required: true
              crawler_data: {
                task: { class_name: "YourCrawlerClass" }
                inputs: { url: { value: "https://example.com/api/options" } }
                outputs: {
                  data: {
                    message: "Data loaded successfully"
                    error: "Failed to load options"
                    optionValues: []
                  }
                }
              }
              ui_texts: {
                button: "Load Options"
                loading: "Loading..."
                error: "Error loading options"
                success: "Options loaded"
                options_suffix: "options"
              }
              options: []
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      id
      label
      nodeType
      body
    }
  }
}
```

### Error Responses

When validation fails, you'll receive errors with the following structure:

```json
{
  "errors": [
    {
      "message": "Validation failed",
      "extensions": {
        "code": "INVALID_INPUT",
        "details": {
          "setting_0.crawler_data.task": ["can't be blank"],
          "setting_0.crawler_data.inputs.url.value": ["must be a valid URL"],
          "setting_0.options": ["must be empty when crawler_data is present"]
        }
      }
    }
  ]
}
```
