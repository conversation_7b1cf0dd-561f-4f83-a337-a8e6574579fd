# Select Module Documentation

## Tổng quan

Module Select cho phép người dùng chọn một tùy chọn từ danh sách dropdown. Module này hỗ trợ hai loại dữ liệu:

- **Static Options**: <PERSON>h sách tùy chọn cố định được định nghĩa trước
- **Dynamic Options**: Danh sách tùy chọn được tải động từ API thông qua crawler

## <PERSON><PERSON><PERSON> lục

- [Hướng dẫn sử dụng](#hướng-dẫn-sử-dụng)
  - [Tù<PERSON> chọn tĩnh (Static Options)](#tùy-chọn-tĩnh-static-options)
  - [Tùy chọn động với Crawler](#tùy-chọn-động-với-crawler)
- [Tham chiếu trường dữ liệu](#tham-chiếu-trường-dữ-liệu)
  - [<PERSON><PERSON><PERSON> trường dữ liệu tùy chọn tĩnh](#các-trường-dữ-liệu-tùy-chọn-tĩnh)
  - [<PERSON>á<PERSON> trường dữ liệu Crawler](#các-trường-dữ-liệu-crawler)
  - [Tùy chỉnh giao diện người dùng](#tùy-chỉnh-giao-diện-người-dùng)
- [Quy tắc kiểm tra dữ liệu](#quy-tắc-kiểm-tra-dữ-liệu)
- [Xử lý lỗi](#xử-lý-lỗi)

## Hướng dẫn sử dụng

### Tùy chọn tĩnh (Static Options)

![image](../../assets/select_static.png)

Sử dụng khi bạn có một tập hợp tùy chọn cố định không thay đổi.

**Ưu điểm:**

- Đơn giản, dễ cấu hình
- Hiệu suất cao (không cần gọi API)
- Phù hợp với dữ liệu ít thay đổi

**Khi nào sử dụng:**

- Danh sách phương thức thanh toán
- Danh sách quốc gia/tỉnh thành
- Các tùy chọn cố định khác

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "Payment Method",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "Chọn phương thức thanh toán",
          "variable": "payment_method",
          "type": "select",
          "required": true,
          "options": [
            { "text": "Thẻ tín dụng (Miễn phí)" },
            { "text": "Chuyển khoản ngân hàng (Phí: ¥250)" },
            { "text": "Cửa hàng tiện lợi (Phí: ¥300)" }
          ]
        }
      ]
    }
  }
}
```

### Tùy chọn động với Crawler

![image](../../assets/select_before_crawler.png)

![image](../../assets/select_crawler_success.png)

![image](../../assets/select_crawler_failed.png)

Sử dụng khi tùy chọn cần được tải động từ API endpoint.

**Ưu điểm:**

- Dữ liệu luôn cập nhật
- Linh hoạt với nguồn dữ liệu bên ngoài
- Có thể tích hợp với nhiều API khác nhau

**Khi nào sử dụng:**

- Danh sách sản phẩm từ database
- Dữ liệu thay đổi thường xuyên
- Tích hợp với hệ thống bên thứ ba

```json
{
  "scenarioId": "685e6a22-50fb-46ec-bbbf-94dbc87c480b",
  "input": {
    "label": "Tùy chọn động",
    "nodeType": "input",
    "body": {
      "type": "select",
      "settings": [
        {
          "label": "Chọn một tùy chọn",
          "variable": "dynamic_option",
          "type": "select",
          "required": true,
          "crawler_data": {
            "task": {
              "class_name": "YourCrawlerClass" // Bắt buộc: Tên class crawler
            },
            "inputs": {
              "url": {
                "value": "https://example.com/api/options" // Bắt buộc: URL hợp lệ
              }
            },
            "outputs": {
              "data": {
                "message": "Dữ liệu đã được tải thành công", // Bắt buộc
                "error": "Không thể tải tùy chọn", // Bắt buộc
                "optionValues": [] // Bắt buộc: Sẽ chứa dữ liệu tải về
              }
            }
          },
          "ui_texts": {
            // Tùy chọn: Tùy chỉnh text giao diện
            "button": "Tải tùy chọn", // Mặc định: "データを取得する"
            "loading": "Đang tải...", // Mặc định: "読み込み中..."
            "error": "Lỗi khi tải tùy chọn", // Mặc định: "データの取得に失敗しました。入力内容をご確認ください。"
            "success": "Tùy chọn đã được tải", // Mặc định: "データが正常に取得されました"
            "options_suffix": "tùy chọn" // Mặc định: "オプション"
          },
          "options": [] // Phải để trống khi sử dụng crawler_data
        }
      ]
    }
  }
}
```

**Luồng hoạt động của select crawler**

1. Khi người dùng click vào nút "Tải tùy chọn", một request websocket sẽ được gửi đến server
2. Server sẽ nhận dạng dữ liệu vào gọi đến server task runner để thực hiện request
3. Task runner sẽ gọi đến class crawler automation để lấy dữ liệu trả về cho server Bot
4. Server nhận dữ liệu trả về từ task runner và gửi về client qua websocket
5. Client sẽ hiển thị dữ liệu lên giao diện

- Về Cơ bản nó khá giống với cách thức hoạt động của `headless_task` khác ở chỗ
  select with crawler chỉ để lấy data còn headless_task thì dùng để fill data và submit form

## Tham chiếu trường dữ liệu

### Các trường dữ liệu tùy chọn tĩnh

| Trường   | Kiểu dữ liệu | Bắt buộc | Mô tả                                          |
| -------- | ------------ | -------- | ---------------------------------------------- |
| label    | String       | Có       | Nhãn hiển thị phía trên select                 |
| variable | String       | Có       | Tên biến để submit form                        |
| type     | String       | Có       | Phải là "select"                               |
| required | Boolean      | Không    | Trường có bắt buộc hay không (mặc định: false) |
| options  | Array        | Có\*     | Mảng các object `{text: string}`               |

> **Lưu ý:** `options` bắt buộc khi không sử dụng `crawler_data`

### Các trường dữ liệu Crawler

Khi sử dụng `crawler_data`, các trường sau là bắt buộc:

| Trường         | Kiểu dữ liệu | Bắt buộc | Mô tả                                                 |
| -------------- | ------------ | -------- | ----------------------------------------------------- |
| crawler_data   | Object       | Có       | Container chứa cấu hình crawler                       |
| - task         | Object       | Có       | Cấu hình task                                         |
| - class_name   | String       | Có       | Tên class crawler sử dụng                             |
| - inputs       | Object       | Có       | Tham số đầu vào cho crawler                           |
| - outputs      | Object       | Có       | Cấu hình đầu ra                                       |
| - url          | Object       | Có       | Cấu hình URL                                          |
| - value        | String       | Có       | URL để lấy dữ liệu tùy chọn (phải là URL hợp lệ)      |
| - type         | String       | Có       | kiểu dữ liệu                                          |
| - data         | Object       | Có       | Cấu trúc dữ liệu cho response                         |
| - message      | String       | Có       | Thông báo thành công khi tải tùy chọn                 |
| - error        | String       | Có       | Thông báo lỗi khi tải thất bại                        |
| - optionValues | Array        | Có       | Sẽ chứa các tùy chọn được tải (ban đầu để trống)      |
| ui_texts       | Object       | Không    | Tùy chỉnh text giao diện (tất cả trường đều tùy chọn) |

### Tùy chỉnh giao diện người dùng

Tùy chỉnh các chuỗi text giao diện (tất cả trường đều tùy chọn):

| Trường         | Kiểu dữ liệu | Mặc định (JP)                                            | Mô tả                                            |
| -------------- | ------------ | -------------------------------------------------------- | ------------------------------------------------ |
| button         | String       | "データを取得する"                                       | Text cho nút tải dữ liệu                         |
| loading        | String       | "読み込み中..."                                          | Text hiển thị khi đang tải tùy chọn              |
| error          | String       | "データの取得に失敗しました。入力内容をご確認ください。" | Thông báo lỗi khi tải thất bại                   |
| success        | String       | "データが正常に取得されました"                           | Thông báo thành công khi tải tùy chọn thành công |
| options_suffix | String       | "オプション"                                             | Text hiển thị sau số lượng tùy chọn đã tải       |

## Quy tắc kiểm tra dữ liệu

### Static Options

1. **`label`** - Phải là chuỗi không rỗng
2. **`variable`** - Phải là tên biến hợp lệ (chữ cái, số và dấu gạch dưới)
3. **`type`** - Phải là "select"
4. **`options`** - Phải là mảng không rỗng
5. **Mỗi option** - Phải là object có thuộc tính `text` (string)

### Crawler Data

1. **`crawler_data`** - Bắt buộc và phải là object
2. **`crawler_data.task`** - Bắt buộc và phải chứa `class_name`
3. **`crawler_data.inputs.url.value`** - Bắt buộc và phải là URL hợp lệ
4. **`crawler_data.outputs.data`** - Bắt buộc và phải chứa:
   - `message` (string)
   - `error` (string)
   - `optionValues` (array, sẽ được populate)
5. **`options`** - Phải là mảng trống khi sử dụng `crawler_data`
6. **`crawler_data.ui_texts`** - (nếu có) chỉ được chứa các key được phép

### Lưu ý quan trọng khi sử dụng

- **Không thể sử dụng đồng thời `options` và `crawler_data`, khi sử dụng crawler_data thì `options` phải để trống**
- **URL trong `crawler_data` phải trả về dữ liệu đúng format**
- **Class crawler phải tồn tại và được cấu hình đúng**

### Tạo Node với Static Options

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "0338af0c-d425-45f5-bc72-6e5973898be0"
      input: {
        nodeType: "input"
        label: null
        body: {
          type: "select"
          settings: [
            {
              label: "設置希望日"
              variable: "scheduled_date"
              type: "select"
              required: true
              options: [
                { text: "Thẻ tín dụng (Miễn phí)" }
                { text: "Chuyển khoản ngân hàng (Phí: ¥250)" }
                { text: "Cửa hàng tiện lợi (Phí: ¥300)" }
              ]
            }
          ]
        }
      }
    }
  ) {
    clientMutationId
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

### Cập nhật Node với Crawler Data

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "0338af0c-d425-45f5-bc72-6e5973898be0"
      input: {
        nodeType: "input"
        label: null
        body: {
          type: "select"
          settings: [
            {
              label: "設置希望日"
              variable: "scheduled_date"
              type: "select"
              required: true
              options: []
              crawler_data: {
                task: { class_name: "HawaiiWaterDesiredInstallationDate" }
                inputs: {
                  url: {
                    value: "https://www.hawaiiwater.co.jp/regist/index"
                    type: "string"
                  }
                  addressee: { value: "", type: "string" }
                  zipcode: { value: "", type: "string" }
                  prefectures: { value: "", type: "string" }
                }
                outputs: { data: { message: "", error: "", optionValues: [] } }
                ui_texts: {
                  button: "データを取得する"
                  loading: "読み込み中..."
                  error: "データの取得に失敗しました。入力内容をご確認ください。"
                  success: "データが正常に取得されました"
                  options_suffix: "オプション"
                }
              }
            }
          ]
        }
      }
    }
  ) {
    clientMutationId
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

## Xử lý lỗi

### Cấu trúc Error Response

Khi validation thất bại, bạn sẽ nhận được lỗi với cấu trúc sau:

```json
{
  "errors": [
    {
      "message": "Create Failed",
      "locations": [
        {
          "line": 2,
          "column": 5
        }
      ],
      "path": ["adminsScenariosNodesCreate"],
      "status": "unprocessable_entity",
      "code": 422,
      "errors": {
        "setting.task": ["Task を入力してください"],
        "setting.inputs": ["Inputs を入力してください"],
        "setting.outputs": ["Outputs を入力してください"],
        "setting.ui_texts": ["Ui texts 無効なキーが含まれています: load2wing"]
      },
      "reset_data": false
    }
  ],
  "data": {
    "adminsScenariosNodesCreate": null
  }
}
```
