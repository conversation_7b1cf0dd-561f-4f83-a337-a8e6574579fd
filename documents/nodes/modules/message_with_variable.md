# 変数付きメッセージ（Message with Variable）

- [Message ノードについてはこちら](./message.md)

- `Message with Variable` は、submit 後に response を待つためのノードです。

- このメッセージには、エラー時に表示するデフォルトメッセージ `fallback_error` を追加します。

  - `response.error == true` かつ `fallback_error` が設定されている場合 → `fallback_error` の内容を表示する

  - `response.error == true` かつ `fallback_error` が未設定の場合 → `response` の内容を表示する

  - `response.error == false` の場合 → `response` の内容を表示する

---

### メッセージノード作成 Mutation（Create）

```graphql
mutation AdminsScenariosNodesCreate {
  adminsScenariosNodesCreate(
    input: {
      scenarioId: "2cf4ac9e-0162-4677-b69a-23ccac5ed092"
      input: {
        label: "test create"
        nodeType: "message"
        body: {
          settings: [
            {
              type: "text"
              content: ""
              fallback_error: "<カスタムエラーメッセージ>"
              variable: "result_msg"
            }
          ]
        }
      }
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
    }
  }
}
```

### メッセージノード更新 Mutation（Update Message Image）

```graphql
mutation AdminsScenariosNodesUpdate {
  adminsScenariosNodesUpdate(
    input: {
      input: { label: "Test Message" }
      nodeId: "232f00c6-74f8-4315-91bf-1c16b30e51cf"
      scenarioId: "685e6a22-50fb-46ec-bbbf-94dbc87c480b"
    }
  ) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
  }
}
```
