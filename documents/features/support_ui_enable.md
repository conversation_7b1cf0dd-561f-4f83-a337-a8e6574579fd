# Support UI

- Scenario の `support_ui_enable` フラグは、ノード（`name`、`FullName` 、`nameSexBirthday`、`telEmailPassword`、`Address`）を使ったユーザーインターフェースを有効または無効にするために使用されます。
  - このフラグがオンの場合、`support_ui_enable=true`、アプリケーションまたはシステムはユーザーインターフェースのサポートを提供します。
  - このフラグがオフの場合、`support_ui_enable=false`、ユーザーインターフェースのサポートはデフォルトで表示されます。

## Name

![Image](../assets/name_sp_ui.png)

## FullName

![Image](../assets/full_name_sp_ui.png)

## Name Sex Birthday

![Image](../assets/name_sex_birthday.png)

## Tel email password

![Image](../assets/tel_email_password_sp_ui.png)

## Address

### zipcode

![Image](../assets/zipcode_sp_ui.png)

### address 02

![Image](../assets/address02_sp_ui.png)
