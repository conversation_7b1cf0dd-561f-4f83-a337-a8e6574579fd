# Flow when payment method errors

## Diagram

![Diagram](../assets/flow_payment_method_error.png)

## Node use

| Node Type                       | Node Data                                                                                                                                                                               | Note                                                                                                                                                                       |
| ------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `set_value`                     | `{ "node_type": "set_value", "body": { "repeatable": true, "settings": [ { "variable": "payment_method", "value": "クレジット決済(手数料:無料)" } ] } }`                                | Used to reset the value of the variable `payment_method`                                                                                                                   |
| `condition message Credit card` | `{ "node_type": "condition", "label": "Check if Payment error", "body": { "repeatable": true, "type": "condition", "settings": [ { "variable": "result_msg", "value": "エラー" } ] } }` | Check the next node, value approximately matches the value of `result_msg`. In case `result_msg` is not satisfied, please add a condition node for the corresponding flow. |
| `condition message NP`          | `{ "node_type": "condition", "label": "Check if NP error", "body": { "repeatable": true, "type": "condition", "settings": [ { "variable": "result_msg", "value": "後払い" } ] } }`      | Check the next node, value approximately matches the value of `result_msg`.                                                                                                |

## Flow 1

```json
{
  "fetchScenario": {
    "rootNodeUid": "9abd3629-f86a-452f-86f9-f50d1653c039",
    "nodes": [
      {
        "body": {
          "settings": [
            {
              "type": "image",
              "content": "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["9f42516a-530d-4c55-9b1a-0f268b4cfc7f"],
        "nodeType": "message",
        "uid": "9abd3629-f86a-452f-86f9-f50d1653c039"
      },
      {
        "body": {
          "settings": [
            {
              "type": "image",
              "content": "https://iqdum.jp/upload/lp/main_1/img/bnr_oos_last.png"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["2be11dc7-d042-456e-aaf8-05e09287cbba"],
        "nodeType": "message",
        "uid": "9f42516a-530d-4c55-9b1a-0f268b4cfc7f"
      },
      {
        "body": {
          "settings": [
            {
              "type": "text",
              "content": "それでは受付を開始しますね。\n        まずはお客様の電話番号とメールアドレスを入力してください。"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["078423c4-d1cd-4827-941f-7c4d09d861d6"],
        "nodeType": "message",
        "uid": "2be11dc7-d042-456e-aaf8-05e09287cbba"
      },
      {
        "body": {
          "type": "select",
          "settings": [
            {
              "type": "select",
              "label": "商品",
              "options": [
                {
                  "text": "初回300円OFF_IQDUM定期コース｜[初回1本]イクダムハンドクリーム2本"
                },
                {
                  "text": "初回300円OFF_【おまとめ配送】IQDUM定期コース｜イクダムハンドクリーム3本"
                }
              ],
              "required": true,
              "variable": "product"
            }
          ]
        },
        "label": "商品",
        "nextNodeUid": ["f1123d38-88d6-4200-af63-50f3ff0dce81"],
        "nodeType": "input",
        "uid": "078423c4-d1cd-4827-941f-7c4d09d861d6"
      },
      {
        "body": {
          "type": "name",
          "settings": [
            {
              "label": "お名前",
              "layout": "horizontal",
              "ratios": 2,
              "settings": [
                {
                  "type": "input",
                  "label": "姓",
                  "ratio": 1,
                  "required": true,
                  "variable": "sei",
                  "placeholder": "山田"
                },
                {
                  "type": "input",
                  "label": "名",
                  "ratio": 1,
                  "required": true,
                  "variable": "mei",
                  "placeholder": "太郎"
                }
              ]
            },
            {
              "label": "フリガナ",
              "layout": "horizontal",
              "ratios": 2,
              "settings": [
                {
                  "type": "input",
                  "label": "セイ",
                  "ratio": 1,
                  "required": true,
                  "variable": "seifuri",
                  "placeholder": "ヤマダ"
                },
                {
                  "type": "input",
                  "label": "メイ",
                  "ratio": 1,
                  "required": true,
                  "variable": "meifuri",
                  "placeholder": "タロウ"
                }
              ]
            }
          ]
        },
        "label": "お名前",
        "nextNodeUid": ["e6c54c9c-023d-43aa-a92a-ffdea61145c3"],
        "nodeType": "input",
        "uid": "f1123d38-88d6-4200-af63-50f3ff0dce81"
      },
      {
        "body": {
          "type": "text",
          "settings": [
            {
              "type": "input",
              "label": "電話番号",
              "required": true,
              "variable": "tel",
              "placeholder": "例）09011112222"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["8a774700-8fce-44c5-adf9-6ecf7b1f92ba"],
        "nodeType": "input",
        "uid": "7375fffd-003f-44fb-bf2c-32f47b256030"
      },
      {
        "body": {
          "type": "text",
          "settings": [
            {
              "type": "input",
              "label": "メールアドレス",
              "required": true,
              "variable": "mail",
              "placeholder": "例）<EMAIL>"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["7375fffd-003f-44fb-bf2c-32f47b256030"],
        "nodeType": "input",
        "uid": "e6c54c9c-023d-43aa-a92a-ffdea61145c3"
      },
      {
        "body": {
          "type": "text",
          "settings": [
            {
              "type": "input",
              "label": "クーポンコード",
              "required": false,
              "variable": "coupon",
              "placeholder": "クーポンを入力"
            }
          ]
        },
        "label": "クーポン",
        "nextNodeUid": ["e1c6dd37-14a6-44b5-9358-e06cd9cc86be"],
        "nodeType": "input",
        "uid": "9084b50d-7974-418e-bdf4-3bfc91e33cad"
      },
      {
        "body": {
          "type": "address",
          "settings": [
            {
              "type": "input",
              "label": "郵便番号",
              "required": true,
              "variable": "zipcode",
              "placeholder": "1300000"
            },
            {
              "type": "select",
              "label": "都道府県名",
              "required": true,
              "variable": "prefectures"
            },
            {
              "type": "input",
              "label": "市区町村名",
              "required": true,
              "variable": "address01",
              "placeholder": "市区町村名 (千代田区神田神保町)"
            },
            {
              "type": "input",
              "label": "番地・マンション名",
              "required": true,
              "variable": "address02",
              "placeholder": "番地・マンション名 (1-3-5 ○○マンション 201号室)"
            }
          ]
        },
        "label": "住所",
        "nextNodeUid": ["6d5aa4f4-e32e-4c40-b242-585ee9a0844a"],
        "nodeType": "input",
        "uid": "8a774700-8fce-44c5-adf9-6ecf7b1f92ba"
      },
      {
        "body": {
          "type": "select",
          "settings": [
            {
              "type": "select",
              "label": "お支払い方法",
              "options": [
                {
                  "text": "NP後払いwizRT(手数料:250円)"
                },
                {
                  "text": "クレジット決済(手数料:無料)"
                }
              ],
              "required": true,
              "variable": "payment_method"
            }
          ],
          "repeatable": true
        },
        "label": "お支払い方法",
        "nextNodeUid": [
          "1d93a8c7-be45-4c06-b6b5-a6ab5242c578",
          "8075e992-5dc2-4be4-abb2-03d345b6f485"
        ],
        "nodeType": "input",
        "uid": "e1c6dd37-14a6-44b5-9358-e06cd9cc86be"
      },
      {
        "body": {
          "type": "sex_and_birthday",
          "settings": [
            {
              "type": "radio",
              "label": "性別",
              "options": [
                {
                  "text": "男"
                },
                {
                  "text": "女"
                }
              ],
              "required": false,
              "variable": "sex"
            },
            {
              "type": "date",
              "label": "生年月日",
              "layout": "horizontal",
              "ratios": 3,
              "settings": [
                {
                  "type": "select",
                  "label": "日",
                  "ratio": 1,
                  "required": false,
                  "variable": "day"
                },
                {
                  "type": "select",
                  "label": "月",
                  "ratio": 1,
                  "required": false,
                  "variable": "month"
                },
                {
                  "type": "select",
                  "label": "年",
                  "ratio": 1,
                  "required": false,
                  "variable": "year"
                }
              ]
            }
          ]
        },
        "label": "",
        "nextNodeUid": ["9084b50d-7974-418e-bdf4-3bfc91e33cad"],
        "nodeType": "input",
        "uid": "6d5aa4f4-e32e-4c40-b242-585ee9a0844a"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "クレジット決済(手数料:無料)",
              "variable": "payment_method"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["a5ccc7b8-6cde-4f3c-9de0-5bbf706d71a4"],
        "nodeType": "condition",
        "uid": "8075e992-5dc2-4be4-abb2-03d345b6f485"
      },
      {
        "body": {
          "type": "credit_card",
          "settings": [
            {
              "type": "input",
              "label": "カード番号",
              "required": true,
              "variable": "card_number",
              "placeholder": "4897XXXXXXXXXXXX"
            },
            {
              "label": "有効期限",
              "layout": "horizontal",
              "ratios": 2,
              "settings": [
                {
                  "type": "select",
                  "label": "月",
                  "ratio": 1,
                  "required": true,
                  "variable": "card_expired_month"
                },
                {
                  "type": "select",
                  "label": "年",
                  "ratio": 1,
                  "required": true,
                  "variable": "card_expired_year"
                }
              ],
              "variable": "card_expired"
            },
            {
              "type": "input",
              "label": "カード名義(ローマ字氏名)",
              "required": true,
              "variable": "card_name",
              "placeholder": "YAMADA HANAKO"
            }
          ],
          "repeatable": true
        },
        "label": "お名前",
        "nextNodeUid": ["3f277899-dec5-4379-b889-d791d53307eb"],
        "nodeType": "input",
        "uid": "a5ccc7b8-6cde-4f3c-9de0-5bbf706d71a4"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "NP後払いwizRT(手数料:250円)",
              "variable": "payment_method"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["3f277899-dec5-4379-b889-d791d53307eb"],
        "nodeType": "condition",
        "uid": "1d93a8c7-be45-4c06-b6b5-a6ab5242c578"
      },
      {
        "body": {
          "settings": [
            {
              "type": "template",
              "content": {
                "body": "◆<b>注文内容</b>◆\n<b>【商品名】</b> {{product}}\n<b>【お名前】</b> {{sei}}{{mei}}\n<b>【お名前（フリガナ）】</b> {{seifuri}}{{meifuri}}\n<b>【郵便番号】</b> {{zipcode}}\n<b>【住所】</b> {{prefectures}}{{address01}}{{address02}}\n<b>【電話番号】</b> {{tel}}\n<b>【メールアドレス】</b> {{mail}}\n<b>【お支払方法】</b> {{payment_method}}\n",
                "title": "内容確認（枠内をスクロールして最後までご確認ください）\n"
              }
            },
            {
              "type": "policy",
              "content": {
                "body": "第1条(会員)\n\n1. 「会員」とは、当社が定める手続に従い本規約に同意の上、入会の申し込みを行う個人をいいます。\n2. 「会員情報」とは、会員が当社に開示した会員の属性に関する情報および会員の取引に関する履歴等の情報をいいます。 \n3. 本規約は、すべての会員に適用され、登録手続時および登録後にお守りいただく規約です。\n4.顧客情報や履歴を管理するにあたって、登録されたお客様を会員と呼称します。\n5. 会員は、本規約に加え、弊社が管理運営する通信販売用ウェブサイトに記載された各種利用条件(各種手数料の有無、金額、配送方法等を含みますが、これらに限られません。)に従うものとし、これらに変更がある場合には、変更後の各種利用条件がウェブサイトに表示された後は、変更後の条件に従うものとします。\n\n第2条(登録)\n\n1. 会員資格 本規約に同意の上、所定の入会申込みをされたお客様は、所定の登録手続完了後に会員としての資格を有します。会員登録手続は、会員となるご本人が行ってください。代理による登録は一切認められません。なお、過去に会員資格が取り消された方やその他当社が相応しくないと判断した方からの会員申込はお断りする場合があります。  \n2. 会員情報の入力 会員登録手続の際には、入力上の注意をよく読み、所定の入力フォームに必要事項を正確に入力してください。会員情報の登録において、特殊記号・旧漢字・ローマ数字などはご使用になれません。これらの文字が登録された場合は当社にて変更致します。  \n3. パスワードの管理 (1)パスワードは会員本人のみが利用できるものとし、第三者に譲渡・貸与できないものとします。 (2)パスワードは、他人に知られることがないよう定期的に変更する等、会員本人が責任をもって管理してください。 (3)パスワードを用いて当社に対して行われた意思表示は、会員本人の意思表示とみなし、そのために生じる支払等はすべて会員の責任となります。\n\n第3条(変更)\n\n1. 会員は、氏名、住所など当社に届け出た事項に変更があった場合には、速やかに当社に連絡するものとします。 \n2. 変更登録がなされなかったことにより生じた損害について、当社は一切責任を負いません。また、変更登録がなされた場合でも、変更登録前にすでに手続がなされた取引は、変更登録前の情報に基づいて行われますのでご注意ください。\n\n第4条(退会)\n\n会員が退会を希望する場合には、会員本人が退会手続きを行ってください。所定の退会手続の終了後に、退会となります。\n\n第5条(会員資格の喪失及び賠償義務)\n\n1. 会員が、会員資格取得申込の際に虚偽の申告をしたとき、通信販売による代金支払債務を怠ったとき、その他当社が会員として不適当と認める事由があるときは、当社は、会員資格を取り消すことができることとします。 \n2. 会員が、以下の各号に定める行為をしたときは、これにより当社が被った損害を賠償する責任を負います。 (1)会員番号、パスワードを不正に利用すること (2)当ホームページにアクセスして情報を改ざんしたり、当ホームページに有害なコンピュータプログラムを送信するなどして、当社の営業を妨害すること (3)当社が扱う商品の知的所有権を侵害する行為をすること (4)その他、この利用規約に反する行為をすること\n\n第6条(会員情報の取扱い)\n\n1. 当社は、原則として会員情報を会員の事前の同意なく第三者に対して開示することはありません。ただし、次の各号の場合には、会員の事前の同意なく、当社は会員情報その他のお客様情報を開示できるものとします。 (1)法令に基づき開示を求められた場合 (2)当社の権利、利益、名誉等を保護するために必要であると当社が判断した場合 \n2. 会員情報につきましては、当社の「個人情報保護への取組み」に従い、当社が管理します。当社は、会員情報を、会員へのサービス提供、サービス内容の向上、サービスの利用促進、およびサービスの健全かつ円滑な運営の確保を図る目的のために、当社において利用することができるものとします。 \n3. 当社は、会員に対して、メールマガジンその他の方法による情報提供(広告を含みます)を行うことができるものとします。会員が情報提供を希望しない場合は、当社所定の方法に従い、その旨を通知して頂ければ、情報提供を停止します。ただし、本サービス運営に必要な情報提供につきましては、会員の希望により停止をすることはできません。\n\n第7条(禁止事項)\n\n本サービスの利用に際して、会員に対し次の各号の行為を行うことを禁止します。 \n1. 法令または本規約、本サービスご利用上のご注意、本サービスでのお買い物上のご注意その他の本規約等に違反すること \n2. 当社、およびその他の第三者の権利、利益、名誉等を損ねること \n3. 青少年の心身に悪影響を及ぼす恐れがある行為、その他公序良俗に反する行為を行うこと \n4. 他の利用者その他の第三者に迷惑となる行為や不快感を抱かせる行為を行うこと \n5. 虚偽の情報を入力すること \n6. 有害なコンピュータプログラム、メール等を送信または書き込むこと \n7. 当社のサーバその他のコンピュータに不正にアクセスすること \n8. パスワードを第三者に貸与・譲渡すること、または第三者と共用すること \n9. 本サービスを利用して購入したかどうかを問わず、当社の商品を第三者に転売、再販売する行為その他営利目的で利用する行為 \n10. その他当社が不適切と判断すること\n\n第8条(サービスの中断・停止等)\n\n1. 当社は、本サービスの稼動状態を良好に保つために、次の各号(1)～(4)に該当する場合、予告なしに、本サービスの提供全てあるいは一部を停止することがあります。 (1)システムの定期保守および緊急保守のために必要な場合 (2)システムに負荷が集中した場合 (3)火災、停電、第三者による妨害行為などによりシステムの運用が困難になった場合 (4)その他、止むを得ずシステムの停止が必要と当社が判断した場合\n\n第9条(サービスの変更・廃止)\n\n当社は、その判断によりサービスの全部または一部を事前の通知なく、適宜変更・廃止できるものとします。\n\n第10条(免責)\n\n1. 通信回線やコンピュータなどの障害によるシステムの中断・遅滞・中止・データの消失、データへの不正アクセスにより生じた損害、その他当社のサービスに関して会員に生じた損害について、当社は一切責任を負わないものとします。 \n2. 当社は、当社のウェブページ・サーバ・ドメインなどから送られるメール・コンテンツに、コンピュータ・ウィルスなどの有害なものが含まれていないことを保証いたしません。 \n3. 会員が本規約等に違反したことによって生じた損害については、当社は一切責任を負いません。\n\n第11条(本規約の改定)\n\n当社は、本規約を任意に改定できるものとし、また、当社において本規約を補充する規約(以下「補充規約」といいます)を定めることができます。本規約の改定または補充は、改定後の本規約または補充規約を当社所定のサイトに掲示したときにその効力を生じるものとします。この場合、会員は、改定後の規約および補充規約に従うものと致します。\n\n第12条(準拠法、管轄裁判所)\n\n本規約に関して紛争が生じた場合、当社本店所在地を管轄する地方裁判所を第一審の専属的合意管轄裁判所とします。\n",
                "title": "個人情報保護方針\n"
              }
            },
            {
              "type": "button",
              "content": "購入確認画面へ"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["db97e73b-f820-4586-bb4e-a166799422e0"],
        "nodeType": "button",
        "uid": "3f277899-dec5-4379-b889-d791d53307eb"
      },
      {
        "body": {
          "settings": [
            {
              "task": {
                "content": "(function () {\n  window.productFlag = 0;\n  window.postFlag = 0;\n  window.messageFlag = 0;\n\n  var existingIframe = document.getElementById(\"iqdum_iframe\");\n\n  if (existingIframe) {\n    existingIframe.parentNode.removeChild(existingIframe);\n  }\n\n  /**\n   * Sets the value of an input element identified by the given selector.\n   * Triggers the 'blur', 'change', and 'input' events on the element after setting the value.\n   *\n   * @param {Document} doc - The document object.\n   * @param {string} selector - The CSS selector of the input element.\n   * @param {string} val - The value to be set.\n   * @returns {Promise<void>} A promise that resolves once the value is set and events are triggered.\n   */\n  var send_keys = (doc, selector, val) =>\n    new Promise((resolve) => {\n      var _target = doc.querySelector(selector);\n      if (_target) {\n        setTimeout(() => {\n          _target.value = val;\n          [\"blur\", \"change\", \"input\"].forEach((e) => {\n            _target.dispatchEvent(new Event(e, { bubbles: true }));\n          });\n          resolve();\n        }, 100);\n      } else {\n        resolve();\n      }\n    });\n\n  /**\n   * Sends the selected text to a specified element.\n   *\n   * @param {Document} doc - The document object.\n   * @param {string} selector - The CSS selector of the target element.\n   * @param {string} val - The value to be selected.\n   * @returns {Promise<void>} - A promise that resolves when the text is sent.\n   */\n  var send_selected_text = (doc, selector, val) =>\n    new Promise((resolve) => {\n      if (!val) {\n        return;\n      }\n      var _target = doc.querySelector(selector);\n      if (_target) {\n        setTimeout(() => {\n          Array.from(_target.options).filter((x) =>\n            x.textContent.includes(val)\n          )[0].selected = true;\n          _target.dispatchEvent(new Event(\"change\", { bubbles: true }));\n          resolve();\n        }, 100);\n      } else {\n        resolve();\n      }\n    });\n\n  function set_gender_input(doc, selector, sex) {\n    var divElement = doc.querySelector(selector);\n\n    var inputs = divElement.getElementsByTagName(\"input\");\n\n    for (var i = 0; i < inputs.length; i++) {\n      var label = doc.querySelector(`label[for=\"${inputs[i].id}\"]`);\n\n      if (label.textContent === sex) {\n        inputs[i].click();\n        break;\n      }\n    }\n  }\n\n  /**\n   * Waits for an element to be present in the document and returns it.\n   * @param {Document} doc - The document object.\n   * @param {string} selector - The CSS selector of the element to wait for.\n   * @param {number} [delay=300] - The delay in milliseconds between each search attempt.\n   * @param {number} [tries=50] - The maximum number of search attempts.\n   * @returns {Promise<Element|null>} - A promise that resolves with the element if found, or null if not found within the specified number of tries.\n   */\n  function _waitForElement(doc, selector, delay = 300, tries = 50) {\n    const element = doc.querySelector(selector);\n\n    if (!window[`__${selector}`]) {\n      window[`__${selector}`] = 0;\n    }\n\n    function _search() {\n      return new Promise((resolve) => {\n        window[`__${selector}`]++;\n        setTimeout(resolve, delay);\n      });\n    }\n\n    if (element === null) {\n      if (window[`__${selector}`] >= tries) {\n        window[`__${selector}`] = 0;\n        return Promise.resolve(null);\n      }\n\n      return _search().then(() => _waitForElement(doc, selector));\n    } else {\n      return Promise.resolve(element);\n    }\n  }\n\n  // パスワード自動入力\n  function setPasswordInput(doc) {\n    doc.querySelector(\"#order_name01\").addEventListener(\"click\", function () {\n      doc.querySelector(\"#password\").dispatchEvent(new Event(\"blur\"));\n    });\n    doc\n      .querySelector('select[name=\"year\"]')\n      .addEventListener(\"click\", function () {\n        doc.querySelector(\"#password\").dispatchEvent(new Event(\"blur\"));\n      });\n    doc.querySelector(\"#password\").addEventListener(\"blur\", function () {\n      doc.querySelector(\"#password\").value = \"aaaa1234\";\n    });\n  }\n\n  var iframe = null;\n  iframe = document.createElement(\"iframe\");\n  iframe.id = \"iqdum_iframe\";\n  iframe.src = window.parent.location.href;\n  iframe.style.width = \"0\";\n  iframe.style.height = \"0\";\n  iframe.style.left = \"0\";\n  iframe.style.position = \"fixed\";\n  iframe.style.bottom = \"0\";\n  iframe.style.borderRadius = \"10px\";\n  iframe.style.margin = \"10px\";\n  iframe.style.boxShadow = \"rgba(100, 100, 111, 0.2) 0px 7px 29px 0px\";\n  iframe.style.zIndex = \"9999\";\n  document.body.appendChild(iframe);\n  iframe.addEventListener(\"load\", () => run());\n\n  async function run() {\n    var ifm = document.querySelector(\"iframe#iqdum_iframe\");\n    var ifmdoc = ifm.contentWindow.document;\n    var ifmPath = ifm.contentWindow.location.pathname;\n    console.log(ifm.contentWindow.location);\n    try {\n      if (window.productFlag === 0) {\n        console.time(\"fill_data\");\n        await post();\n        console.timeEnd(\"fill_data\");\n      }\n\n      if (window.productFlag == 1 && window.postFlag == 0) {\n        console.time(\"click_button_submit\");\n        if (ifmPath === \"/shopping/confirm.php\") {\n          await confirm_page();\n        } else if (ifmPath === \"/shopping/lp.php\") {\n          await parse_lp_error(ifmdoc);\n        }\n        console.timeEnd(\"click_button_submit\");\n      } else if (window.postFlag == 1 && window.messageFlag == 0) {\n        console.time(\"thanks_page\");\n        await thanks_page();\n        console.timeEnd(\"thanks_page\");\n      }\n    } catch (error) {\n      console.error(\"Error parsing JSON:\", error.message);\n    }\n  }\n\n  async function post() {\n    var ifm = document.querySelector(\"body iframe#iqdum_iframe\");\n    var ifmdoc = ifm.contentWindow.document;\n    var ifmPath = ifm.contentWindow.location.pathname;\n\n    window.scrollTo(0, ifmdoc.body.scrollHeight);\n\n    if (ifmPath === \"/shopping/lp.php\") {\n      // パスワード自動入力\n\n      var res_btn = ifmdoc.querySelector(\".mgt20\");\n      const login = res_btn.style.display == \"none\";\n\n      // product\n      send_selected_text(ifmdoc, \"#product_id\", `v.product`);\n\n      if (!login) {\n        setPasswordInput(ifmdoc);\n        // name\n\n        send_keys(ifmdoc, \"#order_name01\", `v.sei`);\n        send_keys(ifmdoc, \"#order_name02\", `v.mei`);\n\n        send_keys(ifmdoc, \"#order_kana01\", `v.seifuri`);\n        send_keys(ifmdoc, \"#order_kana02\", `v.meifuri`);\n\n        // address\n        send_keys(ifmdoc, \"#order_zip\", `v.zipcode`);\n        send_keys(ifmdoc, \"#order_addr02\", `v.address02`);\n\n        // tel\n        send_keys(ifmdoc, \"input[name='order_tel']\", `v.tel`);\n\n        // email\n        send_keys(ifmdoc, \"#order_email\", `v.mail`);\n\n        // sex\n        if (`v.sex` === \"男\") {\n          set_gender_input(ifmdoc, \"#order_sex_group\", \"男\");\n        } else {\n          set_gender_input(ifmdoc, \"#order_sex_group\", \"女\");\n        }\n\n        // birthday\n        send_selected_text(\n          ifmdoc,\n          \".birth-select select[name='year']\",\n          `v.year`\n        );\n        send_selected_text(\n          ifmdoc,\n          \".birth-select select[name='month']\",\n          `v.month`\n        );\n        send_selected_text(ifmdoc, \".birth-select select[name='day']\", `v.day`);\n\n        ifmdoc.querySelector(\"input[name='password']\").value = \"aaaa1234\";\n      }\n      // coupon\n      send_keys(ifmdoc, \"#coupon_code\", `v.coupon`);\n      // payment\n      await _waitForElement(ifmdoc, \"#payment_id\");\n      await send_selected_text(ifmdoc, \"#payment_id\", `v.payment_method`);\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n\n      // credit\n      if (\"v.payment_method\".includes(\"クレジット\")) {\n        await _waitForElement(\n          ifmdoc,\n          \".gp_paygent_token_input_error.js-validate\"\n        );\n\n        var credit_data = Object.assign({}, window.creditCard);\n\n        var credit_name = credit_data.card_name;\n        var credit_number = credit_data.card_number;\n        var credit_expiry_month = credit_data.card_expired_month;\n        var credit_expiry_year = credit_data.card_expired_year;\n\n        await send_keys(ifmdoc, \"#gp_paygent_token_card_number\", credit_number);\n        await send_keys(\n          ifmdoc,\n          \"#gp_paygent_token_card_expires_month\",\n          credit_expiry_month\n        );\n        await send_keys(\n          ifmdoc,\n          \"#gp_paygent_token_card_expires_year\",\n          credit_expiry_year\n        );\n        await send_keys(ifmdoc, \"#gp_paygent_token_card_name\", credit_name);\n        await new Promise((resolve) => setTimeout(resolve, 1000));\n      }\n\n      await _waitForElement(ifmdoc, \"#confirm_submit_image\");\n      ifmdoc.querySelector(\"#confirm_submit_image\").click();\n\n      await parse_lp_error(ifmdoc);\n      console.log(\"click\");\n      window.productFlag = 1;\n    }\n  }\n\n  async function confirm_page() {\n    var ifm = document.querySelector(\"iframe#iqdum_iframe\");\n    var ifmdoc = ifm.contentWindow.document;\n\n    ifmdoc.querySelector(\".ordercomp_bt\").click();\n    window.postFlag = 1;\n  }\n\n  async function thanks_page() {\n    var ifm = document.querySelector(\"iframe#iqdum_iframe\");\n    var ifmdoc = ifm.contentWindow.document;\n\n    var alertContent = \"\";\n    var textComplete = ifmdoc.querySelector(\"#completetext em\")\n      ? ifmdoc.querySelector(\"#completetext em\").textContent\n      : \"\";\n    var error = ifmdoc.querySelector(\".messagearea p\")\n      ? ifmdoc.querySelector(\".messagearea p\").textContent\n      : \"\";\n\n    var idNumber = ifmdoc.querySelector(\"#completetext p\")\n      ? ifmdoc.querySelector(\"#completetext p\").textContent\n      : \"\";\n\n    alertContent = error + textComplete + idNumber;\n\n    if (alertContent.length !== 0) {\n      var data = { message: `${alertContent}`, error: error != \"\" };\n      window.parent.UnicornCartChatbot.handleResponse(data);\n    }\n    window.messageFlag == 1;\n  }\n\n  async function parse_lp_error(ifmdoc) {\n    await _waitForElement(ifmdoc, \"#alert_errors\");\n    var alert_errors = ifmdoc.querySelector(\"#alert_errors\");\n\n    if (\n      alert_errors &&\n      alert_errors.textContent !== `\\n                        `\n    ) {\n      var textError = alert_errors.textContent;\n\n      if (textError) {\n        var data = { message: `${textError}`, error: true };\n        window.parent.UnicornCartChatbot.handleResponse(data);\n      }\n    }\n  }\n})();\n"
              }
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["876b3209-4772-4129-9fae-d914490dafce"],
        "nodeType": "html_tasks",
        "uid": "db97e73b-f820-4586-bb4e-a166799422e0"
      },
      {
        "body": {
          "settings": [
            {
              "type": "text",
              "content": "注文を処理しています。"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["7a32a43b-d30a-4c27-94c1-4d5dbe93d500"],
        "nodeType": "message",
        "uid": "876b3209-4772-4129-9fae-d914490dafce"
      },
      {
        "body": {
          "settings": [
            {
              "type": "text",
              "content": "少々お待ちください。"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": ["e2893a36-6516-44fd-96d1-822a5b7eeff4"],
        "nodeType": "message",
        "uid": "7a32a43b-d30a-4c27-94c1-4d5dbe93d500"
      },
      {
        "body": {
          "settings": [
            {
              "type": "text",
              "content": "",
              "variable": "result_msg"
            }
          ],
          "repeatable": true
        },
        "label": null,
        "nextNodeUid": [
          "d2a4649c-af5c-4a14-a418-66b5201abce8",
          "bbd9df60-aa3f-4269-a753-4b3375be5794"
        ],
        "nodeType": "message",
        "uid": "e2893a36-6516-44fd-96d1-822a5b7eeff4"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": true,
              "variable": "error"
            }
          ],
          "repeatable": true
        },
        "label": "Check if submit Failed",
        "nextNodeUid": [
          "412b1a5f-baea-458f-b4db-17894493f90e",
          "af11b3e3-c0b3-44fd-8da3-61793d4544fe",
          "2b074812-434a-49f9-85ca-ba5ab250decd"
        ],
        "nodeType": "condition",
        "uid": "bbd9df60-aa3f-4269-a753-4b3375be5794"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "カード登録エラー",
              "variable": "result_msg"
            }
          ],
          "repeatable": true
        },
        "label": "Check if card error",
        "nextNodeUid": ["e1c6dd37-14a6-44b5-9358-e06cd9cc86be"],
        "nodeType": "condition",
        "uid": "412b1a5f-baea-458f-b4db-17894493f90e"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "エラー",
              "variable": "result_msg"
            }
          ],
          "repeatable": true
        },
        "label": "Check if Payment error",
        "nextNodeUid": ["e1c6dd37-14a6-44b5-9358-e06cd9cc86be"],
        "nodeType": "condition",
        "uid": "2b074812-434a-49f9-85ca-ba5ab250decd"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "後払い",
              "variable": "result_msg"
            }
          ],
          "repeatable": true
        },
        "label": "Check if NP error",
        "nextNodeUid": ["e1c6dd37-14a6-44b5-9358-e06cd9cc86be"],
        "nodeType": "condition",
        "uid": "af11b3e3-c0b3-44fd-8da3-61793d4544fe"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "初回300円OFF_IQDUM定期コース｜[初回1本]イクダムハンドクリーム2本",
              "variable": "product"
            }
          ]
        },
        "label": "Check Product ID 9",
        "nextNodeUid": ["03d81d1d-0158-47b3-8a4e-1eed18c8d2f0"],
        "nodeType": "condition",
        "uid": "f7f74c0b-1cc1-47e1-80f5-84aced0447d3"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "初回300円OFF_【おまとめ配送】IQDUM定期コース｜イクダムハンドクリーム3本",
              "variable": "product"
            }
          ]
        },
        "label": "Check Product ID 10",
        "nextNodeUid": ["f5b9dc46-27b6-4818-8dc4-2b9990241f1f"],
        "nodeType": "condition",
        "uid": "b4f03c8a-a64c-4d59-ad46-2aecc0d846b4"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": false,
              "variable": "error"
            }
          ],
          "repeatable": true
        },
        "label": "Check if submit success",
        "nextNodeUid": [
          "f7f74c0b-1cc1-47e1-80f5-84aced0447d3",
          "b4f03c8a-a64c-4d59-ad46-2aecc0d846b4"
        ],
        "nodeType": "condition",
        "uid": "d2a4649c-af5c-4a14-a418-66b5201abce8"
      },
      {
        "body": {
          "type": "radio_button",
          "settings": [
            {
              "type": "radio",
              "options": [
                {
                  "text": "1本無料＆プレゼントをお得にゲットする！"
                },
                {
                  "text": "申し込まない"
                }
              ],
              "required": true,
              "variable": "cv_upsell_1"
            }
          ]
        },
        "label": null,
        "nextNodeUid": [
          "f6d8e445-30a4-4e18-811e-1418d3af92c2",
          "6480f51a-21ee-41d6-915f-f40e3dcd2ffb"
        ],
        "nodeType": "input",
        "uid": "03d81d1d-0158-47b3-8a4e-1eed18c8d2f0"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "1本無料＆プレゼントをお得にゲットする！",
              "variable": "cv_upsell_1"
            }
          ]
        },
        "label": "Check Flow Part 1",
        "nextNodeUid": ["c2d2a407-d0d3-462e-90bf-06460d3b1a8a"],
        "nodeType": "condition",
        "uid": "f6d8e445-30a4-4e18-811e-1418d3af92c2"
      },
      {
        "body": {
          "type": "condition",
          "settings": [
            {
              "value": "申し込まない",
              "variable": "cv_upsell_1"
            }
          ]
        },
        "label": "Check Flow Part 2",
        "nextNodeUid": ["75e00b0c-c763-4869-8f96-c5a41cd5d97a"],
        "nodeType": "condition",
        "uid": "6480f51a-21ee-41d6-915f-f40e3dcd2ffb"
      },
      {
        "body": {
          "type": "radio_button",
          "settings": [
            {
              "type": "radio",
              "options": [
                {
                  "text": "2本無料プレゼントに申し込む"
                },
                {
                  "text": "申し込まない"
                }
              ],
              "required": true,
              "variable": "cv_upsell_2"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["41fda247-f572-4cbb-b3bc-8428b405b735"],
        "nodeType": "input",
        "uid": "c2d2a407-d0d3-462e-90bf-06460d3b1a8a"
      },
      {
        "body": {
          "type": "radio_button",
          "settings": [
            {
              "type": "radio",
              "options": [
                {
                  "text": "初回１本プレゼントを受け取る"
                },
                {
                  "text": "申し込まない"
                }
              ],
              "required": true,
              "variable": "cv_upsell_2"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["41fda247-f572-4cbb-b3bc-8428b405b735"],
        "nodeType": "input",
        "uid": "75e00b0c-c763-4869-8f96-c5a41cd5d97a"
      },
      {
        "body": {
          "type": "radio_button",
          "settings": [
            {
              "type": "radio",
              "options": [
                {
                  "text": "2本無料プレゼントに申し込む"
                },
                {
                  "text": "申し込まない"
                }
              ],
              "required": true,
              "variable": "cv_upsell_1"
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["41fda247-f572-4cbb-b3bc-8428b405b735"],
        "nodeType": "input",
        "uid": "f5b9dc46-27b6-4818-8dc4-2b9990241f1f"
      },
      {
        "body": {
          "settings": [
            {
              "type": "text",
              "content": "",
              "variable": "result_msg_thank_offer"
            }
          ]
        },
        "label": null,
        "nextNodeUid": null,
        "nodeType": "message",
        "uid": "97f5bcee-f47a-4352-8249-7eea79f88f47"
      },
      {
        "body": {
          "settings": [
            {
              "task": {
                "content": "(function () {\n  var iframe = document.getElementById(\"iqdum_iframe\");\n\n  if (!iframe) return;\n\n  iframe.addEventListener(\"load\", () => executeCvUpsell2());\n\n  // Complete Page\n  const executeCvUpsell1 = function () {\n    var ifm = document.querySelector(\"#iqdum_iframe\");\n    var ifmDoc = ifm.contentWindow.document;\n    var ifmLocation = ifm.contentWindow.location;\n\n    console.log(\"upsell 1\", ifmLocation);\n    if (ifmLocation.origin != \"https://iqdum.jp\") return;\n\n    const cvUpsell1 = \"v.cv_upsell_1\";\n    const imgCvUpsell1 = ifmDoc.querySelector(`img[alt='${cvUpsell1}']`);\n\n    if (!imgCvUpsell1) {\n      console.log(\"Not Found Element Img has attribute alt= \", cvUpsell1);\n      return;\n    }\n\n    console.log(\"cv upsell 1 click\");\n    imgCvUpsell1.parentElement.click();\n  };\n\n  executeCvUpsell1();\n\n  // Thank Offer Page\n  const executeCvUpsell2 = async function () {\n    try {\n      var ifm = document.querySelector(\"#iqdum_iframe\");\n      var ifmDoc = ifm.contentWindow.document;\n      var ifmLocation = ifm.contentWindow.location;\n\n      console.log(\"upsell 2\", ifmLocation);\n      if (ifmLocation.origin != \"https://iqdum.jp\") return;\n\n      const cvUpsell2 = () => {\n        const cvUpsell2 = \"v.cv_upsell_2\";\n        const imgCvUpsell2 = ifmDoc.querySelector(`img[alt='${cvUpsell2}']`);\n\n        if (!imgCvUpsell2) {\n          console.log(\"Not Found Element Img has attribute alt= \", cvUpsell2);\n          return;\n        }\n\n        console.log(\"cv upsell 2 click\");\n        imgCvUpsell2.parentElement.click();\n      };\n\n      const formWrapper = ifmDoc.querySelector(\"#formWrapper\");\n      const thxMessage = ifmDoc.querySelector(\"#thxMessage\");\n\n      if (formWrapper) {\n        // plan 08, 01, 02\n        console.log(\"formWrapper click\");\n        formWrapper.querySelector(\"button[type=submit]\").click();\n\n        if (thxMessage.querySelector(\".offerWrap\")) {\n          await waitForElm(ifmDoc, \"#thxMessage\", { \"style.display\": \"\" });\n\n          cvUpsell2();\n        } else {\n          console.log(\"finish\");\n          var tksText = ifmDoc.querySelector(\".texWrapper p\").textContent;\n\n          window.parent.UnicornCartChatbot.handleResponse({\n            message: tksText,\n            error: false,\n          });\n        }\n      } else {\n        if (ifmDoc.querySelector(\".thanksWrapper\")) {\n          cvUpsell2();\n        } else {\n          if (thxMessage && thxMessage.style.display != \"none\") {\n            console.log(\"finish\");\n            var tksText = ifmDoc.querySelector(\".texWrapper p\").textContent;\n\n            window.parent.UnicornCartChatbot.handleResponse({\n              message: tksText,\n              error: false,\n            });\n          }\n        }\n      }\n    } catch (e) {\n      console.log(\"error\", e);\n\n      window.parent.UnicornCartChatbot.handleResponse({\n        message: `${e}`,\n        error: true,\n      });\n    }\n\n    // https://iqdum.jp/gold/thx_program/plan04/07.html?10248\n    // flow 1: no -> plan04/07 -> no -> plan04/06 -> ok\n    // flow 2: no -> plan04/07 -> yes -> plan04/08 -> yes -> plan04/08 ->ok\n    // flow 3: yes -> plan04/01 -> yes -> plan04/01 -> no -> plan04/03 ->ok\n    // flow 4: yes -> plan04/01 -> yes -> plan04/01 -> yes -> plan04/02 -> yes ->  plan04/02 -> ok\n\n    // product ID=10\n    // plan04/02 -> yess ->plan04/02 -> ok\n    // plan04/03-> noo -> plan04/03 -> ok\n  };\n\n  //  Funtion Wait Element\n  function waitForElm(doc, selector, attributes = {}) {\n    const resolvePromise = () => {\n      const element = doc.querySelector(selector);\n\n      if (!element) return false;\n\n      const keys = Object.keys(attributes);\n      let keysLength = keys.length;\n\n      for (let key in attributes) {\n        const expectValue = attributes[key];\n        let currentValue;\n\n        key = key.split(\".\");\n\n        for (let index = 0; index < key.length; index++) {\n          currentValue =\n            index == 0 ? element[key[index]] : currentValue[key[index]];\n        }\n\n        if (expectValue !== currentValue) break;\n\n        keysLength--;\n      }\n\n      return keysLength == 0;\n    };\n\n    return new Promise((resolve) => {\n      if (resolvePromise()) return resolve();\n\n      const observer = new MutationObserver((_mutations) => {\n        if (!resolvePromise()) return;\n\n        observer.disconnect();\n        resolve();\n      });\n\n      observer.observe(doc.body, {\n        attributes: true,\n        childList: true,\n        subtree: true,\n      });\n    });\n  }\n})();\n"
              }
            }
          ]
        },
        "label": null,
        "nextNodeUid": ["97f5bcee-f47a-4352-8249-7eea79f88f47"],
        "nodeType": "html_tasks",
        "uid": "41fda247-f572-4cbb-b3bc-8428b405b735"
      }
    ],
    "id": "45b91ef4-def3-4d04-b081-3b2ba90f264a",
    "ssid": "scenario-9bca046f-9073-400f-8c60-4cb770c0375e-dcba175adf8904083df96a85473d4111",
    "sdata": "",
    "shopId": null
  }
}
```

<!-- TODO: Update -->
