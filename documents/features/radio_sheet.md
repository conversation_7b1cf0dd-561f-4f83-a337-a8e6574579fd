# Scenario

## Node Input

1. Node Input: AGE

- UI

  ![UI](../assets/radio/radio_button_grid.png)

- When the flag `enable_edit_button` is set to `true`, after selecting an option, the UI will change as shown below:
  ![UI](../assets/radio/radio_button_grid_enable_edit_button.png)

- Use `body:type` `radio_button_grid`

- <PERSON><PERSON> sample

```json
{
  "nodeType": "input",
  "body": {
    "type": "radio_button_grid",
    "settings": [
      {
        "variable": "age",
        "required": true,
        "enable_edit_button": true,
        "options": [
          {
            "text": "20代"
          },
          {
            "text": "30代"
          },
          {
            "text": "40代"
          },
          {
            "text": "50代"
          },
          {
            "text": "60代"
          },
          {
            "text": "70代"
          }
        ]
      }
    ]
  }
}
```

2. QA: `現在投資で得たいものは何ですか？`

- UI Sample

![image](../assets/radio/radio_buttons_multi_select.png)

- Use `body:type` `radio_buttons_multi_select`

- <PERSON><PERSON> Sample

```json
{
  "nodeType": "input",
  "body": {
    "type": "radio_buttons_multi_select",
    "settings": [
      {
        "variable": "investment_goals",
        "required": true,
        "options": [
          {
            "text": "インカムゲイン"
          },
          {
            "text": "キャピタルゲイン"
          },
          {
            "text": "インフレ対策"
          },
          {
            "text": "ハワイ不動産"
          },
          {
            "text": "税対策"
          },
          {
            "text": "資産形成"
          },
          {
            "text": "資産分散"
          },
          {
            "text": "特になし"
          }
        ]
      }
    ]
  }
}
```

3. The area and province you live in

```
北海道→表示なし　
東北→青森県、岩手県、秋田県、宮城県、山形県、福島県
関東甲信越→埼玉県、千葉県、東京都、神奈川県、茨城県、栃木県、群馬県、山梨県、長野県、新潟県
東海→静岡県、岐阜県、愛知県、三重県
北陸→富山県、石川県、福井県、新潟県
近畿→滋賀県、京都県、大阪県、兵庫県、奈良県、和歌山県
中国→鳥取県、島根県、岡山県、広島県、山口県
四国→徳島県、香川県、愛媛県、高知県
九州→福岡県、佐賀県、長崎県、熊本県、大分県、宮崎県、鹿児島県
沖縄→表示なし　
海外→表示なし

```

- Flow Node
  ![image](../assets/radio/node_live.png)

  - Node Area Sample Json

    ```json
    {
      "body": {
        "type": "radio_button_grid",
        "settings": [
          {
            "options": [
              {
                "text": "北海道"
              },
              {
                "text": "東北"
              },
              {
                "text": "関東甲信越"
              },
              {
                "text": "東海"
              },
              {
                "text": "北陸"
              },
              {
                "text": "近畿"
              },
              {
                "text": "中国"
              },
              {
                "text": "四国"
              },
              {
                "text": "九州"
              },
              {
                "text": "沖縄"
              },
              {
                "text": "海外"
              }
            ],
            "required": true,
            "variable": "area"
          }
        ],
        "repeatable": true
      },
      "nextNodeUid": [
        "8ff5ba67-9d4b-4f38-9481-69f00e67e686",
        "420db834-df20-45f2-9f7f-f1b6421057b9",
        "48f49d2b-882a-4f7a-b9ea-479404283af1",
        "63ce4f36-c2db-4c52-8332-65d696f4ea37",
        "2d5afffc-ab19-45e2-8360-a2be6392d818",
        "c2cb16f3-4719-4d1f-a472-74db08d486e2",
        "63dc5af1-a535-47b6-a53b-a9340baa249d",
        "7d269680-3dff-4cf9-b254-55d31242d1d5",
        "551fcec6-8057-4c23-b0c3-61b509fb002a",
        "3881ae4d-77e5-46c1-bb44-7269bd0716ab",
        "*************-4604-b43d-bdb79f231ad6"
      ],
      "nodeType": "input",
      "uid": "4233f28c-e028-4908-b863-67102000ee5c"
    }
    ```

  - Node Condition: `東北` Sample Json

    ```json
    {
      "body": {
        "type": "condition",
        "settings": [
          {
            "value": "東北",
            "variable": "area"
          }
        ]
      },
      "label": null,
      "nextNodeUid": ["404ac4ff-b51a-4da7-ae5a-244f90d0a559"],
      "nodeType": "condition",
      "uid": "420db834-df20-45f2-9f7f-f1b6421057b9"
    }
    ```

  - Node Message Sample Json

    ```json
    {
      "body": {
        "settings": [
          {
            "type": "text",
            "content": "どちらの都道府県にお住まいですか？"
          }
        ]
      },
      "label": null,
      "nextNodeUid": ["b8db3511-9018-42ab-a87e-5a5dc0012fe2"],
      "nodeType": "message",
      "uid": "404ac4ff-b51a-4da7-ae5a-244f90d0a559"
    }
    ```

  - Node Prefecture Sample Json

    ```json
    {
      "body": {
        "type": "radio_button_grid",
        "settings": [
          {
            "options": [
              {
                "text": "青森県"
              },
              {
                "text": "岩手県"
              },
              {
                "text": "秋田県"
              },
              {
                "text": "宮城県"
              },
              {
                "text": "山形県"
              },
              {
                "text": "福島県"
              }
            ],
            "required": true,
            "variable": "prefecture"
          }
        ]
      },
      "label": null,
      "nextNodeUid": ["9acf3ab4-9497-4d94-9cde-6260bb5fe0cc"],
      "nodeType": "input",
      "uid": "b8db3511-9018-42ab-a87e-5a5dc0012fe2"
    }
    ```

- This section uses node conditions similar to the PaymentMethod flow. If you don't understand the above, you can check it again.

  - COD → Button Submit
  - Credit → CreditForm → Button Submit

- UI Sample:

  - 北海道

    - Select

    ![image](../assets/radio/北海道.png)

    - Next node

    ![image](../assets/radio/next_node_北海道.png)

  - 東北

    - Select

    ![image](../assets/radio/東北.png)

    - Next node

    ![image](../assets/radio/next_node_東北.png)

  - 関東甲信越

    - Select

    ![image](../assets/radio/関東甲信越.png)

    - Next node

    ![image](../assets/radio/next_node_関東甲信越.png)

4. Node Submit

- UI Sample

![image](../assets/radio/button_v2.png)

- Use `nodeType`: `button_v2`
- Json Sample

```json
{
  "body": {
    "settings": [
      {
        "variable": "agreement1",
        "content": "<a href=\"\">個人情報のお取り扱い</a>個人情報のお取り扱いについて同意します。",
        "type": "checkbox",
        "required": true
      },
      {
        "content": "資料を受け取る",
        "type": "button"
      }
    ]
  },
  "nodeType": "button_v2"
}
```

5. Other Nodes

   The remaining nodes should only use the existing node types.
