# QUERIES

## adminScenario

### 説明

シナリオを表示する

### 使用例

```graphql
query AdminScenario($id: ID!) {
  adminScenario(id: $id) {
    active
    description
    id
    matchType
    matchValue
    name
    rootNodeUid
    supportUiEnable
    userId
    nodes {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                                                     |
| ------------ | -------- | -------------------------------------------------------- |
| id           | String   | 必須パラメータ。シナリオを一意に識別するための ID です。 |

### 出力

| フィールド名 | データ型                                         | 説明                                                   |
| ------------ | ------------------------------------------------ | ------------------------------------------------------ |
| scenario     | [ScenarioType](./payloads.md#AdminsScenarioType) | 指定された ID に対応するシナリオの詳細情報を返します。 |

---

## adminScenarios

### 説明

シナリオのリストを取得する

```graphql
query AdminScenarios($input: PagyInput) {
  adminScenarios(input: $input) {
    metadata
    collection {
      active
      description
      id
      matchType
      matchValue
      name
      rootNodeUid
      supportUiEnable
      userId
      nodes {
        body
        label
        nextNodeUid
        nodeType
        position
        rootNode
        uid
      }
    }
  }
}
```

### 入力

| フィールド名 | データ型                              | 説明 |
| ------------ | ------------------------------------- | ---- |
| input        | [PagyInput](./arguments.md#pagyinput) |      |

### 出力

| フィールド名 | データ型                                                               | 説明                                                           |
| ------------ | ---------------------------------------------------------------------- | -------------------------------------------------------------- |
| collection   | [ScenarioType[]](./payloads.md#AdminsScenarioType)                     | ページング処理後に返されるレコードのリストを表すデータ型です。 |
| metadata     | [Types::BaseScalar](https://graphql-ruby.org/type_definitions/scalars) | ページング情報を返します。                                     |

---

## adminScenarioSetting

### 説明

シナリオ設定を取得する

### 使用例

```graphql
query AdminScenarioSetting($id: ID!, $scenarioId: ID!) {
  adminScenarioSetting(id: $id, scenarioId: $scenarioId) {
    active
    cssCustomize
    description
    id
    javascriptCustomize
    name
    settingType
    generalSettings {
      chatButtonTitle
      chatOperatorImgUrl
      chatOperatorName
      chatWindowPosition
      chatWindowSubtitle
      chatWindowTitle
      confirmationText
      mobileChatWindowPosition
      pcCustomChatWindowHeight
      pcCustomChatWindowWidth
      showButtonClose
      showChatStartButton
      showConfirmationCloseModal
      startChatbotImmediately
    }
    themeCustomizeSettings {
      chatDesignThemeId
      chatWindowBgColor
      customerMsgBodyBgColor
      customerMsgBodyTxtColor
      dateSystemMessageTxtColor
      formBgColor
      formBorderColor
      formBtnBgColor
      formBtnTxtColor
      formInputBorderColor
      headerBgColor
      initiateBtnBgColor
      initiateBtnTxtColor
      messageInputColor
      operatorMsgBodyBgColor
      operatorMsgBodyTxtColor
      operatorNameColor
      optionActiveBgColor
      optionActiveTxtColor
      optionBgColor
      optionTxtColor
      progressBarBgColor
      progressPercentageBgColor
      progressPercentageColor
      themeColor
      titleTxtColor
    }
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                                               |
| ------------ | -------- | -------------------------------------------------- |
| id           | ID       | （必須）操作するシナリオの一意の ID を指定します。 |
| scenarioId   | ID       | （必須）関連付けられたシナリオの ID を指定します。 |

### 出力

| フィールド名     | データ型                                                    | 説明                                                     |
| ---------------- | ----------------------------------------------------------- | -------------------------------------------------------- |
| scenarioSettings | [ScenarioSettings](./payloads.md#AdminsScenarioSettingType) | 指定された ID に基づくシナリオ設定の詳細情報を返します。 |

---

## adminScenarioSettings

### 説明

シナリオ設定のリストを取得する

### 使用例

```graphql
query AdminScenarioSettings($input: PagyInput, $scenarioId: ID!) {
  adminScenarioSettings(input: $input, scenarioId: $scenarioId) {
    metadata
    collection {
      active
      cssCustomize
      description
      id
      javascriptCustomize
      name
      settingType
      generalSettings {
        chatButtonTitle
        chatOperatorImgUrl
        chatOperatorName
        chatWindowPosition
        chatWindowSubtitle
        chatWindowTitle
        confirmationText
        mobileChatWindowPosition
        pcCustomChatWindowHeight
        pcCustomChatWindowWidth
        showButtonClose
        showChatStartButton
        showConfirmationCloseModal
        startChatbotImmediately
      }
      themeCustomizeSettings {
        chatDesignThemeId
        chatWindowBgColor
        customerMsgBodyBgColor
        customerMsgBodyTxtColor
        dateSystemMessageTxtColor
        formBgColor
        formBorderColor
        formBtnBgColor
        formBtnTxtColor
        formInputBorderColor
        headerBgColor
        initiateBtnBgColor
        initiateBtnTxtColor
        messageInputColor
        operatorMsgBodyBgColor
        operatorMsgBodyTxtColor
        operatorNameColor
        optionActiveBgColor
        optionActiveTxtColor
        optionBgColor
        optionTxtColor
        progressBarBgColor
        progressPercentageBgColor
        progressPercentageColor
        themeColor
        titleTxtColor
      }
    }
  }
}
```

### 入力

| フィールド名 | データ型                              | 説明 |
| ------------ | ------------------------------------- | ---- |
| input        | [PagyInput](./arguments.md#pagyinput) |      |

### 出力

| フィールド名 | データ型                                                               | 説明                                                           |
| ------------ | ---------------------------------------------------------------------- | -------------------------------------------------------------- |
| collection   | [ScenarioSettings[]](./payloads.md#AdminsScenarioSettingType)          | ページング処理後に返されるレコードのリストを表すデータ型です。 |
| metadata     | [Types::BaseScalar](https://graphql-ruby.org/type_definitions/scalars) | ページング情報を返します。                                     |

---

## adminUser

### 説明

ユーザーを取得する

### 使用例

```graphql
query AdminUser($id: ID!) {
  adminUser(id: $id) {
    email
    id
    shopId
    userType
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                                                 |
| ------------ | -------- | ---------------------------------------------------- |
| ID           | ID       | （必須）取得したいユーザーの一意の ID を指定します。 |

### 出力

| フィールド名 | データ型                       | 説明                                                 |
| ------------ | ------------------------------ | ---------------------------------------------------- |
| User         | [User](./payloads.md#UserType) | 指定された ID に基づくユーザーの詳細情報を返します。 |

---

## adminUsers

### 説明

ユーザーのリストを表示する

### 使用例

```graphql
query AdminUsers($input: PagyInput) {
  adminUsers(input: $input) {
    metadata
    collection {
      email
      id
      shopId
      userType
    }
  }
}
```

### 入力

| フィールド名 | データ型                              | 説明 |
| ------------ | ------------------------------------- | ---- |
| input        | [PagyInput](./arguments.md#pagyinput) |      |

### 出力

| フィールド名 | データ型                                                               | 説明                                                           |
| ------------ | ---------------------------------------------------------------------- | -------------------------------------------------------------- |
| collection   | [User[]](./payloads.md#UserType)                                       | ページング処理後に返されるレコードのリストを表すデータ型です。 |
| metadata     | [Types::BaseScalar](https://graphql-ruby.org/type_definitions/scalars) | ページング情報を返します。                                     |

---

## FetchScenario

### 説明

チャットボットのシナリオを取得する

### 使用例

```graphql
query fetchScenario(
  $url: String!
  $userId: ID!
  $ssid: String
  $scenarioId: String
) {
  fetchScenario(
    userId: $userId
    url: $url
    ssid: $ssid
    scenarioId: $scenarioId
  ) {
    rootNodeUid
    supportUiEnable
    nodes {
      body
      label
      nextNodeUid
      nodeType
      uid
    }
    id
    ssid
    sdata
    shopId
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                               |
| ------------ | -------- | ---------------------------------- |
| userId       | String   | （必須）ユーザーの一意の ID を指定 |
| url          | String   | （必須）関連付けられる URL         |
| ssid         | String   | 任意のセッション ID                |
| scenarioId   | String   | 任意のシナリオ ID                  |

### 出力

| フィールド名    | データ型                         | 説明 |
| --------------- | -------------------------------- | ---- |
| ssid            | String                           |      |
| sdata           | String                           |      |
| rootNodeUid     | String                           |      |
| supportUiEnable | Boolean                          |      |
| shopId          | String                           |      |
| nodes           | [Node[]](./payloads.md#NodeType) |      |

---

## ScheduledDateOption

### 説明

予定日オプションを取得する

### 使用例

```graphql
query scheduledDateOption($daysAfterCurrent: String, $rangeDays: String) {
  scheduledDateOption(
    daysAfterCurrent: $daysAfterCurrent
    rangeDays: $rangeDays
  )
}
```

### 入力

| フィールド名     | データ型                                                                                                                                                                                                                                                                                                                           | 説明 |
| ---------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---- |
| daysAfterCurrent | This represents the number of days from the current date to the selected date. For example, if days_after_current is "4", it means the selected date will be the current date plus 4 days.                                                                                                                                         |      |
| rangeDays        | This is the time span (in days) for which you want to generate a series of dates starting from the selected date (the date after adding days_after_current). For example, if range_days is "18", it means that starting from the selected date (after adding days_after_current), you will have an 18-day period to choose a date. |      |

### 出力

Return array dates

## GetKanaName

### 説明

Convert Name

### 使用例

```graphql
query getKanaName($fullName: String!) {
  getKanaName(fullName: $fullName) {
    first
    firstKana
    last
    lastKana
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                     |
| ------------ | -------- | ------------------------ |
| fullName     | String   | 完全な名前を入力します。 |

### 出力

| フィールド名 | データ型 | 説明                         |
| ------------ | -------- | ---------------------------- |
| first        | String   | 姓の部分（最初の名前）。     |
| firstKana    | String   | 姓の部分（カタカナで表記）。 |
| last         | String   | 名の部分（最終の名前）。     |
| lastKana     | String   | 名の部分（カタカナで表記）。 |

---

## FetchScenarioSetting

### 説明

各シナリオに対するシナリオ設定を取得する

### 使用例

```graphql
query fetchScenarioSetting($userId: ID!, $url: String!, $scenarioId: String) {
  fetchScenarioSetting(userId: $userId, url: $url, scenarioId: $scenarioId) {
    scenarioDesignSetting {
      chatDesignThemeId
      chatWindowBgColor

      cssCustomize
      javascriptCustomize

      customerMsgBodyBgColor
      customerMsgBodyTxtColor

      dateSystemMessageTxtColor

      formBgColor
      formBorderColor
      formBtnBgColor
      formBtnTxtColor
      formInputBorderColor

      headerBgColor
      titleTxtColor

      initiateBtnBgColor
      initiateBtnTxtColor

      messageInputColor

      operatorMsgBodyBgColor
      operatorMsgBodyTxtColor
      operatorNameColor

      optionActiveBgColor
      optionActiveTxtColor
      optionBgColor
      optionTxtColor

      themeColor
    }
    scenarioGeneralSetting {
      chatButtonTitle

      chatOperatorImgUrl
      chatOperatorName

      chatWindowPosition
      mobileChatWindowPosition

      chatWindowTitle
      chatWindowSubtitle
      confirmationText

      pcCustomChatWindowHeight
      pcCustomChatWindowWidth

      showButtonClose
      showChatStartButton
      startChatbotImmediately
      showConfirmationCloseModal
    }
  }
}
```

### 入力

| フィールド名 | データ型 | 説明                                                                                      |
| ------------ | -------- | ----------------------------------------------------------------------------------------- |
| userId       | String   | ユーザーの一意の識別子。システム内でユーザーを識別するために使用されます。                |
| url          | String   | 対象となる URL。ユーザーがアクセスしているウェブページの URL を指定します。               |
| scenarioId   | String   | シナリオの ID。シナリオに関連するデータを特定するための識別子です。これは任意の入力です。 |

### 出力

| フィールド名           | データ型                                                              | 説明                                                                                                               |
| ---------------------- | --------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| scenarioDesignSetting  | [ScenarioDesignSettingType](./payloads.md#ScenarioSettingGeneralType) | シナリオのデザイン設定に関する情報を含むオブジェクト。ユーザーインターフェースの見た目に関連する設定を管理します。 |
| scenarioGeneralSetting | [ScenarioGeneralSettingType](./payloads.md#ScenarioSettingDesignType) | シナリオの一般的な設定に関する情報を含むオブジェクト。シナリオの動作や動作の設定を管理します。                     |

---

## Tasks

### 説明

タスクリストを取得する

### 使用例

```graphql
query Tasks($input: PagyInput) {
  tasks(input: $input) {
    metadata
    collection {
      className
      description
      id
    }
  }
}
```

### 入力

| フィールド名 | データ型                              | 必須  |
| ------------ | ------------------------------------- | ----- |
| input        | [PagyInput](./arguments.md#Pagyinput) | false |

### 出力

| フィールド名 | データ型                                                               | 説明                                                           |
| ------------ | ---------------------------------------------------------------------- | -------------------------------------------------------------- |
| collection   | [Tasks[]](./payloads.md#TaskType)                                      | ページング処理後に返されるレコードのリストを表すデータ型です。 |
| metadata     | [Types::BaseScalar](https://graphql-ruby.org/type_definitions/scalars) | ページング情報を返します。                                     |

---
