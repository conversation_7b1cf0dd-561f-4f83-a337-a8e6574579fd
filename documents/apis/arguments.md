# ARGUMENTS

## PagyInput

### タイプの説明

これはページングに関する情報を含む引数です。

### タイプの諸속と値

| フィールド名 | データ型                      | 説明                             |
| ------------ | ----------------------------- | -------------------------------- |
| page         | Integer                       | 現在のページ                     |
| per_page     | Integer                       | 1 ページあたりのレコード数       |
| q            | [SearchParams](#SearchParams) | データフィルタリングに関する情報 |

---

## AdminsScenarioSettingsCreateInput

### タイプの諸속と値

| 引数名     | データ型                                                  | 説明                   |
| ---------- | --------------------------------------------------------- | ---------------------- |
| input      | [AdminsScenarioSettingInput](#AdminsScenarioSettingInput) | シナリオ設定の入力情報 |
| scenarioId | String                                                    | （必須）シナリオ ID    |

## AdminsScenarioSettingsDestroyInput

| 引数名     | データ型 | 説明                                |
| ---------- | -------- | ----------------------------------- |
| id         |          | （必須）削除する設定の ID。         |
| scenarioId |          | （必須）設定が属するシナリオの ID。 |

## AdminsScenarioSettingsUpdateInput

### タイプの諸속と値

| 引数名     | データ型                                                  | 説明                   |
| ---------- | --------------------------------------------------------- | ---------------------- |
| input      | [AdminsScenarioSettingInput](#AdminsScenarioSettingInput) | シナリオ設定の入力情報 |
| scenarioId | String                                                    | （必須） シナリオ ID   |

## AdminsScenarioSettingInput

### タイプの諸속と値

| 引数名                 | データ型                                                                                | 説明                                                                             |
| ---------------------- | --------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------- |
| active                 | Boolean                                                                                 | この設定が有効か無効かを示すフラグ。true は有効、false は無効                    |
| description            | String                                                                                  | シナリオの説明や詳細情報を記述するフィールド。                                   |
| name                   | String                                                                                  | シナリオ設定の名前。                                                             |
| settingType            | String                                                                                  | シナリオ設定の種類を示す文字列(`general`, `design`)                              |
| cssCustomize           | String                                                                                  | シナリオの CSS カスタマイズを定義するための文字列。                              |
| javascriptCustomize    | String                                                                                  | シナリオの JavaScript カスタマイズを定義するための文字列。                       |
| generalSettings        | [AdminsScenarioSettingsGeneralSettingInput](#AdminsScenarioSettingsGeneralSettingInput) | 一般的なシナリオ設定を定義するための入力データ。                                 |
| themeCustomizeSettings | [AdminsScenarioSettingsDesignSettingInput](#AdminsScenarioSettingsDesignSettingInput)   | シナリオのデザインやテーマに関連するカスタマイズ設定を定義するための入力データ。 |

## AdminsScenarioSettingsGeneralSettingInput

### タイプの諸속と値

| 引数名                     | データ型 | 説明                                                                                              |
| -------------------------- | -------- | ------------------------------------------------------------------------------------------------- |
| chatButtonTitle            | String   | チャットボタンのタイトル。ユーザーがクリックするボタンに表示されるテキスト。                      |
| chatOperatorImgUrl         | String   | チャットオペレーターの画像 URL。オペレーターのアイコン画像のリンク。                              |
| chatOperatorName           | String   | チャットオペレーターの名前。オペレーターを識別するための名前。                                    |
| chatWindowPosition         | String   | チャットウィンドウの表示位置。                                                                    |
| chatWindowSubtitle         | String   | チャットウィンドウのサブタイトル。ウィンドウ内で表示される補足情報。                              |
| chatWindowTitle            | String   | チャットウィンドウのタイトル。ウィンドウ上部に表示されるタイトル。                                |
| confirmationText           | String   | チャット開始前に表示される確認メッセージ。ユーザーに対して確認を求めるテキスト。                  |
| mobileChatWindowPosition   | String   | モバイル用のチャットウィンドウの表示位置。                                                        |
| pcCustomChatWindowHeight   | String   | PC 用のカスタムチャットウィンドウの高さ。                                                         |
| pcCustomChatWindowWidth    | String   | PC 用のカスタムチャットウィンドウの幅。                                                           |
| showButtonClose            | Boolean  | チャットウィンドウの閉じるボタンを表示するかどうかを示すフラグ。`true` で表示、`false` で非表示。 |
| showChatStartButton        | Boolean  | チャット開始ボタンを表示するかどうかを示すフラグ。true で表示、`false` で非表示。                 |
| showConfirmationCloseModal | Boolean  | 確認メッセージを表示後にモーダルを閉じるかどうかを示すフラグ。`true` で閉じる。                   |
| startChatbotImmediately    | Boolean  | チャットボットを即座に開始するかどうかを示すフラグ。`true` で開始。                               |

## AdminsScenarioSettingsDesignSettingInput

| 引数名                    | データ型 | 説明                                                                                   |
| ------------------------- | -------- | -------------------------------------------------------------------------------------- |
| chatDesignThemeId         | String   | チャットのデザインテーマの ID。チャットの外観を設定するための ID。                     |
| chatWindowBgColor         | String   | チャットウィンドウの背景色。通常のチャットウィンドウの背景色。                         |
| customerMsgBodyBgColor    | String   | 顧客メッセージの本体背景色。顧客が送信したメッセージの背景色。                         |
| customerMsgBodyTxtColor   | String   | 顧客メッセージのテキスト色。顧客が送信したメッセージの文字色。                         |
| dateSystemMessageTxtColor | String   | システムメッセージの日付のテキスト色。システムから送信されるメッセージの日付部分の色。 |
| formBgColor               | String   | フォームの背景色。フォーム入力欄の背景色。                                             |
| formBorderColor           | String   | フォームの枠線の色。フォーム入力欄の外枠の色。                                         |
| formBtnBgColor            | String   | フォームボタンの背景色。フォーム内の送信ボタンの背景色。                               |
| formBtnTxtColor           | String   | フォームボタンのテキスト色。フォーム内の送信ボタンの文字色。                           |
| formInputBorderColor      | String   | フォーム入力欄の枠線色。フォームの入力フィールドの枠線色。                             |
| headerBgColor             | String   | ヘッダーの背景色。チャットウィンドウのヘッダー部分の背景色。                           |
| initiateBtnBgColor        | String   | チャット開始ボタンの背景色。ユーザーが最初にクリックするボタンの色。                   |
| initiateBtnTxtColor       | String   | チャット開始ボタンのテキスト色。ボタンに表示される文字の色。                           |
| messageInputColor         | String   | メッセージ入力欄の色。ユーザーがメッセージを入力するテキストボックスの色。             |
| operatorMsgBodyBgColor    | String   | オペレーターのメッセージ本体の背景色。オペレーターが送信したメッセージの背景色。       |
| operatorMsgBodyTxtColor   | String   | オペレーターのメッセージテキスト色。オペレーターが送信したメッセージの文字色。         |
| operatorNameColor         | String   | オペレーターの名前の色。チャット内でオペレーターの名前を表示するための色。             |
| optionActiveBgColor       | String   | 選択肢がアクティブなときの背景色。ユーザーが選択できるオプションの背景色。             |
| optionActiveTxtColor      | String   | 選択肢がアクティブなときの文字色。選択可能なオプションの文字色。                       |
| optionBgColor             | String   | 選択肢の背景色。ユーザーが選択できるオプションの背景色。                               |
| optionTxtColor            | String   | 選択肢の文字色。オプション内の文字の色。                                               |
| progressBarBgColor        | String   | プログレスバーの背景色。処理中の進捗状況を示すバーの背景色。                           |
| progressPercentageBgColor | String   | プログレスバー内の進捗部分の背景色。進捗を示すバーの色。                               |
| progressPercentageColor   | String   | プログレスバー内の進捗パーセンテージの色。進捗のパーセンテージを示すテキストの色。     |
| themeColor                | String   | チャット全体のテーマカラー。チャットシステムの基調色。                                 |
| titleTxtColor             | String   | タイトルのテキスト色。チャットウィンドウ内のタイトル部分の文字色。                     |

## AdminsScenariosCreateInput

| 引数名 | データ型                                    | 説明                                                                                           |
| ------ | ------------------------------------------- | ---------------------------------------------------------------------------------------------- |
| input  | [AdminsScenarioInput](#AdminsScenarioInput) | シナリオの設定に必要な情報を含む入力オブジェクトです。シナリオの作成または更新時に使用します。 |

## AdminsScenariosUpdateInput

| 引数名 | データ型                                    | 説明                                                                                           |
| ------ | ------------------------------------------- | ---------------------------------------------------------------------------------------------- |
| id     | String                                      | （必須）                                                                                       |
| input  | [AdminsScenarioInput](#AdminsScenarioInput) | シナリオの設定に必要な情報を含む入力オブジェクトです。シナリオの作成または更新時に使用します。 |

## AdminsScenariosDestroyInput

| 引数名 | データ型 | 説明                                                                                        |
| ------ | -------- | ------------------------------------------------------------------------------------------- |
| id     | String   | （必須）シナリオの ID。ユニークな識別子として使用され、シナリオを特定するために使われます。 |

## AdminsScenarioInput

| 引数名          | データ型 | 説明                                                                                                                    |
| --------------- | -------- | ----------------------------------------------------------------------------------------------------------------------- |
| active          | Boolean  | シナリオがアクティブかどうかを示すフラグ。`true` でアクティブ、`false` で非アクティブ。                                 |
| description     | String   | シナリオの説明。シナリオの内容や目的を簡潔に記述します。                                                                |
| matchType       | Enum     | マッチタイプ。`match` または `include` のいずれかの値を取ります。`match` は完全一致、`include` は部分一致を意味します。 |
| matchValue      | String   | マッチ値。シナリオが発動するために必要な URL の初期値。                                                                 |
| name            | String   | シナリオの名前。シナリオを識別するための一意な名前。                                                                    |
| rootNodeUid     | String   | ルートノードの UID。シナリオが開始される最初のノードの UID。                                                            |
| supportUiEnable | Boolean  | UI サポートが有効かどうかを示すフラグ。`true` で UI が有効、`false` で無効。                                            |

- `matchType`: これは Enum 型で、2 つの値を取ります：
  - `match`: matchValue の値と完全一致する URL を検索します。
  - `include`: matchValue の値が URL に含まれている場合に一致とみなします。完全一致は必要ありません。

## AdminsScenariosNodesCreateInput

| 引数名     | データ型                                  | 説明                                                                                 |
| ---------- | ----------------------------------------- | ------------------------------------------------------------------------------------ |
| input      | [ScenariosNodeInput](#ScenariosNodeInput) | （必須）シナリオノードを作成するための入力データ。ノードの詳細設定が含まれます。     |
| scenarioId | String                                    | （必須）ノードが関連付けられるシナリオの ID。シナリオを識別するためのユニークな ID。 |

## AdminsScenariosNodesDestroyInput

| 引数名     | データ型 | 説明                                                                                  |
| ---------- | -------- | ------------------------------------------------------------------------------------- |
| nodeId     | String   | （必須）削除するノードの ID。シナリオ内でノードを一意に識別するための ID。            |
| scenarioId | String   | （必須）ノードが関連するシナリオの ID。ノードがどのシナリオに属しているかを示します。 |

## AdminsScenariosNodesUpdateInput

| 引数名     | データ型                                  | 説明                                                                                  |
| ---------- | ----------------------------------------- | ------------------------------------------------------------------------------------- |
| nodeId     | String                                    | （必須）更新するノードの ID。シナリオ内でノードを一意に識別するための ID。            |
| scenarioId | String                                    | （必須）ノードが関連するシナリオの ID。ノードがどのシナリオに属しているかを示します。 |
| input      | [ScenariosNodeInput](#ScenariosNodeInput) | ノードの更新情報を含む入力データ。ノードの新しい設定が含まれます。                    |

## ScenariosNodeInput

| 引数名      | データ型 | 説明                                                                                              |
| ----------- | -------- | ------------------------------------------------------------------------------------------------- |
| rootNode    | Boolean  | このノードがルートノードかどうかを示すフラグ。`true` でルートノード、`false` でそれ以外のノード。 |
| nodeType    | String   | （必須）ノードのタイプ。                                                                          |
| nextNodeUid | String   | 次に進むノードの UID。ノードが遷移する先を示す ID です。                                          |
| label       | String   | ノードのラベル。ノードを識別するための名前やラベルです。                                          |
| body        | Jsonb    | ノードの内容。JSON 形式で保存されたデータを含むことができます。                                   |

- Enum `nodeType`
  - `message (0)`: ユーザーにメッセージを表示するノードのタイプです。
  - `input (10)`: ユーザーからの入力を受け付けるノードのタイプです。
  - `condition (20)`: 条件を評価し、その結果に基づいてアクションを分岐させるノードのタイプです。
  - `button (30)`: ユーザーに選択肢を提供するボタンを作成するノードのタイプです。
  - `html_tasks (40)`: HTML コンテンツを表示したり、HTML に関連するタスクを実行するノードのタイプです。
  - `headless_tasks (50)`: ユーザーインターフェースなしでバックエンドのタスクを実行するノードのタイプです。
  - `set_value (60)`: シナリオ内の変数に値を設定するノードのタイプです。

## ScenariosHandleResponseInput

| 引数名 | データ型                                                        | 説明                                                                       |
| ------ | --------------------------------------------------------------- | -------------------------------------------------------------------------- |
| data   | [BaseScalar](https://graphql-ruby.org/type_definitions/scalars) | 処理するデータ、汎用的なデータ型。                                         |
| ssid   | String                                                          | （必須） セッション ID、ユーザーのセッションを識別するために使用されます。 |
| userId | ID                                                              | （必須） ユーザー ID、システム内のユーザーを一意に識別するための ID。      |

## AdminsUsersUpdateInput

| 引数名 | データ型                            | 説明                             |
| ------ | ----------------------------------- | -------------------------------- |
| id     | String                              | （必須）ユーザーの一意な識別子。 |
| input  | [AdminsUserInput](#AdminsUserInput) | 更新するユーザーの情報。         |

## AdminsUsersCreateInput

| 引数名 | データ型                            | 説明                   |
| ------ | ----------------------------------- | ---------------------- |
| input  | [AdminsUserInput](#AdminsUserInput) | 新しいユーザーの情報。 |

## AdminsUsersDestroyInput

| 引数名 | データ型 | 説明                             |
| ------ | -------- | -------------------------------- |
| id     | String   | （必須）ユーザーの一意な識別子。 |

## AdminsUserInput

| 引数名   | データ型 | 説明                                                         |
| -------- | -------- | ------------------------------------------------------------ |
| email    | String   | （必須）ユーザーのメールアドレス。                           |
| password | String   | （必須）ユーザーのパスワード。                               |
| shopId   | String   | （必須）ユーザーが関連付けられたショップの ID。              |
| userType | String   | （必須）ユーザーの種類。'admin' または 'member' のいずれか。 |

## AdminsAuthsSignInInput

| 引数名     | データ型 | 説明                                                                                   |
| ---------- | -------- | -------------------------------------------------------------------------------------- |
| email      | String   | （必須）ログインするためのユーザーのメールアドレス。                                   |
| password   | String   | （必須）ユーザーのログインパスワード。                                                 |
| rememberMe | Boolean  | （必須）ログイン状態を保持するかどうかを示すフラグ。 true で保持、false で保持しない。 |

## SearchParams

| Predicate              | Description                                          | Notes                                                                                                  |
| ---------------------- | ---------------------------------------------------- | ------------------------------------------------------------------------------------------------------ |
| `*_eq`                 | equal                                                |                                                                                                        |
| `*_not_eq`             | not equal                                            |                                                                                                        |
| `*_matches`            | matches with LIKE                                    | e.g. `q[email_matches]=%@gmail.com`                                                                    |
| `*_does_not_match`     | does not match with LIKE                             |                                                                                                        |
| `*_matches_any`        | Matches any                                          |                                                                                                        |
| `*_matches_all`        | Matches all                                          |                                                                                                        |
| `*_does_not_match_any` | Does not match any                                   |                                                                                                        |
| `*_does_not_match_all` | Does not match all                                   |                                                                                                        |
| `*_lt`                 | less than                                            |                                                                                                        |
| `*_lteq`               | less than or equal                                   |                                                                                                        |
| `*_gt`                 | greater than                                         |                                                                                                        |
| `*_gteq`               | greater than or equal                                |                                                                                                        |
| `*_present`            | not null and not empty                               | Only compatible with string columns. Example: `q[name_present]=1` (SQL: col is not null AND col != '') |
| `*_blank`              | is null or empty                                     | (SQL: col is null OR col = '')                                                                         |
| `*_null`               | is null                                              |                                                                                                        |
| `*_not_null`           | is not null                                          |                                                                                                        |
| `*_in`                 | match any values in array                            | e.g. `q[name_in][]=Alice&q[name_in][]=Bob`                                                             |
| `*_not_in`             | match none of values in array                        |                                                                                                        |
| `*_lt_any`             | Less than any                                        | SQL: `col < value1 OR col < value2`                                                                    |
| `*_lteq_any`           | Less than or equal to any                            |                                                                                                        |
| `*_gt_any`             | Greater than any                                     |                                                                                                        |
| `*_gteq_any`           | Greater than or equal to any                         |                                                                                                        |
| `*_lt_all`             | Less than all                                        | SQL: `col < value1 AND col < value2`                                                                   |
| `*_lteq_all`           | Less than or equal to all                            |                                                                                                        |
| `*_gt_all`             | Greater than all                                     |                                                                                                        |
| `*_gteq_all`           | Greater than or equal to all                         |                                                                                                        |
| `*_not_eq_all`         | none of values in a set                              |                                                                                                        |
| `*_start`              | Starts with                                          | SQL: `col LIKE 'value%'`                                                                               |
| `*_not_start`          | Does not start with                                  |                                                                                                        |
| `*_start_any`          | Starts with any of                                   |                                                                                                        |
| `*_start_all`          | Starts with all of                                   |                                                                                                        |
| `*_not_start_any`      | Does not start with any of                           |                                                                                                        |
| `*_not_start_all`      | Does not start with all of                           |                                                                                                        |
| `*_end`                | Ends with                                            | SQL: `col LIKE '%value'`                                                                               |
| `*_not_end`            | Does not end with                                    |                                                                                                        |
| `*_end_any`            | Ends with any of                                     |                                                                                                        |
| `*_end_all`            | Ends with all of                                     |                                                                                                        |
| `*_not_end_any`        | Does not end with any of                             |                                                                                                        |
| `*_not_end_all`        | Does not end with all of                             |                                                                                                        |
| `*_cont`               | Contains value                                       | uses LIKE                                                                                              |
| `*_cont_any`           | Contains any of                                      |                                                                                                        |
| `*_cont_all`           | Contains all of                                      |                                                                                                        |
| `*_not_cont`           | Does not contain                                     |                                                                                                        |
| `*_not_cont_any`       | Does not contain any of                              |                                                                                                        |
| `*_not_cont_all`       | Does not contain all of                              |                                                                                                        |
| `*_i_cont`             | Contains value with case insensitive                 | uses ILIKE                                                                                             |
| `*_i_cont_any`         | Contains any of values with case insensitive         |                                                                                                        |
| `*_i_cont_all`         | Contains all of values with case insensitive         |                                                                                                        |
| `*_not_i_cont`         | Does not contain with case insensitive               |                                                                                                        |
| `*_not_i_cont_any`     | Does not contain any of values with case insensitive |                                                                                                        |
| `*_not_i_cont_all`     | Does not contain all of values with case insensitive |                                                                                                        |
| `*_true`               | is true                                              |                                                                                                        |
| `*_false`              | is false                                             |                                                                                                        |
