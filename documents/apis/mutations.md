# MUTATIONS

## adminsScenarioSettingsCreate

### 説明

シナリオの設定を作成し、管理者がボットのインターフェースと動作を調整できるようにします。

### 使用例

```graphql
mutation AdminsScenarioSettingsCreate(
  $input: AdminsScenarioSettingsCreateInput!
) {
  adminsScenarioSettingsCreate(input: $input) {
    message
    scenarioSetting {
      active
      cssCustomize
      description
      id
      javascriptCustomize
      name
      settingType
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                              | 説明 |
| ------------ | ------------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenarioSettingsCreateInput](./arguments.md#AdminsScenarioSettingsCreateInput) |      |

### 出力

| フィールド名    | データ型                                                             | 説明                         |
| --------------- | -------------------------------------------------------------------- | ---------------------------- |
| message         | String                                                               | 処理の結果に関するメッセージ |
| scenarioSetting | [AdminsScenarioSettingType](./payloads.md#AdminsScenarioSettingType) | シナリオ設定の詳細情報       |

---

## adminsScenarioSettingsDestroy

### 説明

シナリオ設定を削除する

### 使用例

```graphql
mutation AdminsScenarioSettingsDestroy(
  $input: AdminsScenarioSettingsDestroyInput!
) {
  adminsScenarioSettingsDestroy(input: $input) {
    message
  }
}
```

### 入力

| フィールド名 | データ型                                                                                | 必須 |
| ------------ | --------------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenarioSettingsDestroyInput](./arguments.md#AdminsScenarioSettingsDestroyInput) | true |

### 出力

| フィールド名 | データ型 | 説明                       |
| ------------ | -------- | -------------------------- |
| message      | String   | 処理結果に関するメッセージ |

---

## adminsScenarioSettingsUpdate

### 説明

シナリオ設定を更新する

### 使用例

```graphql
mutation AdminsScenarioSettingsUpdate(
  $input: AdminsScenarioSettingsUpdateInput!
) {
  adminsScenarioSettingsUpdate(input: $input) {
    message
    scenarioSetting {
      active
      cssCustomize
      description
      id
      javascriptCustomize
      name
      settingType
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                              | 必須 |
| ------------ | ------------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenarioSettingsUpdateInput](./arguments.md#AdminsScenarioSettingsUpdateInput) | true |

### 出力

| フィールド名    | データ型                                                             | 説明                       |
| --------------- | -------------------------------------------------------------------- | -------------------------- |
| message         | String                                                               | 処理結果に関するメッセージ |
| scenarioSetting | [AdminsScenarioSettingType](./payloads.md#AdminsScenarioSettingType) | シナリオ設定の詳細情報     |

---

## adminsScenariosCreate

### 説明

シナリオを追加する

### 使用例

```graphql
mutation AdminsScenariosCreate($input: AdminsScenariosCreateInput) {
  adminsScenariosCreate(input: $input) {
    message
    scenario {
      active
      description
      id
      matchType
      matchValue
      name
      rootNodeUid
      supportUiEnable
      userId
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                | 必須 |
| ------------ | ----------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosCreateInput](./arguments.md#AdminsScenariosCreateInput) | true |

### 出力

| フィールド名 | データ型                               | 説明                       |
| ------------ | -------------------------------------- | -------------------------- |
| message      | String                                 | 処理結果に関するメッセージ |
| scenario     | [Scenario](./payloads.md#ScenarioType) | シナリオの詳細情報         |

---

## adminsScenariosUpdate

### 説明

シナリオを更新する.

### 使用例

```graphql
mutation AdminsScenariosUpdate($input: AdminsScenariosUpdateInput) {
  adminsScenariosUpdate(input: $input) {
    message
    scenario {
      active
      description
      id
      matchType
      matchValue
      name
      rootNodeUid
      supportUiEnable
      userId
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                | 必須 |
| ------------ | ----------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosUpdateInput](./arguments.md#AdminsScenariosUpdateInput) | true |

### 出力

| フィールド名 | データ型                               | 説明                       |
| ------------ | -------------------------------------- | -------------------------- |
| message      | String                                 | 処理結果に関するメッセージ |
| scenario     | [Scenario](./payloads.md#ScenarioType) | シナリオの詳細情報         |

---

## adminsScenariosDestroy

### 説明

シナリオを削除する

### 使用例

```graphql
mutation AdminsScenariosDestroy($input: AdminsScenariosDestroyInput!) {
  adminsScenariosDestroy(input: $input) {
    message
  }
}
```

### 入力

| フィールド名 | データ型                                                                  | 必須 |
| ------------ | ------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosDestroyInput](./arguments.md#AdminsScenariosDestroyInput) | true |

### 出力

| フィールド名 | データ型 | 説明                       |
| ------------ | -------- | -------------------------- |
| message      | String   | 処理結果に関するメッセージ |

---

## adminsScenariosNodesCreate

### 説明

シナリオにノードを追加する.

### 使用例

```graphql
mutation AdminsScenariosNodesCreate($input: AdminsScenariosNodesCreateInput!) {
  adminsScenariosNodesCreate(input: $input) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                          | 必須 |
| ------------ | --------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosNodesCreateInput](./arguments.md#AdminsScenariosNodesCreateInput) | true |

### 出力

| フィールド名 | データ型                       | 説明                       |
| ------------ | ------------------------------ | -------------------------- |
| message      | String                         | 処理結果に関するメッセージ |
| node         | [Node](./payloads.md#NodeType) | ノードの詳細情報           |

---

## adminsScenariosNodesUpdate

### 説明

シナリオのノードを更新する

### 使用例

```graphql
mutation AdminsScenariosNodesUpdate($input: AdminsScenariosNodesUpdateInput!) {
  adminsScenariosNodesUpdate(input: $input) {
    message
    node {
      body
      label
      nextNodeUid
      nodeType
      position
      rootNode
      uid
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                          | 必須 |
| ------------ | --------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosNodesUpdateInput](./arguments.md#AdminsScenariosNodesUpdateInput) | true |

### 出力

| フィールド名 | データ型                       | 説明                       |
| ------------ | ------------------------------ | -------------------------- |
| message      | String                         | 処理結果に関するメッセージ |
| node         | [Node](./payloads.md#NodeType) | ノードの詳細情報           |

---

## adminsScenariosNodesDestroy

### 説明

ノードを削除する.

### 使用例

```graphql
mutation AdminsScenariosNodesDestroy(
  $input: AdminsScenariosNodesDestroyInput!
) {
  adminsScenariosNodesDestroy(input: $input) {
    message
  }
}
```

### 入力

| フィールド名 | データ型                                                                            | 必須 |
| ------------ | ----------------------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosNodesDestroyInput](./arguments.md#AdminsScenariosNodesDestroyInput) | true |

### 出力

| フィールド名 | データ型 | 説明                       |
| ------------ | -------- | -------------------------- |
| message      | String   | 処理結果に関するメッセージ |

---

## adminsScenariosUpdate

### 説明

シナリオを更新する

### 使用例

```graphql
mutation AdminsScenariosUpdate($input: AdminsScenariosUpdateInput!) {
  adminsScenariosUpdate(input: $input) {
    message
    scenario {
      active
      description
      id
      matchType
      matchValue
      name
      rootNodeUid
      supportUiEnable
      userId
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                                | 必須 |
| ------------ | ----------------------------------------------------------------------- | ---- |
| input        | [AdminsScenariosUpdateInput](./arguments.md#AdminsScenariosUpdateInput) | true |

### 出力

| フィールド名 | データ型                               | 説明                       |
| ------------ | -------------------------------------- | -------------------------- |
| message      | String                                 | 処理結果に関するメッセージ |
| scenario     | [Scemario](./payloads.md#ScenarioType) | シナリオに関する詳細情報   |

---

## adminsSignIn

### 説明

CMS にサインインする.

### 使用例

```graphql
mutation AdminsSignIn($input: AdminsAuthsSignInInput!) {
  adminsSignIn(input: $input) {
    message
    token
  }
}
```

### 入力

| フィールド名 | データ型                                                        | 必須 |
| ------------ | --------------------------------------------------------------- | ---- |
| input        | [AdminsAuthsSignInInput](./arguments.md#AdminsAuthsSignInInput) | true |

### 出力

| フィールド名 | データ型 | 説明                                     |
| ------------ | -------- | ---------------------------------------- |
| message      | String   | 処理結果に関するメッセージ               |
| token        | String   | アクセストークンまたは認証トークンの情報 |

## adminsUsersCreate

### 説明

このミューテーションは、CMS に新しいユーザーを作成するためのものです。管理者はこのミューテーションを使用して、システムに新しいユーザーを追加することができます。

### 使用例

```graphql
mutation AdminsUsersCreate($input: AdminsUsersCreateInput!) {
  adminsUsersCreate(input: $input) {
    message
    user {
      email
      id
      shopId
      userType
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                        | 必須 |
| ------------ | --------------------------------------------------------------- | ---- |
| input        | [AdminsUsersCreateInput](./arguments.md#AdminsUsersCreateInput) | true |

### 出力

| フィールド名 | データ型                       | 説明                         |
| ------------ | ------------------------------ | ---------------------------- |
| message      | String                         | 処理結果に関するメッセージ   |
| user         | [User](./payloads.md#UserType) | 作成されたユーザーの詳細情報 |

---

## adminsUsersDestroy

### 説明

このミューテーションは、CMS からユーザーを削除するためのものです。管理者はこのミューテーションを使用して、システムから既存のユーザーを削除することができます。

### 使用例

```graphql
mutation adminsUsersDestroy($input: AdminsUsersDestroyInput!) {
  adminsUsersDestroy(input: $input) {
    message
  }
}
```

### 入力

| フィールド名 | データ型                                                          | 必須 |
| ------------ | ----------------------------------------------------------------- | ---- |
| input        | [AdminsUsersDestroyInput](./arguments.md#AdminsUsersDestroyInput) | true |

### 出力

| フィールド名 | データ型 | 説明                       |
| ------------ | -------- | -------------------------- |
| message      | String   | 処理結果に関するメッセージ |

---

## adminsUsersUpdate

### 説明

このミューテーションは、CMS のユーザー情報を更新するためのものです。管理者はこのミューテーションを使用して、既存のユーザーの情報を変更することができます。

### 使用例

```graphql
mutation AdminsUsersUpdate($input: AdminsUsersUpdateInput!) {
  adminsUsersUpdate(input: $input) {
    message
    user {
      email
      id
      shopId
      userType
    }
  }
}
```

### 入力

| フィールド名 | データ型                                                        | 必須 |
| ------------ | --------------------------------------------------------------- | ---- |
| input        | [AdminsUsersUpdateInput](./arguments.md#AdminsUsersUpdateInput) | true |

### 出力

| フィールド名 | データ型                       | 説明                         |
| ------------ | ------------------------------ | ---------------------------- |
| message      | String                         | 処理結果に関するメッセージ   |
| user         | [User](./payloads.md#UserType) | 作成されたユーザーの詳細情報 |

---

## scenariosHandleResponseResult

### 説明

この機能は、WebSocket を介してメッセージを処理します。WebSocket 接続を通じて送信されたメッセージを受信し、適切なレスポンスを返します。これにより、シナリオの結果をリアルタイムで処理し、Chatbot クライアントに対して反応を返すことができます。

### 使用例

```graphql
mutation scenariosHandleResponseResult($input: ScenariosHandleResponseInput!) {
  scenariosHandleResponseResult(input: { input: $input }) {
    message
  }
}
```

### 入力

| フィールド名 | データ型                                                                    | 必須 |
| ------------ | --------------------------------------------------------------------------- | ---- |
| input        | [ScenariosHandleResponseInput](./arguments.md#ScenariosHandleResponseInput) | true |

### 出力

| フィールド名 | データ型 | 説明                       |
| ------------ | -------- | -------------------------- |
| message      | String   | 処理結果に関するメッセージ |

---
