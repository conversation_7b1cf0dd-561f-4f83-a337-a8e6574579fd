# QUERIES AND MUTATIONS

このドキュメントは、プロジェクトに関連するすべての質問についての情報を提供します。入力、出力、クエリの使用方法に関する注釈が含まれています。

## 目次

- [Queries](./queries.md)

  - Admin

    - [adminScenario](./queries.md#adminScenario)
    - [adminScenarioSetting](./queries.md#adminScenarioSetting)
    - [adminScenarioSettings](./queries.md#adminScenarioSettings)
    - [adminScenarios](./queries.md#adminScenarios)
    - [adminUser](./queries.md#adminUser)
    - [adminUsers](./queries.md#adminUsers)

  - Public

    - [fetchScenario](./queries.md#FetchScenario)
    - [fetchScenarioSetting](./queries.md#FetchScenarioSetting)
    - [getKanaName](./queries.md#GetKanaName)
    - [scheduledDateOption](./queries.md#ScheduledDateOption)
    - [tasks](./queries.md#Tasks)

- [Mutations](./mutations.md)
  - Admin
    - [adminsScenarioSettingsCreate](./mutations.md#adminsScenarioSettingsCreate)
    - [adminsScenarioSettingsDestroy](./mutations.md#adminsScenarioSettingsDestroy)
    - [adminsScenarioSettingsUpdate](./mutations.md#adminsScenarioSettingsUpdate)
    - [adminsScenariosCreate](./mutations.md#adminsScenariosCreate)
    - [adminsScenariosDestroy](./mutations.md#adminsScenariosDestroy)
    - [adminsScenariosNodesCreate](./mutations.md#adminsScenariosNodesCreate)
    - [adminsScenariosNodesDestroy](./mutations.md#adminsScenariosNodesDestroy)
    - [adminsScenariosNodesUpdate](./mutations.md#adminsScenariosNodesUpdate)
    - [adminsSignIn](./mutations.md#adminsSignIn)
    - [adminsUsersCreate](./mutations.md#adminsUsersCreate)
    - [adminsUsersDestroy](./mutations.md#adminsUsersDestroy)
    - [adminsUsersUpdate](./mutations.md#adminsUsersUpdate)
  - Public
    - [scenariosHandleResponseResult](./mutations.md#scenariosHandleResponseResult)
