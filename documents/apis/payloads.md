# PAYLOADS

## PagyType

### タイプの説明

これはページング処理の結果として返されるデータ型です。通常、 [PagyInput](./arguments.md#pagyinput) という入力引数とともに使用されます。

### タイプの諸속と値

| フィールド名 | データ型                                                                                     | 説明                                                           |
| ------------ | -------------------------------------------------------------------------------------------- | -------------------------------------------------------------- |
| collection   | [Collection](https://www.rubydoc.info/gems/activerecord-collections/ActiveRecord/Collection) | ページング処理後に返されるレコードのリストを表すデータ型です。 |
| metadata     | [Types::BaseScalar](https://graphql-ruby.org/type_definitions/scalars)                       | ページング情報を返します。                                     |

---

## UserType

### タイプの説明

ユーザーデータ型

### タイプの諸属性と値

| フィールド名 | データ型 | 説明                     |
| ------------ | -------- | ------------------------ |
| email        | String   | ユーザーのメールアドレス |
| id           | ID       | ユーザーの ID            |
| shopId       | String   | ショップ ID              |
| userType     | String   | ユーザータイプ           |

---

## AdminsScenarioType

### タイプの説明

管理者のシナリオデータ型

### タイプの諸属性と値

| フィールド名    | データ型              | 説明                 |
| --------------- | --------------------- | -------------------- |
| active          | Boolean               | アクティブかどうか   |
| description     | String                | 説明                 |
| id              | ID                    | シナリオの ID        |
| matchType       | String                | マッチタイプ         |
| matchValue      | String                | マッチの値           |
| name            | String                | 名前                 |
| nodes           | [NodeType](#NodeType) | ノードのリスト       |
| rootNodeUid     | String                | ルートノード UID     |
| supportUiEnable | Boolean               | サポート UI の有効化 |
| userId          | String                | ユーザー ID          |

---

## NodeType

### タイプの説明

ノードデータ型

### タイプの諸属性と値

| フィールド名 | データ型                                                            | 説明                    |
| ------------ | ------------------------------------------------------------------- | ----------------------- |
| body         | [BaseScalar](<(https://graphql-ruby.org/type_definitions/scalars)>) | ノードの本文            |
| label        | String                                                              | ノードのラベル          |
| nextNodeUid  | [String]                                                            | 次のノードの UID リスト |
| nodeType     | String                                                              | ノードのタイプ          |
| rootNode     | Boolean                                                             | ルートノードかどうか    |
| uid          | ID                                                                  | ノード ID               |

---

## ScenarioType

### タイプの説明

シナリオデータ型

### タイプの諸属性と値

| フィールド名    | データ型              | 説明                 |
| --------------- | --------------------- | -------------------- |
| id              | String                | シナリオ ID          |
| nodes           | [NodeType](#NodeType) | ノードのリスト       |
| rootNodeUid     | String                | ルートノードの UID   |
| sdata           | String                | セッションデータ     |
| shopId          | String                | ショップ ID          |
| ssid            | String                | セッション ID        |
| supportUiEnable | Boolean               | サポート UI の有効化 |

---

## CommonSettingInterface

### タイプの説明

共通設定インターフェース

### タイプの諸属性と値

| フィールド名        | データ型 | 説明                          |
| ------------------- | -------- | ----------------------------- |
| cssCustomize        | String   | カスタマイズされた CSS        |
| javascriptCustomize | String   | カスタマイズされた JavaScript |
| description         | String   | 設定の説明                    |
| active              | Boolean  | アクティブかどうか            |
| name                | String   | 設定の名前                    |
| id                  | String   | 設定の ID                     |
| settingType         | String   | 設定タイプ                    |

---

## ScenarioSettingGeneralType

### タイプの説明

シナリオ設定の一般設定データ型

### タイプの諸属性と値

| 引数名                     | データ型 | 説明                                                                                              |
| -------------------------- | -------- | ------------------------------------------------------------------------------------------------- |
| chatButtonTitle            | String   | チャットボタンのタイトル。ユーザーがクリックするボタンに表示されるテキスト。                      |
| chatOperatorImgUrl         | String   | チャットオペレーターの画像 URL。オペレーターのアイコン画像のリンク。                              |
| chatOperatorName           | String   | チャットオペレーターの名前。オペレーターを識別するための名前。                                    |
| chatWindowPosition         | String   | チャットウィンドウの表示位置。                                                                    |
| chatWindowSubtitle         | String   | チャットウィンドウのサブタイトル。ウィンドウ内で表示される補足情報。                              |
| chatWindowTitle            | String   | チャットウィンドウのタイトル。ウィンドウ上部に表示されるタイトル。                                |
| confirmationText           | String   | チャット開始前に表示される確認メッセージ。ユーザーに対して確認を求めるテキスト。                  |
| mobileChatWindowPosition   | String   | モバイル用のチャットウィンドウの表示位置。                                                        |
| pcCustomChatWindowHeight   | String   | PC 用のカスタムチャットウィンドウの高さ。                                                         |
| pcCustomChatWindowWidth    | String   | PC 用のカスタムチャットウィンドウの幅。                                                           |
| showButtonClose            | Boolean  | チャットウィンドウの閉じるボタンを表示するかどうかを示すフラグ。`true` で表示、`false` で非表示。 |
| showChatStartButton        | Boolean  | チャット開始ボタンを表示するかどうかを示すフラグ。true で表示、`false` で非表示。                 |
| showConfirmationCloseModal | Boolean  | 確認メッセージを表示後にモーダルを閉じるかどうかを示すフラグ。`true` で閉じる。                   |
| startChatbotImmediately    | Boolean  | チャットボットを即座に開始するかどうかを示すフラグ。`true` で開始。                               |

---

## AdminsScenarioSettingType

### タイプの説明

管理者用シナリオ設定データ型

### タイプの諸属性と値

| フィールド名           | データ型                                          | 説明                          |
| ---------------------- | ------------------------------------------------- | ----------------------------- |
| cssCustomize           | String                                            | カスタマイズされた CSS        |
| javascriptCustomize    | String                                            | カスタマイズされた JavaScript |
| description            | String                                            | 設定の説明                    |
| active                 | Boolean                                           | アクティブかどうか            |
| name                   | String                                            | 設定の名前                    |
| id                     | String                                            | 設定の ID                     |
| settingType            | String                                            | 設定タイプ                    |
| generalSettings        | [GeneralSettingType](#ScenarioSettingGeneralType) | 一般設定                      |
| themeCustomizeSettings | [DesignSettingType](#ScenarioSettingDesignType)   | デザイン設定                  |

---

## ScenarioSettingDesignType

### タイプの説明

シナリオ設定のデザイン設定データ型

### タイプの諸属性と値

| 引数名                    | データ型 | 説明                                                                                   |
| ------------------------- | -------- | -------------------------------------------------------------------------------------- |
| chatDesignThemeId         | String   | チャットのデザインテーマの ID。チャットの外観を設定するための ID。                     |
| chatWindowBgColor         | String   | チャットウィンドウの背景色。通常のチャットウィンドウの背景色。                         |
| customerMsgBodyBgColor    | String   | 顧客メッセージの本体背景色。顧客が送信したメッセージの背景色。                         |
| customerMsgBodyTxtColor   | String   | 顧客メッセージのテキスト色。顧客が送信したメッセージの文字色。                         |
| dateSystemMessageTxtColor | String   | システムメッセージの日付のテキスト色。システムから送信されるメッセージの日付部分の色。 |
| formBgColor               | String   | フォームの背景色。フォーム入力欄の背景色。                                             |
| formBorderColor           | String   | フォームの枠線の色。フォーム入力欄の外枠の色。                                         |
| formBtnBgColor            | String   | フォームボタンの背景色。フォーム内の送信ボタンの背景色。                               |
| formBtnTxtColor           | String   | フォームボタンのテキスト色。フォーム内の送信ボタンの文字色。                           |
| formInputBorderColor      | String   | フォーム入力欄の枠線色。フォームの入力フィールドの枠線色。                             |
| headerBgColor             | String   | ヘッダーの背景色。チャットウィンドウのヘッダー部分の背景色。                           |
| initiateBtnBgColor        | String   | チャット開始ボタンの背景色。ユーザーが最初にクリックするボタンの色。                   |
| initiateBtnTxtColor       | String   | チャット開始ボタンのテキスト色。ボタンに表示される文字の色。                           |
| messageInputColor         | String   | メッセージ入力欄の色。ユーザーがメッセージを入力するテキストボックスの色。             |
| operatorMsgBodyBgColor    | String   | オペレーターのメッセージ本体の背景色。オペレーターが送信したメッセージの背景色。       |
| operatorMsgBodyTxtColor   | String   | オペレーターのメッセージテキスト色。オペレーターが送信したメッセージの文字色。         |
| operatorNameColor         | String   | オペレーターの名前の色。チャット内でオペレーターの名前を表示するための色。             |
| optionActiveBgColor       | String   | 選択肢がアクティブなときの背景色。ユーザーが選択できるオプションの背景色。             |
| optionActiveTxtColor      | String   | 選択肢がアクティブなときの文字色。選択可能なオプションの文字色。                       |
| optionBgColor             | String   | 選択肢の背景色。ユーザーが選択できるオプションの背景色。                               |
| optionTxtColor            | String   | 選択肢の文字色。オプション内の文字の色。                                               |
| progressBarBgColor        | String   | プログレスバーの背景色。処理中の進捗状況を示すバーの背景色。                           |
| progressPercentageBgColor | String   | プログレスバー内の進捗部分の背景色。進捗を示すバーの色。                               |
| progressPercentageColor   | String   | プログレスバー内の進捗パーセンテージの色。進捗のパーセンテージを示すテキストの色。     |
| themeColor                | String   | チャット全体のテーマカラー。チャットシステムの基調色。                                 |
| titleTxtColor             | String   | タイトルのテキスト色。チャットウィンドウ内のタイトル部分の文字色。                     |

---

## TaskType

### タイプの説明

タスクタイプ

### タイプの諸属性と値

| フィールド名 | データ型 | 説明      |
| ------------ | -------- | --------- |
| className    | String   | クラス名  |
| description  | String   | 説明      |
| id           | String   | タスク ID |
