# API DOCUMENTATION

このフォルダは、プロジェクト内のすべての API に関する説明と使い方のガイドです。

## フォルダ構成

フォルダ構成は以下の通りです:

```bash
apis/                        # APIの説明フォルダ
|- arguments.md              # プロジェクト内のすべての情報引数の説明
|- mutations.md              # プロジェクトのすべてのミューテーションの説明
|- payloads.md               # プロジェクト内のすべての情報ペイロードの説明
|- queries_and_mutations.md  # 全てのクエリとミューテーションの総合
|- queries.md                # プロジェクトのすべてのクエリーの説明
|- README.md
```

## CMS Design

![Design](../assets/cms.png)
