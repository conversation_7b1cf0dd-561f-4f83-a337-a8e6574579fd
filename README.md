## Staging Release Commands

- `make release-stg-server`: Build and deploy the server in the Staging environment
- `make release-stg-anycable-rpc`: Build and deploy AnyCable RPC in the Staging environment
- `make release-stg-runner`: Deploy the Runner in the Staging environment

## Production Release Commands

- `make release-prod-server`: Build and deploy the server in the Production environment
- `make release-prod-anycable-rpc`: Build and deploy AnyCable RPC in the Production environment
- `make release-prod-runner`: Deploy the Runner in the Production environment
