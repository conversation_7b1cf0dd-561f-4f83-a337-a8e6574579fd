# == Schema Information
#
# Table name: users
#
#  id                 :uuid             not null, primary key
#  email              :string           not null
#  encrypted_password :string           not null
#  lock_version       :integer          not null
#  user_type          :integer          default("admin"), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  shop_id            :string
#
# Indexes
#
#  index_users_on_email  (email) UNIQUE
#
class User < ApplicationRecord
  devise :database_authenticatable

  has_many :scenarios, dependent: :destroy

  enum user_type: {
    admin: 0,
    member: 5,
  }, _prefix: true

  def jwt_payload
    {
      sub: id,
      type: :user,
    }
  end
end
