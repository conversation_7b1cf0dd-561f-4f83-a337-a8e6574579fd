class ChatDesignTheme < ActiveHash::Base
  include ActiveHash::Associations
  fields :id,
         :theme_color,
         :initiate_btn_bg_color,
         :initiate_btn_txt_color,
         :header_bg_color,
         :title_txt_color,
         :chat_window_bg_color,
         :date_system_message_txt_color,
         :message_input_color,
         :operator_name_color,
         :operator_msg_body_bg_color,
         :operator_msg_body_txt_color,
         :customer_msg_body_bg_color,
         :customer_msg_body_txt_color,
         :option_bg_color,
         :option_active_bg_color,
         :option_txt_color,
         :option_active_txt_color,
         :form_bg_color,
         :form_border_color,
         :form_input_border_color,
         :form_btn_bg_color,
         :form_btn_txt_color,
         :progress_bar_bg_color,
         :progress_percentage_bg_color,
         :progress_percentage_color

  add id: 1,
      theme_color: "green",
      initiate_btn_bg_color: "#df3772",
      initiate_btn_txt_color: "#ffffff",
      header_bg_color: "#df3772",
      title_txt_color: "#ffffff",
      chat_window_bg_color: "#f0d6d6",
      date_system_message_txt_color: "#9e9e9e",
      message_input_color: "#333333",
      operator_name_color: "#9e9e9e",
      operator_msg_body_bg_color: "#df3772",
      operator_msg_body_txt_color: "#ffffff",
      customer_msg_body_bg_color: "#ffffff",
      customer_msg_body_txt_color: "#333333",
      option_bg_color: "#f0d6d6",
      option_active_bg_color: "#df3772",
      option_txt_color: "#333333",
      option_active_txt_color: "#ffffff",
      form_bg_color: "#ffffff",
      form_border_color: "#ffffff",
      form_input_border_color: "#df3772",
      form_btn_bg_color: "#df3772",
      form_btn_txt_color: "#ffffff",
      progress_bar_bg_color: "#ffffff",
      progress_percentage_bg_color: "#df3772",
      progress_percentage_color: "#ffffff"
end
