# == Schema Information
#
# Table name: nodes
#
#  id            :uuid             not null, primary key
#  body          :jsonb
#  label         :string
#  lock_version  :integer          not null
#  next_node_uid :string           is an Array
#  node_type     :integer          default("message"), not null
#  position      :jsonb
#  root_node     :boolean          default(FALSE)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  scenario_id   :string           not null
#
# Indexes
#
#  index_nodes_on_scenario_id  (scenario_id)
#
class Node < ApplicationRecord
  include NodeConstConcern
  include OnlyOneDefaultRecordConcern

  belongs_to :scenario

  # Same data node_type: message
  # body{}

  # Same data node_type: message
  # "body": {
  #     "type": "name",
  #     "settings": [
  #         {
  #             "label": "山田",
  #             "variable": "sei"
  #         },
  #         {
  #             "label": "花子",
  #             "variable": "mei"
  #         }
  #     ]
  # },

  enum node_type: {
    message: 0,
    input: 10,
    condition: 20,
    button: 30,
    html_tasks: 40,
    headless_tasks: 50,
    set_value: 60,
    button_v2: 70,
  }, _prefix: true

  def process_destroy
    next_nodes = self.class.where("next_node_uid @> ?", "{#{id}}")

    ActiveRecord::Base.transaction do
      if scenario.root_node_uid == id
        scenario.update!(root_node_uid: nil)
      end

      next_nodes.each do |n|
        next_node_uids = n.next_node_uid.excluding(id)
        n.update!(next_node_uid: next_node_uids)
      end

      destroy!
    end

    true
  rescue StandardError => e
    errors.add(:base, e.message)
    false
  end

  def check_modified_attribute
    attr_name = "root_node"
    scope = { scenario_id: scenario_id }

    super(attr_name, scope)
  end

  def modify_others_record
    super { update_root_node! }
  end

  def update_root_node!
    scenario.update!(root_node_uid: id)
  end
end
