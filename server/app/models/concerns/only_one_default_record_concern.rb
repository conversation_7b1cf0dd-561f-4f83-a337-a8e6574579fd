module OnlyOneDefaultRecordConcern
  extend ActiveSupport::Concern

  attr_reader :modified_attr, :attr_name, :scope

  included do
    before_save :check_modified_attribute
    before_commit :modify_others_record, on: [:create, :update]
  end

  def check_modified_attribute(attr_name, scope = {})
    @attr_name = attr_name
    @scope = scope

    @modified_attr = self.send(:"#{@attr_name}_changed?", from: false, to: true)
  end

  def modify_others_record
    return unless @modified_attr

    condition = { "#{@attr_name}": true }
    condition.merge!(@scope) if @scope.present? && @scope.is_a?(Hash)

    # rubocop:disable Rails/SkipsModelValidations
    self.class.where(condition).where.not(id: id).update_all("#{@attr_name}": false)
    # rubocop:enable Rails/SkipsModelValidations

    yield if block_given?
  end
end
