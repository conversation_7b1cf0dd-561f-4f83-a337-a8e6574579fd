module NodeConstConcern
  extend ActiveSupport::Concern

  MODULE_TYPE = {
    PRODUCT: "product",
    QUANTITY: "quantity",
    VARIANT: "variant",
    NAME: "name",
    FULL_NAME: "full_name",
    PHONE_NUMBER: "phone_number",
    PASSWORD: "password",
    EMAIL: "email",
    TEXT: "text",
    SELECT: "select",
    ADDRESS: "address",
    COUPON: "coupon",
    SEX_AND_BIRTHDAY: "sex_and_birthday",
    NAME_SEX_BIRTHDAY: "name_sex_birthday",
    TEL_EMAIL_PASSWORD: "tel_email_password",
    PAYMENT_METHOD: "payment_method",
    CREDIT_CARD: "credit_card",
    SCHEDULED_DELIVERY: "scheduled_delivery",
    RADIO_BUTTON: "radio_button",
    RADIO_BUTTON_RESELECTABLE: "radio_button_reselectable",
    RADIO_BUTTON_GRID: "radio_button_grid",
    RADIO_BUTTONS_MULTI_SELECT: "radio_buttons_multi_select",
    CHECKBOX: "checkbox",
    NAME_BIRTHDAY_TEL_EMAIL: "name_birthday_tel_email",
    INFORMATION: "information",
  }

  MESSAGE_TYPE = {
    TEXT: "text",
    IMAGE: "image",
    IMAGES: "images",
    VIDEO: "video",
  }

  INPUT_TYPE = {
    SELECT: "select",
    CHECKBOX: "checkbox",
    INPUT: "input",
    RADIO: "radio",
    DATE: "date",
  }

  CREDIT_CARD_FORM_KEYS = ["name", "brand", "cvv", "variable", "number", "expired"]

  NAME_SEX_BIRTHDAY_VARIABLES = [
    ["sei", "mei"],
    ["seifuri", "meifuri"],
    ["sex"],
    ["day", "month", "year"],
  ]

  NAME_BIRTHDAY_TEL_EMAIL_VARIABLES = [
    ["sei", "mei"],
    ["seifuri", "meifuri"],
    ["month", "year"],
    ["day", "month", "year"],
    ["mail"],
    ["tel"],
  ]
  VALID_SINGLE_VARIABLES = ["sex", "tel", "mail"].freeze

  INFORMATION_REQUIRED_VARIABLES = ["full_name", "sex", "birthday", "address", "tel", "mail"].freeze

  REQUIRED_VARIABLES_BY_TYPE = {
    "full_name" => [
      ["sei", "mei"],
      ["seifuri", "meifuri"]
    ],
    "address" => [
      ["zipcode", "prefectures", "address01", "address02"]
    ],
    "birthday" => [
      ["day", "month", "year"]
    ]
  }.freeze

  REQUIRED_SETTING_TYPE = ["input", "message"]
  NON_REQUIRED_INPUT_TYPE = ["radio_button_grid", "radio_buttons_multi_select"]

  LAYOUT = {
    HORIZONTAL: "horizontal",
    VERTICAL: "vertical",
  }
end
