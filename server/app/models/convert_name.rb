# TODO Update
module ConvertName
  class << self
    def get_kana_readings(full_name)
      full_name = full_name.gsub("　", " ")
      nm = Natto::MeCab.new
      result = { last: "", first: "", lastKana: "", firstKana: "" }
      parts = []

      nm.parse(full_name) do |n|
        parts << { kanji: n.surface, kana: n.feature.split(",")[7] } if n.surface != "EOS"
      end

      if parts.size == 3
        result[:last] = parts[0][:kanji]
        result[:first] = parts[1][:kanji]
        result[:lastKana] = parts[0][:kana]
        result[:firstKana] = parts[1][:kana]
      elsif parts.size <= 2
        result[:last] = parts[0][:kanji]
        result[:lastKana] = parts[0][:kana]
      else
        text_not_exist_mecab = parts.select { |v| v if v[:kana].nil? }
        if text_not_exist_mecab.blank?

          result[:last] = "#{parts[0][:kanji]}#{parts[1][:kanji]}"
          result[:first] = parts[2][:kanji]
          result[:lastKana] = "#{parts[0][:kana]}#{parts[1][:kana]}"
          result[:firstKana] = parts[2][:kana]
        else
          result[:last] = parts[0][:kanji]
          result[:first] = "#{parts[1][:kanji]}#{parts[2][:kanji]}"
          result[:lastKana] = parts[0][:kana]
          result[:firstKana] = "#{parts[1][:kana]}#{parts[2][:kana]}"
        end
      end

      result
    end

    # 小泉 名詞,固有名詞,人名,姓,*,*,小泉,コイズミ,コイズミ
    # 亜 名詞,固有名詞,地域,一般,*,*,亜,ア,ア
    # 瑠 名詞,固有名詞,組織,*,*,*,*
    # EOS

    # def suika_get_kana_readings(full_name)
    #   nm = Suika::Tagger.new
    #   result = { last: '', first: '', lastKana: '', firstKana: '' }n
    #
    #   parts = []

    #   nm.parse(full_name).each do |n|
    #     parts << { kanji: n.split("\t")[0], kana: n.split(',')[7] }
    #   end

    #   if parts.size >= 2
    #     result[:last] = parts[0][:kanji]
    #     result[:first] = parts[1][:kanji]
    #     result[:lastKana] = parts[0][:kana]
    #     result[:firstKana] = parts[1][:kana]
    #   elsif parts.size == 1
    #     result[:last] = parts[0][:kanji]
    #     result[:lastKana] = parts[0][:kana]
    #   end

    #   result
    # end
  end
end
