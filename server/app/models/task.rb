# == Schema Information
#
# Table name: tasks
#
#  id           :uuid             not null, primary key
#  class_name   :string           not null
#  description  :string
#  lock_version :integer          not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
class Task < ApplicationRecord
  CLASS_NAME = ["HawaiiWaterDesiredInstallationDate", "GenpeiseiyakuSubmit" ,"MiraiSubmit"]

  def self.initialize_data
    CLASS_NAME.each do |class_name|
      task = Task.find_or_initialize_by(class_name:)
      task.save!
    end
  end
end
