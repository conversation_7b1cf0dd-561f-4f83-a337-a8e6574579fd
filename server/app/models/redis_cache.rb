module RedisCache
  class << self
    # Options Hash defined here: https://github.com/redis/redis-rb/blob/eb72f8257a052c5eb16c9f5dbab70d8017f494d6/lib/redis/commands/strings.rb#L71-L82

    DEFAULT_EXPIRATION = 86400 # 1 day in seconds

    def write(key, value = {}, options = {})
      $redis.set(key, value, **default_options.merge(options))
    end

    def read(key)
      return nil if key.nil?

      $redis.get(key)
    end

    def delete(key)
      $redis.del(key)
    end

    def exists?(key)
      read(key).present?
    end

    def keys
      $redis.keys
    end

    def delete_all
      keys.each { |key| delete(key) }
    end

    private

    def default_options
      { ex: DEFAULT_EXPIRATION }
    end
  end
end
