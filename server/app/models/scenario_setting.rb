# == Schema Information
#
# Table name: scenario_settings
#
#  id                       :uuid             not null, primary key
#  active                   :boolean          default(FALSE)
#  css_customize            :jsonb
#  description              :string           not null
#  general_settings         :jsonb
#  javascript_customize     :jsonb
#  lock_version             :integer          not null
#  name                     :string           not null
#  setting_type             :integer          default("general")
#  theme_customize_settings :jsonb
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  scenario_id              :string           not null
#
# Indexes
#
#  index_scenario_settings_on_scenario_id  (scenario_id)
#
class ScenarioSetting < ApplicationRecord
  include OnlyOneDefaultRecordConcern

  belongs_to :scenario

  enum setting_type: {
    general: 1,
    design: 10,
  }, _prefix: true

  scope :active, -> { where(active: true) }

  GENERAL_SETTINGS_ATTRIBUTES = [
    { key: :chat_window_position, type: "String" },
    { key: :mobile_chat_window_position, type: "String" },
    { key: :chat_button_title, type: "String" },
    { key: :chat_window_title, type: "String" },
    { key: :chat_window_subtitle, type: "String" },
    { key: :chat_operator_name, type: "String" },
    { key: :chat_operator_img_url, type: "String" },
    { key: :pc_custom_chat_window_width, type: "String" },
    { key: :pc_custom_chat_window_height, type: "String" },
    { key: :show_button_close, type: "Boolean" },
    { key: :show_confirmation_close_modal, type: "Boolean" },
    { key: :confirmation_text, type: "String" },
    { key: :show_chat_start_button, type: "Boolean" },
    { key: :start_chatbot_immediately, type: "Boolean" },
  ]

  THEME_CUSTOMIZE_SETTINGS_ATTRIBUTES = [:chat_design_theme_id]
  THEME_AND_DESIGN_SETTING_ATTRIBUTES = ::ChatDesignTheme.field_names.excluding(:id) + THEME_CUSTOMIZE_SETTINGS_ATTRIBUTES

  DESIGN_SETTINGS_ATTRIBUTES = THEME_AND_DESIGN_SETTING_ATTRIBUTES.map do |item|
    { key: item, type: "String" }
  end

  store :general_settings, accessors: GENERAL_SETTINGS_ATTRIBUTES.pluck(:key), coder: JSON
  store :theme_customize_settings, accessors: DESIGN_SETTINGS_ATTRIBUTES.pluck(:key), coder: JSON

  def check_modified_attribute
    attr_name = "active"
    scope = { scenario_id: scenario_id, setting_type: setting_type }

    super(attr_name, scope)
  end
end
