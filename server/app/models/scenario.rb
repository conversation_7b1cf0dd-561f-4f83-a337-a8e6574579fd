# == Schema Information
#
# Table name: scenarios
#
#  id                :uuid             not null, primary key
#  active            :boolean          default(FALSE)
#  data_json         :jsonb
#  description       :string
#  lock_version      :integer          not null
#  match_type        :integer          default("match"), not null
#  match_value       :string           not null
#  name              :string           not null
#  root_node_uid     :string
#  support_ui_enable :boolean          default(FALSE)
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  user_id           :string           not null
#
# Indexes
#
#  index_scenarios_on_user_id  (user_id)
#
class Scenario < ApplicationRecord
  include OnlyOneDefaultRecordConcern

  belongs_to :user

  has_many :scenario_settings, dependent: :destroy
  has_one :scenario_general_setting_active, -> { active.setting_type_general }, class_name: "ScenarioSetting", dependent: :destroy, inverse_of: :scenario
  has_one :scenario_design_setting_active, -> { active.setting_type_design }, class_name: "ScenarioSetting", dependent: :destroy, inverse_of: :scenario

  has_many :nodes, dependent: :destroy

  scope :active, -> { where(active: true) }

  enum match_type: {
    match: 1,
    include: 10,
  }, _prefix: true

  def check_modified_attribute
    attr_name = "active"
    scope = { user_id: user_id, match_value: match_value }

    super(attr_name, scope)
  end

  def sid_data(ssid)
    sdata = nil

    if RedisCache.exists?(ssid)
      sdata = RedisCache.read(ssid)
    else
      ssid = "scenario-#{id}-#{SecureRandom.hex(16)}"
      RedisCache.write(ssid)
    end

    [ssid, sdata]
  end

  def matches_url?(url)
    if self.match_type_include?
      regex_url = Regexp.new(Regexp.escape(match_value))

      url.match?(regex_url)
    else
      url == match_value
    end
  end
  class << self
    def find_matching_url(user_id, url)
      scenarios = self.active.where(user_id:)
      scenarios.detect { |sc| sc.matches_url?(url) }
    end

    def in_charge_of(current_user)
      return none unless current_user && current_user.instance_of?(User)

      if current_user.user_type_admin?
        all
      elsif current_user.user_type_member?
        where(user_id: current_user.id)
      end
    end
  end
end
