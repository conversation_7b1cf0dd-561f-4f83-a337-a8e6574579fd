class Variable < ActiveHash::Base
  include ActiveHash::Associations
  fields :id, :name, :desc

  # Don't change key please!
  add id: 1, name: "product", desc: "Product"
  add id: 2, name: "sei", desc: "姓"
  add id: 3, name: "mei", desc: "姓"
  add id: 4, name: "seif<PERSON>", desc: "セイ"
  add id: 5, name: "meifuri", desc: "セイ"
  add id: 7, name: "zipcode", desc: "郵便番号"
  add id: 8, name: "prefectures", desc: "選択してください"
  add id: 9, name: "address01", desc: "Address01"
  add id: 10, name: "address02", desc: "Address02"
  add id: 10, name: "address03", desc: "Address03"
  add id: 11, name: "mail", desc: "メールアドレス"
  add id: 12, name: "tel", desc: "電話番号"
  add id: 13, name: "password", desc: "Password"
  add id: 14, name: "sex", desc: "性別"
  add id: 15, name: "cv_upsell_1", desc: "CV Upsell 1"
  add id: 16, name: "cv_upsell_2", desc: "CV Upsell 2"

  add id: 17, name: "coupon", desc: "クーポンコード"
  add id: 18, name: "payment_method", desc: "お支払い方法"
  add id: 19, name: "day", desc: "生年月日"
  add id: 20, name: "month", desc: "生年月日"
  add id: 21, name: "year", desc: "生年月日"
  add id: 22, name: "credit_card", desc: "カード番号"
  # add id: 22, name: "card_number", desc: "カード番号"
  # add id: 23, name: "card_expired", desc: "有効期限"
  # add id: 24, name: "card_expired_month", desc:"月"
  # add id: 25, name: "card_expired_year", desc:"年"
  # add id: 26, name: "card_name", desc:"カード名義(ローマ字氏名"
  # add id: 27, name: "card_cvv", desc:"セキュリティコード"
  #
  add id: 28, name: "result_msg", desc: "Result Message"
  add id: 29, name: "result_msg_thank_offer", desc: "Result Message Thank Offer"
  add id: 30, name: "error", desc: "Error Handle Submit when payment fail"
  add id: 31, name: "scheduled_date", desc: "お届け日の指定"
  add id: 32, name: "scheduled_time", desc: "お届け時間の指定"
  add id: 33, name: "agreement1", desc: "同意"
  add id: 34, name: "agreement2", desc: "同意"
  add id: 35, name: "agreement3", desc: "同意"
  add id: 36, name: "full_name", desc: "お名前"
  add id: 37, name: "full_name_kana", desc: "フリガナ"
end
