module Api
  module V1
    class FormPrefillsController < Api::BaseController

      YES = 1
      NO = 0

      def show
        unless params[:id].present?
          render json: { error: "Missing or invalid id" }, status: :bad_request
          return
        end

        data = fetch_cart_data(params[:id])
        if data
          render json: { data: data }
        else
          render json: { error: "Cart data not found or invalid" }, status: :not_found
        end
      end

      def destroy
        unless params[:id].present?
          render json: { error: "Missing or invalid id" }, status: :bad_request
          return
        end

        if RedisCache.delete(params[:id]) == YES
          render json: { message: "Deleted" }
        else
          render json: { error: "Failed to delete cart data" }, status: :unprocessable_entity
        end
      end

      private

      def fetch_cart_data(id)
        raw_data = RedisCache.read(id)
        return nil unless raw_data

        parsed_data = JSON.parse(raw_data)
        unless parsed_data.is_a?(Hash) && parsed_data["cart"]
          Rails.logger.error("Invalid cart data format for id: #{id}")
          return nil
        end
        
        parsed_data["cart"]
      rescue JSON::ParserError => e
        Rails.logger.error("JSON parse error for id: #{id}, error: #{e.message}")
        nil
      rescue Redis::CannotConnectError => e
        Rails.logger.error("Redis connection error: #{e.message}")
        nil
      end
    end
  end
end
