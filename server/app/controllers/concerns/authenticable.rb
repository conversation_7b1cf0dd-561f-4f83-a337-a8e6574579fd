require "json_web_token"

module Authenticable
  BEARER_AUTHORIZATION = "Bearer"

  private

  def current_user
    return nil unless token

    fetch_user
  end

  def token
    authen, token = request.headers["United-Cart-Authenticate"].to_s.split(" ")
    return unless authen == BEARER_AUTHORIZATION

    token
  end

  def fetch_user(token: self.token)
    begin
      decoded = JsonWebToken.decode(token)

      user = if decoded[:type] == "user"
               ::User.find(decoded[:sub])
             else
               nil
             end
    rescue StandardError => _e
      return nil
    end

    user
  end
end
