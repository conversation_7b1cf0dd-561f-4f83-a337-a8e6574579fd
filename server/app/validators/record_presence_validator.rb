class RecordPresenceValidator < ActiveModel::EachValidator
  def validate_each(record, attribute, value)
    klass = options.dig(:klass)
    field = options.dig(:field) || :id
    scope = options.dig(:scope)

    condition = { :"#{field}" => value }
    condition.merge!(scope) if scope.present? && scope.is_a?(Hash)

    raise ArgumentError.new("Missing klass args") unless klass

    record.errors.add(attribute, :invalid) unless klass_exists?(klass, condition)
  end

  private

  def klass_exists?(klass, condition)
    klass.superclass == ActiveHash::Base ? klass.find_by(condition).present? : klass.exists?(condition)
  end
end
