class PasswordFormatValidator < ActiveModel::EachValidator
  # rubocop:disable Layout/LineLength
  PASSWORD_FORMAT = /^((?=.*[A-Z])(?=.*[!@#$&*])(?=.*[0-9])|(?=.*[a-z])(?=.*[!@#$&*])(?=.*[0-9])|(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])|(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$&*])).+$/.freeze
  # rubocop:enable Layout/LineLength

  # at least a character and a number, others are optional
  def validate_each(record, attribute, value)
    return if value.blank?
    return if value =~ PASSWORD_FORMAT && value.length >= 8 && value.length <= 255

    record.errors.add(attribute, :invalid_password)
  end
end
