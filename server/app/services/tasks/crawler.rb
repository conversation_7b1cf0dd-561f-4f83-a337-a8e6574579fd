require "net/http"
require "uri"

module Tasks
  class Crawler
    include <PERSON>actor

    def call
      start_time = Time.current
      validate_class_name
      validate_inputs
      validate_outputs

      url = URI.parse("#{ENV.fetch("TASK_RUNNER_URL", "http://localhost:4000")}/api/v1/tasks/crawlers")

      http = Net::HTTP.new(url.host, url.port)
      http.read_timeout = 300
      http.open_timeout = 300

      request = Net::HTTP::Post.new(url)
      request["Content-Type"] = "application/json"

      request.body = JSON.dump({
                                 "class_name" => context.class_name,
                                 "inputs" => context.inputs.to_json,
                                 "outputs" => context.outputs.to_json,
                               })

      response = http.request(request)

      if response.is_a?(Net::HTTPSuccess)
        response_data = JSON.parse(response.body)
        broadcast_message_channel(response_data["data"])
      else
        log_and_broadcast_error("API call failed with status #{response.code}")
      end

      end_time = Time.current
      Rails.logger.info("Total time taken for call: #{end_time - start_time} seconds")
    rescue StandardError => e
      log_and_broadcast_error(e.message)
    end

    private

    def validate_class_name
      return if Task.exists?(class_name: context.class_name)

      raise "Class name #{context.class_name} does not exist in Tasks."
    end

    def validate_inputs
      url = context.inputs.dig("url")
      url_value = url&.dig("value")

      if url_value.blank?
        raise " URL is required"
      elsif !url_value.match?(::UrlFormatValidator::URL_FORMAT)
        raise "Invalid URL format for 'url' in inputs: #{!url_value}"
      end
    end

    def validate_outputs
      data = context.outputs.dig("data")
      return if data.is_a?(Hash) && data.dig("message").is_a?(String) && data.dig("error").is_a?(String)

      raise "Outputs format is invalid. Expected 'data' to be a hash with 'message' and 'error' as strings."
    end

    def log_and_broadcast_error(message)
      Rails.logger.info(message)
      broadcast_message_channel({ message:, error: true, optionValues: [] })
    end

    def broadcast_message_channel(data)
      ActionCable.server.broadcast("system_message_channel_#{context.user_id}_#{context.ssid}", { data: })
    end
  end
end
