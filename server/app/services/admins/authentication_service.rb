##
# Customer Authenticate service
#
# @params: [String] email
# @params: [String] password
# @params: [<PERSON><PERSON><PERSON>] remember_me
#
### Returns
# @return: [Customer] user

module Admins
  class AuthenticationService
    include Interactor

    def call
      user = ::User.find_by(email: context.email)

      unless user
        return context.fail!(
          message: I18n.t("devise.failure.not_found_account",
                          authentication_keys: "email"),
        )
      end

      unless user.valid_password?(context.password)
        return context.fail!(
          message: I18n.t("devise.failure.not_found_in_database",
                          authentication_keys: "email"),
        )
      end

      context.user = user
    end
  end
end
