module Admins
  class ScheduleDateCalculatorService
    HOLIDAY_API_URL = "https://holidays-jp.github.io/api/v1/date.json"
    DATE_FORMAT = "%Y-%m-%d"

    def initialize(days_after_current = 4, range_days = 18)
      @holidays = fetch_japanese_holidays
      @start_date = find_valid_start_date(days_after_current)
      @end_date = @start_date + range_days.days
    end

    def scheduled_dates
      (@start_date.to_date..@end_date.to_date).map { |date| date.strftime(DATE_FORMAT) }
      # generate_date_options(date_option, @start_date, @end_date)
    end

    private

    # def generate_date_options(date_option, start_date, end_date)
    #   replacement_count = 0
    #   (start_date.to_date..end_date.to_date).each do |date|
    #     if excluded_date?(date)
    #       replacement_count += 1
    #       next
    #     end
    #     break if date_option.uniq.count == @range_days
    #     date_option << date.strftime(DATE_FORMAT)
    #   end
    #   date_option_uniq = date_option.uniq
    #   return date_option_uniq if replacement_count == 0

    #   generate_date_options(date_option_uniq, date_option_uniq.last, end_date + replacement_count.days)
    # end

    def fetch_japanese_holidays
      holiday_json = RestClient.get(HOLIDAY_API_URL)
      JSON.parse(holiday_json).keys
    rescue StandardError => e
      Rails.logger.error(e.message)
      []
    end

    def excluded_date?(date)
      date.saturday? || date.sunday? || @holidays.include?(date.strftime(DATE_FORMAT))
    end

    # def find_valid_start_date(date, value)
    #   date += 1.day

    #   count_date = (Time.current.to_date..date.to_date).map { |d| d if !excluded_date?(d) }.compact.count

    #   return date if count_date == value + 1

    #   find_valid_start_date(date, value)
    # end

    def find_valid_start_date(value)
      date = Time.current

      loop do
        date += 1.day
        count_valid_dates = (Time.current.to_date..date.to_date).count { |d| !excluded_date?(d) }

        break if count_valid_dates == value + 1
      end
      date
    end
  end
end
