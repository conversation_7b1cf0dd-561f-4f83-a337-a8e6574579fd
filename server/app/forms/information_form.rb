class InformationForm < BaseForm

  attribute :settings, :array

  validates :settings, presence: true
  validate :validate_structure

  def node_variables
    @node_variables ||= []
  end

  private

  def validate_subsetting_common(subsetting, error_prefix)
    return errors.add(:"#{error_prefix}", I18n.t("errors.messages.invalid")) unless subsetting.is_a?(Hash)

    errors.add(:"#{error_prefix}.required", I18n.t("errors.messages.required")) unless subsetting.key?("required")
    errors.add(:"#{error_prefix}.label", I18n.t("errors.messages.required")) unless subsetting.key?("label")
    errors.add(:"#{error_prefix}.variable", I18n.t("errors.messages.required")) unless subsetting.key?("variable")
    node_variables << subsetting["variable"] if subsetting["variable"].present?
  end

  def validate_subsetting(subsetting, index, item_index)
    validate_subsetting_common(subsetting, "settings.#{index}.subsetting.#{item_index}")
  end

  def validate_full_name_subsetting(subsetting, index, group_index, sub_index)
    validate_subsetting_common(subsetting, "settings.#{index}.settings.#{group_index}.settings.#{sub_index}")
  end

  def validate_group_setting_common(setting, index, variable_type)
    return errors.add(:"settings.#{index}", I18n.t("errors.messages.invalid")) unless setting.is_a?(Hash)

    errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")

    unless setting["settings"].is_a?(Array)
      errors.add(:"settings.#{index}.settings", I18n.t("errors.messages.invalid"))
      return
    end

    variables = setting["settings"].map { |s| s.is_a?(Hash) ? s["variable"] : nil }.compact
    required_variables = Node::REQUIRED_VARIABLES_BY_TYPE[variable_type].first
    missing_variables = required_variables - variables

    if missing_variables.any?
      errors.add(:"settings.#{index}.variables", I18n.t("errors.messages.missing_keys", keys: missing_variables.join(", ")))
    end

    setting["settings"].each_with_index do |subsetting, item_index|
      validate_subsetting(subsetting, index, item_index)
    end
  end

  def validate_full_name_group(group_setting, index, group_index)
    return errors.add(:"settings.#{index}.settings.#{group_index}", I18n.t("errors.messages.invalid")) unless group_setting.is_a?(Hash)

    errors.add(:"settings.#{index}.settings.#{group_index}.label", I18n.t("errors.messages.required")) unless group_setting.key?("label")

    return unless group_setting.key?("settings")

    unless group_setting["settings"].is_a?(Array)
      errors.add(:"settings.#{index}.settings.#{group_index}.settings", I18n.t("errors.messages.invalid"))
      return
    end

    variables = group_setting["settings"].map { |s| s.is_a?(Hash) ? s["variable"] : nil }.compact
    required_groups = Node::REQUIRED_VARIABLES_BY_TYPE["full_name"]
    valid_group_found = required_groups.any? { |group| group.sort == variables.sort }

    unless valid_group_found
      expected = required_groups.map { |vars| vars.join(", ") }.join(" or ")
      errors.add(:"settings.#{index}.settings.#{group_index}.variables", I18n.t("errors.messages.missing_keys", keys: expected))
    end

    group_setting["settings"].each_with_index do |subsetting, sub_index|
      validate_full_name_subsetting(subsetting, index, group_index, sub_index)
    end
  end

  def validate_full_name_setting(setting, index)
    return unless setting.key?("settings")

    unless setting["settings"].is_a?(Array)
      errors.add(:"settings.#{index}.settings", I18n.t("errors.messages.invalid"))
      return
    end

    # Kiểm tra xem có đủ 2 nhóm: ["sei", "mei"] và ["seifuri", "meifuri"]
    group_variables = setting["settings"].map do |group|
      next unless group.is_a?(Hash) && group["settings"].is_a?(Array)
      group["settings"].map { |s| s.is_a?(Hash) ? s["variable"] : nil }.compact.sort
    end.compact

    required_groups = Node::REQUIRED_VARIABLES_BY_TYPE["full_name"].map(&:sort)
    missing_groups = required_groups - group_variables
    if missing_groups.any?
      expected = missing_groups.map { |vars| vars.join(", ") }.join(" and ")
      errors.add(:"settings.#{index}.variables", I18n.t("errors.messages.missing_keys", keys: expected))
    end

    setting["settings"].each_with_index do |group_setting, group_index|
      validate_full_name_group(group_setting, index, group_index)
    end
  end

  def validate_address_setting(setting, index)
    validate_group_setting_common(setting, index, "address")
  end

  def validate_birthday_setting(setting, index)
    validate_group_setting_common(setting, index, "birthday")
  end

  def validate_single_setting(setting, index)
    return errors.add(:"settings.#{index}", I18n.t("errors.messages.invalid")) unless setting.is_a?(Hash)

    errors.add(:"settings.#{index}.required", I18n.t("errors.messages.required")) unless setting.key?("required")
    errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")
    errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.required")) unless setting.key?("variable")
    if setting.key?("variable") && Node::VALID_SINGLE_VARIABLES.exclude?(setting["variable"])
      errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.invalid"))
    end
    node_variables << setting["variable"] if setting["variable"].present? && Node::VALID_SINGLE_VARIABLES.include?(setting["variable"])
  end

  def validate_setting(setting, index)
    return errors.add(:"settings.#{index}", I18n.t("errors.messages.invalid")) unless setting.is_a?(Hash)

    if setting.key?("settings")
      case setting["variable"]
      when "full_name"
        validate_full_name_setting(setting, index)
      when "address"
        validate_address_setting(setting, index)
      when "birthday"
        validate_birthday_setting(setting, index)
      else
        errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.invalid"))
      end
    else
      validate_single_setting(setting, index)
    end
  end

  def validate_structure
    unless settings.is_a?(Array)
      errors.add(:settings, I18n.t("errors.messages.invalid", message: "must be an array"))
      return
    end

    variables = settings.map { |s| s.is_a?(Hash) ? s["variable"] : nil }.compact
    missing_variables = Node::INFORMATION_REQUIRED_VARIABLES - variables
    invalid_variables = variables - Node::INFORMATION_REQUIRED_VARIABLES

    errors.add(:settings, I18n.t("errors.messages.missing_keys", keys: missing_variables.join(", "))) if missing_variables.present?
    errors.add(:settings, I18n.t("errors.messages.invalid_keys", keys: invalid_variables.join(", "))) if invalid_variables.present?

    settings.each_with_index do |setting, index|
      validate_setting(setting, index)
    end
  end
end