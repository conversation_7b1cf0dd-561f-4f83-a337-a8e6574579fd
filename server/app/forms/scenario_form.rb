class ScenarioForm < BaseForm
  attribute :description, :string
  attribute :name, :string
  attribute :active, :boolean
  attribute :match_type, :string
  attribute :match_value, :string
  attribute :root_node_uid, :string
  attribute :support_ui_enable, :boolean
  attribute :progress_bar_enable, :boolean

  validates :name, :match_value,  presence: true
  validates :active, inclusion: { in: [true, false] }
  validates :match_type, inclusion: { in: ::Scenario.match_types.keys }

  def save
    return unless super

    @model.assign_attributes(attributes)
    @model.save
  end
end
