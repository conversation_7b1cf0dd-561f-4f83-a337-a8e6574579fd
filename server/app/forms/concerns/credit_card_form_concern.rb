module CreditCardFormConcern
  extend ActiveSupport::Concern

  included do
    attribute :cvv, :hash
    attribute :brand, :hash
    attribute :name, :hash
    attribute :number, :hash
    attribute :expired, :hash

    validates :cvv, :brand, :name, :expired, :number, presence: true

    validate :validate_cvv, if: -> { cvv.present? }
    validate :validate_name, if: -> { name.present? }
    validate :validate_brand, if: -> { brand.present? }
    validate :validate_number, if: -> { number.present? }
    validate :validate_expired, if: -> { expired.present? }

    private

    def validate_number
      validate_label(number, :number)
      validate_placeholder(number, :number)
    end

    def validate_expired
      validate_label(expired, :expired)

      month = expired["month"]
      return errors.add(:"expired.month", I18n.t("errors.messages.required")) if month.nil?

      year = expired["year"]
      return errors.add(:"expired.year", I18n.t("errors.messages.required")) if year.nil?

      validate_label(month, :"expired.month")
      validate_label(year, :"expired.month")
    end

    def validate_cvv
      validate_key_object(cvv, :cvv)
      validate_enabled_and_required(cvv, :cvv)
      validate_placeholder(cvv, :cvv)
      validate_label(cvv, :cvv)
    end

    def validate_name
      validate_key_object(name, :name)
      validate_enabled_and_required(name, :name)
      validate_placeholder(name, :name)
      validate_label(name, :name)
    end

    def validate_brand
      validate_key_object(brand, :brand)
      validate_enabled_and_required(brand, :brand)
      validate_label(brand, :brand)

      options = brand["options"]
      unless options.is_a?(Array) && options.all? { |option| option["text"].is_a?(String) }
        errors.add(:"brand.option", I18n.t("errors.messages.invalid_options"))
      end
    end

    def validate_key_object(key_object, key)
      errors.add(key, I18n.t("errors.messages.required")) if key_object.blank?
    end

    def validate_placeholder(key_object, key)
      return unless key_object

      placeholder = key_object["placeholder"]
      return errors.add(:"#{key}.placeholder", I18n.t("errors.messages.required")) if placeholder.nil?
    end

    def validate_label(key_object, key)
      return unless key_object

      label = key_object["label"]
      return errors.add(:"#{key}.label", I18n.t("errors.messages.required")) if label.nil?
    end

    def validate_enabled_and_required(key_object, key)
      return unless key_object

      enabled = key_object["enabled"]
      required = key_object["required"]
      return errors.add(:"#{key}.enabled", I18n.t("errors.messages.required")) if enabled.nil?

      unless [true, false].include?(enabled) && (required.nil? || [true, false].include?(required))
        errors.add(key, I18n.t("errors.messages.invalid"))
      end

      errors.add(:"#{key}.required", I18n.t("errors.messages.invalid")) if !enabled && required
    end
  end
end
