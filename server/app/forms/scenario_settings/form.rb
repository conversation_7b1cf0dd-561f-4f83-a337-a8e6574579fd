module ScenarioSettings
  class Form < BaseForm
    attribute :description, :string
    attribute :name, :string
    attribute :active, :boolean
    attribute :setting_type, :string

    attribute :css_customize, :string
    attribute :javascript_customize, :string

    attribute :general_settings
    attribute :theme_customize_settings

    validates :name, presence: true
    validates :active, inclusion: { in: [true, false] }
    validates :setting_type, inclusion: { in: ::ScenarioSetting.setting_types.keys }

    validate :validate_general_setting, if: -> { general_settings.present? && setting_type == "general" }
    validate :validate_design_setting, if: -> { theme_customize_settings.present? && setting_type == "design" }

    def save
      return unless super

      @model.assign_attributes(attributes)
      @model.save
    end

    def validate_general_setting
      general_form = ::ScenarioSettings::GeneralSettingForm.new(general_settings)
      return if general_form.valid?

      general_form.errors.messages.each do |key, messages|
        messages.each do |message|
          errors.add(:"general_settings.#{key}", message)
        end
      end
    end

    def validate_design_setting
      design_form = ::ScenarioSettings::DesignSettingForm.new(theme_customize_settings)
      return if design_form.valid?

      design_form.errors.messages.each do |key, messages|
        messages.each do |message|
          errors.add(:"theme_customize_settings.#{key}", message)
        end
      end
    end
  end
end
