class UserForm < BaseForm
  attribute :email, :string
  attribute :password, :string
  attribute :user_type, :string
  attribute :shop_id, :string

  validates :email, presence: true
  validates :password, presence: true, if: -> { @model.new_record? }

  validates :email, email_format: true, uniq: { klass: ::User }, allow_blank: true
  validates :user_type, inclusion: { in: ::User.user_types.keys }
  validates :password, password_format: true, allow_blank: true

  def save
    return unless super

    @model.assign_attributes(attributes)
    @model.save
  end
end
