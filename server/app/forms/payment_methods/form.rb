module PaymentMethods
  class Form < BaseForm
    attribute :payment_method, :hash
    attribute :credit_card_form, :hash

    validates :payment_method, presence: true

    validate :validate_payment_method, if: -> { payment_method.present? }
    validate :validate_credit_card_form, if: -> { credit_card_form.present? }

    private

    def validate_payment_method
      options = payment_method.dig("options")
      return errors.add(:"payment_method.options", I18n.t("errors.messages.required")) if options.blank?

      return if options.is_a?(Array) && options.all? { |option| option["text"].is_a?(String) }

      errors.add(:"payment_method.option", I18n.t("errors.messages.invalid_options"))
    end

    def validate_credit_card_form
      default_keys = ::Node::CREDIT_CARD_FORM_KEYS.dup 
      
      default_keys << "show_on"
      default_keys.delete("variable")

      missing_keys = default_keys - credit_card_form.keys
      invalid_keys = credit_card_form.keys&.- default_keys

      return errors.add(:settings, I18n.t("errors.messages.missing_keys", keys: missing_keys.join(", "))) if missing_keys.present?
      return errors.add(:settings, I18n.t("errors.messages.invalid_keys", keys: invalid_keys.join(", "))) if invalid_keys.present?

      form = ::PaymentMethods::CreditCardForm.new(credit_card_form)
      return if form.valid?

      form.error_messages_with_no_human_name.each do |key, messages|
        messages.each do |message|
          errors.add(:"settings.#{key}", message)
        end
      end
    end
  end
end
