class BaseForm < ActiveType::Object
  MAX_INTEGER = ((1 << 32) / 2) - 1
  MAX_BIGINT = ((1 << 64) / 2) - 1
  MAX_STRING = 255
  MAX_TEXT = 65_535

  attr_accessor :model

  delegate :persisted?, to: :model, allow_nil: true

  def assign_model(model, params = {}, skip_keys = [])
    @model = model
    @params = params.with_indifferent_access.except(skip_keys)

    attributes.each_key do |k|
      next if skip_keys.include?(k.to_sym)

      self[k] = @params.key?(k) ? @params[k] : model.try(k)
    end

    self
  end

  def model_assign_attributes
    @model.assign_attributes(attributes)
  end

  def error_messages
    errors.messages.each_with_object({}) do |(k, messages), h|
      messages.each do |message|
        h[k.to_sym] = h[k.to_sym].to_a + [self.class.human_attribute_name(k) + " " + message]
      end
    end
  end

  def error_messages_with_no_human_name
    errors.messages.each_with_object({}) do |(k, messages), h|
      messages.each do |message|
        h[k.to_sym] = h[k.to_sym].to_a + [message]
      end
    end
  end
end
