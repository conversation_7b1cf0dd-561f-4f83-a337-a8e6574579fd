class NameBirthdayTelEmailForm < BaseForm
  attribute :settings, :array

  validates :settings, presence: true
  validate :validate_structure

  def node_variables
    @node_variables ||= []
  end

  private

  def validate_structure
    return errors.add(:settings, I18n.t("errors.messages.invalid")) unless settings.is_a?(Array)

    settings.each_with_index do |setting, index|
      validate_setting(setting, index)
    end

    validate_missing_and_invalid_variables
  end

  def validate_setting(setting, index)
    if setting.key?("settings")
      validate_group_setting(setting, index)
    else
      validate_single_setting(setting, index)
    end
  end

  def validate_group_setting(setting, index)
    errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")

    variables = setting["settings"].pluck("variable").compact.sort
    valid_group_found = ::Node::NAME_BIRTHDAY_TEL_EMAIL_VARIABLES.map(&:sort).include?(variables)

    errors.add(:"settings.#{index}.variables", I18n.t("errors.messages.invalid_key_pairs", keys: variables.join(", "))) unless valid_group_found

    setting["settings"].each_with_index do |subsetting, item_index|
      validate_subsetting(subsetting, index, item_index)
    end
  end

  def validate_subsetting(subsetting, index, item_index)
    errors.add(:"settings.#{index}.subsetting.#{item_index}.required", I18n.t("errors.messages.required")) unless subsetting.key?("required")
    errors.add(:"settings.#{index}.subsetting.#{item_index}.label", I18n.t("errors.messages.required")) unless subsetting.key?("label")
    errors.add(:"settings.#{index}.subsetting.#{item_index}.variable", I18n.t("errors.messages.required")) unless subsetting.key?("variable")

    node_variables << subsetting["variable"]
  end

  def validate_single_setting(setting, index)
    errors.add(:"settings.#{index}.required", I18n.t("errors.messages.required")) unless setting.key?("required")
    errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")
    errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.required")) unless setting.key?("variable")
    errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.invalid")) if setting.key?("variable") && ["tel", "mail"].exclude?(setting["variable"])

    node_variables << setting["variable"]
  end

  def validate_missing_and_invalid_variables
    optional_variables = ["day", "mail"]
    required_variables = ::Node::NAME_BIRTHDAY_TEL_EMAIL_VARIABLES.flatten.compact - optional_variables
    missing_variables = required_variables - node_variables
    invalid_variables = node_variables - required_variables - optional_variables

    errors.add(:settings, I18n.t("errors.messages.missing_keys", keys: missing_variables.join(", "))) if missing_variables.present?
    errors.add(:settings, I18n.t("errors.messages.invalid_keys", keys: invalid_variables.join(", "))) if invalid_variables.present?
  end
end
