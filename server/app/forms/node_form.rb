class NodeForm < BaseForm
  attribute :body
  attribute :position
  attribute :node_type, :string
  attribute :scenario_id, :string
  attribute :label, :string
  attribute :next_node_uid, :array
  attribute :root_node, :boolean

  validates :node_type, :scenario_id, presence: true
  validates :root_node, inclusion: { in: [true, false] }

  validates :scenario_id, record_presence: { klass: ::Scenario }, allow_blank: true
  validates :node_type, inclusion: { in: ::Node.node_types.keys }, allow_blank: true

  validate :validate_next_node_uid, if: -> { next_node_uid.present? }
  validate :validate_body, if: -> { body.present? }

  def save
    return unless super

    @model.assign_attributes(attributes)
    @model.save
  end

  private

  # Validate next_node_uid
  def validate_next_node_uid
    nodes = ::Node.where(id: next_node_uid, scenario_id: scenario_id)

    next_node_uid.each_with_index do |id, index|
      node = nodes.detect { |n| n.id == id }
      errors.add(:"next_node_uid_#{index}", I18n.t("errors.messages.invalid")) if node.blank?
    end
  end

  # Validate the body structure
  def validate_body
    return errors.add(:body, I18n.t("errors.messages.invalid")) unless body.is_a? Hash

    body_type = body.dig("type")
    settings = body.dig("settings")

    validate_body_type(body_type, settings)

    case body_type
    when ::Node::MODULE_TYPE[:CREDIT_CARD]
      validate_credit_settings(settings)
    when ::Node::MODULE_TYPE[:NAME_SEX_BIRTHDAY]
      validate_name_sex_birthday_settings(settings)
    when ::Node::MODULE_TYPE[:NAME_BIRTHDAY_TEL_EMAIL]
      validate_name_birthday_tel_email_settings(settings)
    when ::Node::MODULE_TYPE[:PAYMENT_METHOD]
      validate_payment_method_settings(settings)
    when ::Node::MODULE_TYPE[:INFORMATION]
      validate_information_settings(settings)
    else
      validate_settings(settings) if settings.present?
    end
  end

  def validate_name_sex_birthday_settings(settings)
    return if settings.blank? || !settings.is_a?(Array)

    node_variables = []
    settings.each_with_index do |setting, index|
      if setting.key?("settings")
        errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")

        variables = setting["settings"].pluck("variable").compact.sort
        valid_group_found = ::Node::NAME_SEX_BIRTHDAY_VARIABLES.map(&:sort).include?(variables)

        errors.add(:"settings.#{index}.variables", I18n.t("errors.messages.invalid_key_pairs", keys: variables.join(", "))) unless valid_group_found

        setting["settings"].each_with_index do |subsetting, item_index|
          errors.add(:"settings.#{index}.subsetting.#{item_index}.required", I18n.t("errors.messages.required")) unless subsetting.key?("required")
          errors.add(:"setting.#{index}.subsetting.#{item_index}.label", I18n.t("errors.messages.required")) unless subsetting.key?("label")
          errors.add(:"setting.#{index}.subsetting.#{item_index}.variable", I18n.t("errors.messages.required")) unless subsetting.key?("variable")
          node_variables << subsetting["variable"]
        end
      else
        errors.add(:"settings.#{index}.required", I18n.t("errors.messages.required")) unless setting.key?("required")
        errors.add(:"settings.#{index}.label", I18n.t("errors.messages.required")) unless setting.key?("label")
        errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.required")) unless setting.key?("variable")
        errors.add(:"settings.#{index}.variable", I18n.t("errors.messages.invalid")) if setting.key?("variable") && setting["variable"] != "sex"
        node_variables << setting["variable"]
      end
    end

    missing_variables = ::Node::NAME_SEX_BIRTHDAY_VARIABLES.flatten - node_variables
    invalid_variables = node_variables - ::Node::NAME_SEX_BIRTHDAY_VARIABLES.flatten

    return errors.add(:settings, I18n.t("errors.messages.missing_keys", keys: missing_variables.join(", "))) if missing_variables.present?
    return errors.add(:settings, I18n.t("errors.messages.invalid_keys", keys: invalid_variables.join(", "))) if invalid_variables.present?
  end

  def validate_name_birthday_tel_email_settings(settings)
    form = NameBirthdayTelEmailForm.new(settings: settings)
    return if form.valid?

    form.error_messages_with_no_human_name.each do |key, messages|
      messages.each { |message| errors.add(key, message) }
    end
  end

  def validate_information_settings(settings)
    form = InformationForm.new(settings: settings)
    return if form.valid?

    form.error_messages_with_no_human_name.each do |key, messages|
      messages.each { |message| errors.add(key, message) }
    end
  end

  def validate_credit_settings(settings)
    return if settings.blank? || !settings.is_a?(Array)

    missing_keys = ::Node::CREDIT_CARD_FORM_KEYS - settings[0]&.keys
    invalid_keys = settings[0]&.keys&.- ::Node::CREDIT_CARD_FORM_KEYS

    return errors.add(:settings, I18n.t("errors.messages.missing_keys", keys: missing_keys.join(", "))) if missing_keys.present?
    return errors.add(:settings, I18n.t("errors.messages.invalid_keys", keys: invalid_keys.join(", "))) if invalid_keys.present?

    form = CreditCardForm.new(settings[0])
    unless form.valid?
      form.error_messages_with_no_human_name.each do |key, messages|
        messages.each do |message|
          errors.add(:"settings.#{key}", message)
        end
      end
    end
  end

  def validate_payment_method_settings(settings)
    return if settings.blank? || !settings.is_a?(Array)

    form = ::PaymentMethods::Form.new(settings[0])
    return if form.valid?

    form.error_messages_with_no_human_name.each do |key, messages|
      messages.each do |message|
        errors.add(:"settings.#{key}", message)
      end
    end
  end

  def validate_body_type(body_type, settings)
    if node_type == "input"
      return errors.add(:body_type, I18n.t("errors.messages.required")) if body_type.blank?

      return errors.add(:body_type, I18n.t("errors.messages.inclusion")) if ::Node::MODULE_TYPE.values.exclude?(body_type)
    end

    return errors.add(:settings, I18n.t("errors.messages.required")) if settings.blank?
    return errors.add(:settings, I18n.t("errors.messages.invalid")) unless settings.is_a? Array
  end

  def validate_settings(settings, parent_index = nil)
    return if settings.blank?

    settings.each_with_index do |setting, index|
      path_index = parent_index ? "#{parent_index}_#{index}" : "#{index}"

      next errors.add(:"setting_#{path_index}", I18n.t("errors.messages.invalid")) unless setting.is_a? Hash

      setting_type = setting.dig("type")
      if setting.dig("settings").is_a?(Array)
        validate_children_settings(setting, path_index)
      else
        validate_setting_type(setting, setting_type, path_index)
      end

      # Special validation for select type with crawler_data
      if setting_type == ::Node::MODULE_TYPE[:SELECT] && setting.dig("crawler_data").present? && settings.length == 1
        validate_select_with_crawler(setting)
      end

      validate_headless_tasks(setting)
    end
  end

  def validate_setting_type(_setting, setting_type, path_index)
    return unless ::Node::REQUIRED_SETTING_TYPE.include?(node_type)

    body_type = body.dig("type")
    return if ::Node::NON_REQUIRED_INPUT_TYPE.include?(body_type)

    if setting_type.blank?
      errors.add(:"setting_type_#{path_index}", I18n.t("errors.messages.required"))
    elsif "::Node::#{node_type.upcase}_TYPE".constantize.values.exclude?(setting_type)
      errors.add(:"setting_type_#{path_index}", I18n.t("errors.messages.inclusion"))
    end
  end

  def validate_children_settings(setting, path_index)
    children_setting = setting.dig("settings")
    return if children_setting.blank?

    validate_settings(children_setting, path_index)
  end

  # Validate headless task settings
  def validate_headless_tasks(setting)
    return unless node_type == "headless_tasks"

    task = setting.dig("task")
    validate_task(task)
    validate_task_inputs(setting)
    validate_task_outputs(setting)
  end

  def validate_task(task)
    return errors.add(:"setting.task", I18n.t("errors.messages.required")) if task.blank?

    class_name = task.dig("class_name")
    return errors.add(:"setting.task.class_name", I18n.t("errors.messages.required")) if class_name.blank?
    return errors.add(:"setting.task.class_name", I18n.t("errors.messages.inclusion")) unless Task.exists?(class_name: class_name)
  end

  def validate_task_inputs(setting)
    inputs = setting.dig("inputs")
    return errors.add(:"setting.inputs", I18n.t("errors.messages.required")) if inputs.blank?

    url = inputs.dig("url")
    url_value = url&.dig("value")
    if url_value.blank?
      errors.add(:"setting.inputs.url", I18n.t("errors.messages.required"))
    elsif !url_value.match?(::UrlFormatValidator::URL_FORMAT)
      errors.add(:"setting.inputs.url", I18n.t("errors.messages.invalid"))
    end
  end

  def validate_task_outputs(setting)
    outputs = setting.dig("outputs")
    return errors.add(:"setting.outputs", I18n.t("errors.messages.required")) if outputs.blank?

    data = outputs.dig("data")
    return errors.add(:"setting.outputs.data", I18n.t("errors.messages.inclusion")) if data.blank?
  end

  # Validates a select node that uses crawler data for dynamic options
  # @param setting [Hash] The setting hash containing the select configuration
  # @param path_index [String] The path index for error messages
  #
  # Validates the following structure:
  # {
  #   "label": "Select Option",
  #   "variable": "dynamic_option",
  #   "type": "select",
  #   "required": true,
  #   "crawler_data": {
  #     "task": {
  #       "class_name": "YourCrawlerClass"  # Required
  #     },
  #     "inputs": {
  #       "url": {                          # Required
  #         "value": "https://example.com"  # Required, must be valid URL
  #       }
  #     },
  #     "outputs": {
  #       "data": {                         # Required
  #         "message": "...",              # Required
  #         "error": "...",                # Required
  #         "optionValues": []              # Required, will contain options
  #       }
  #     }
  #   },
  #   "ui_texts": {                        # Optional
  #     "button": "Load Options",          # Optional, default shown
  #     "loading": "Loading...",           # Optional, default shown
  #     "error": "Error message",          # Optional, default shown
  #     "success": "Success message",      # Optional, default shown
  #     "options_suffix": "options"        # Optional, default shown
  #   },
  #   "options": []  # Must be empty array when crawler_data is present
  # }
  def validate_select_with_crawler(setting)


    # 1. Validate crawler_data exists and is a hash
    crawler_data = setting["crawler_data"]
    if crawler_data.blank? || !crawler_data.is_a?(Hash)
      return errors.add(:"setting", I18n.t("errors.messages.required"))
    end

    # 2. Validate task structure
    task = crawler_data["task"]
    if task.blank? || !task.is_a?(Hash)
      errors.add(:"setting.task", I18n.t("errors.messages.required"))
    else
      validate_task(task)
    end

    # 3. Validate inputs structure and URL
    inputs = crawler_data
    if inputs.blank? || !inputs.is_a?(Hash)
      errors.add(:"setting.inputs", I18n.t("errors.messages.required"))
    else
      validate_task_inputs(inputs)
    end

    # 4. Validate outputs.data structure
    outputs = crawler_data["outputs"]
    if outputs.blank? || !outputs.is_a?(Hash)
      errors.add(:"setting.outputs", I18n.t("errors.messages.required"))
    elsif outputs["data"].blank? || !outputs["data"].is_a?(Hash)
      errors.add(:"setting.outputs.data", I18n.t("errors.messages.required"))
    else
      data = outputs["data"]
      required_keys = ["message", "error", "optionValues"]
      
      # Check for missing required keys
      missing_keys = required_keys.reject { |key| data.key?(key) }
      if missing_keys.any?
        errors.add(
          :"setting.outputs.data", 
          I18n.t("errors.messages.missing_keys", keys: missing_keys.join(", "), default: "missing required keys: %{keys}")
        )
      end
      
      # Validate optionValues is an array if present
      if data.key?("optionValues") && !data["optionValues"].is_a?(Array)
        errors.add(:"setting.outputs.data.optionValues", I18n.t("errors.messages.not_an_array"))
      end
    end

    # 5. Validate options is empty array when crawler_data is present
    if !setting["options"] 
      errors.add(:"setting.options", I18n.t("errors.messages.required"))
    end

    if setting["options"].present? && setting["options"] != []
      errors.add(
        :"setting.options", 
        I18n.t("errors.messages.must_be_empty_when_crawler_data_present", default: "must be empty when crawler_data is present")
      )
    end

    # 6. Validate ui_texts if present
    if crawler_data.key?("ui_texts")
      ui_texts = crawler_data["ui_texts"]
      
      if ui_texts.is_a?(Hash)
        allowed_keys = ["button", "loading", "error", "success", "options_suffix"]
        invalid_keys = ui_texts.keys - allowed_keys
        if invalid_keys.any?
          errors.add(
            :"setting.ui_texts",
            I18n.t(
              "errors.messages.invalid_keys",
              keys: invalid_keys.join(", "),
              default: "contains invalid keys: %{keys}"
            )
          )
        end
        
        # Validate each UI text is a string
        ui_texts.each do |key, value|
          unless value.is_a?(String)
            errors.add(
              :"setting.ui_texts.#{key}",
              I18n.t("errors.messages.invalid_type", type: "String")
            )
          end
        end
      else
        errors.add(:"setting.ui_texts", I18n.t("errors.messages.invalid_type", type: "Hash"))
      end
    end
  end
end
