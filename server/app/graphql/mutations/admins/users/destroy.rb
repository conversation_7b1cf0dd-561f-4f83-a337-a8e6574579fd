module Mutations
  module Admins
    module Users
      class Destroy < Base
        graphql_name "AdminsUsersDestroy"
        description "AdminsUsersDestroy"

        argument :id, ID, required: false, description: "User Id"

        field :message, String, null: true, description: "Destroy Message"

        def resolve(id:)
          user = ::User.find(id)

          if user.destroy
            {
              message: "Destroy Successfully",
            }
          else
            execution_error(errors: user.errors.messages, message: "Destroy Failed")
          end
        end
      end
    end
  end
end
