module Mutations
  module Admins
    module Users
      class Update < Base
        graphql_name "AdminsUsersUpdate"
        description "AdminsUsersUpdate"

        argument :id, ID, required: false, description: "User Id"
        argument :input, ::Types::Arguments::UserInput, required: false, description: "Data User Input"

        field :message, String, null: true, description: "Updated Message"
        field :user, ::Types::Payloads::Admins::UserType, null: true, description: "Updated User"

        def resolve(input:, id:)
          user = ::User.find(id)

          form = ::UserForm.new.assign_model(
            user, input.to_h
          )

          if form.save
            {
              message: "Updated Successfully",
              user: form.model,
            }
          else
            execution_error(errors: form.error_messages, message: "Update Failed")
          end
        end
      end
    end
  end
end
