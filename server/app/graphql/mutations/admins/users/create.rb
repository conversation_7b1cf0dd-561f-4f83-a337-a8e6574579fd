module Mutations
  module Admins
    module Users
      class Create < Base
        graphql_name "AdminsUsersCreate"
        description "AdminsUsersCreate"

        argument :input, ::Types::Arguments::UserInput, required: false, description: "Data User Input"

        field :message, String, null: true, description: "Created Message"
        field :user, ::Types::Payloads::Admins::UserType, null: true, description: "Created User"

        def resolve(input:)
          form = ::UserForm.new.assign_model(
            ::User.new, input.to_h
          )

          if form.save
            {
              message: "Created Successfully",
              user: form.model,
            }
          else
            execution_error(errors: form.error_messages, message: "Create Failed")
          end
        end
      end
    end
  end
end
