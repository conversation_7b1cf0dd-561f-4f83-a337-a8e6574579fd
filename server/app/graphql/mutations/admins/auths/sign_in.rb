module Mutations
  module Admins
    module Auths
      class SignIn < BaseMutation
        graphql_name "AdminsAuthsSignIn"
        description "AdminsAuthsSignIn"

        argument :email, String, required: false, description: "Email"
        argument :password, String, required: false, description: "Password"
        argument :remember_me, <PERSON><PERSON><PERSON>, required: false, description: "Remember Me"

        field :message, String, null: true, description: "Sign In Message"
        field :token, String, null: true, description: "Authencity Token"

        def resolve(email:, password:, remember_me:)
          result = ::Admins::SignInService.call(email:, password:, remember_me:)

          if result.success?
            {
              token: result.token,
              message: I18n.t("devise.sessions.signed_in"),
            }
          else
            execution_error(message: result.message, status: :unauthorized, code: 401)
          end
        end
      end
    end
  end
end
