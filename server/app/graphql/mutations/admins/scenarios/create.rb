module Mutations
  module Admins
    module Scenarios
      class Create < Base
        graphql_name "AdminsScenariosCreate"
        description "AdminsScenariosCreate"

        argument :input, ::Types::Arguments::ScenarioInput, required: false, description: "Data Scenario Input"

        field :message, String, null: true, description: "Created Message"
        field :scenario, ::Types::Payloads::Admins::ScenarioType, null: true, description: "Created Scenario"

        def resolve(input:)
          scenario = Current.user.scenarios.new

          form = ::ScenarioForm.new.assign_model(
            scenario, input.to_h
          )

          if form.save
            {
              message: "Successfully",
              scenario: form.model,
            }
          else
            execution_error(errors: form.error_messages, message: "Create Failed")
          end
        end
      end
    end
  end
end
