module Mutations
  module Admins
    module Scenarios
      class Destroy < Base
        graphql_name "AdminsScenariosDestroy"
        description "AdminsScenariosDestroy"

        argument :id, ID, required: false, description: "Scenario ID"

        field :message, String, null: true, description: "Destroyed Message"
        field :scenario, ::Types::Payloads::Admins::ScenarioType, null: true, description: "Updated Scenario"

        def resolve(id:)
          scenario = ::Scenario.in_charge_of(Current.user).find(id)

          if scenario.destroy
            {
              message: "Successfully",
              scenario: scenario,
            }
          else
            execution_error(errors: scenario.errors.messages, message: "Destroy Failed")
          end
        end
      end
    end
  end
end
