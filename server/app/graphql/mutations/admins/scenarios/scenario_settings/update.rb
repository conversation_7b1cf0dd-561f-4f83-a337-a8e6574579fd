module Mutations
  module Admins
    module Scenarios
      module ScenarioSettings
        class Update < Base
          graphql_name "AdminsScenarioSettingsUpdate"
          description "AdminsScenarioSettingsUpdate"

          argument :id, ID, required: false, description: "Scenario Setting ID"
          argument :input, ::Types::Arguments::ScenarioSettingInput, required: false, description: "Data Scenario Setting Input"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Updated Message"
          field :scenario_setting, ::Types::Payloads::Admins::ScenarioSettingType, null: true, description: "Updated Scenario Setting"

          def resolve(id:, input:, scenario_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            scenario_setting = scenario.scenario_settings.find(id)

            form = ::ScenarioSettings::Form.new.assign_model(
              scenario_setting, input.to_h
            )

            if form.save
              {
                message: "Successfully",
                scenario_setting: form.model,
              }
            else
              execution_error(errors: form.error_messages, message: "Update Failed")
            end
          end
        end
      end
    end
  end
end
