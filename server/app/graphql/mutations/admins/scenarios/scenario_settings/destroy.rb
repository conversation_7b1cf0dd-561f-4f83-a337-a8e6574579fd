module Mutations
  module Admins
    module Scenarios
      module ScenarioSettings
        class Destroy < Base
          graphql_name "AdminsScenarioSettingsDestroy"
          description "AdminsScenarioSettingsDestroy"

          argument :id, ID, required: false, description: "Data Scenario Setting Id"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Destroy Message"

          def resolve(id:, scenario_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            scenario_setting = scenario.scenario_settings.find(id)

            if scenario_setting.destroy
              {
                message: "Destroy Successfully",
              }
            else
              execution_error(errors: scenario_setting.errors.messages, message: "Destroy Failed")
            end
          end
        end
      end
    end
  end
end
