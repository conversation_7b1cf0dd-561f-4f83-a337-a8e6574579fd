module Mutations
  module Admins
    module Scenarios
      module ScenarioSettings
        class Create < Base
          graphql_name "AdminsScenarioSettingsCreate"
          description "AdminsScenarioSettingsCreate"

          argument :input, ::Types::Arguments::ScenarioSettingInput, required: false, description: "Data Scenario Setting Input"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Created Message"
          field :scenario_setting, ::Types::Payloads::Admins::ScenarioSettingType, null: true, description: "Created Scenario Setting"

          def resolve(input:, scenario_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            form = ::ScenarioSettings::Form.new.assign_model(
              scenario.scenario_settings.new, input.to_h
            )

            if form.save
              {
                message: "Successfully",
                scenario_setting: form.model,
              }
            else
              execution_error(errors: form.error_messages, message: "Create Failed")
            end
          end
        end
      end
    end
  end
end
