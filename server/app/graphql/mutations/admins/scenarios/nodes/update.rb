module Mutations
  module Admins
    module Scenarios
      module Nodes
        class Update < Base
          graphql_name "AdminsScenariosNodesUpdate"
          description "AdminsScenariosNodesUpdate"

          argument :input, ::Types::Arguments::NodeInput, required: false, description: "Data Node Input"
          argument :node_id, ID, required: false, description: "Node Id"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Updated Message"
          field :node, ::Types::Payloads::NodeType, null: true, description: "Updated Node"

          def resolve(scenario_id:, node_id:, input:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)
            node = scenario.nodes.find(node_id)

            form = ::NodeForm.new.assign_model(node, input.to_h)

            if form.save
              {
                message: "Successfully",
                node: form.model,
              }
            else
              execution_error(errors: form.error_messages, message: "Update Failed")
            end
          end
        end
      end
    end
  end
end
