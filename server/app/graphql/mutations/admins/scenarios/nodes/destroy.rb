module Mutations
  module Admins
    module Scenarios
      module Nodes
        class Destroy < Base
          graphql_name "AdminsScenariosNodesDestroy"
          description "AdminsScenariosNodesDestroy"

          argument :node_id, ID, required: false, description: "Node Id"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Destroyed Message"
          field :node, ::Types::Payloads::NodeType, null: true, description: "Destroyed Node"

          def resolve(scenario_id:, node_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            node = scenario.nodes.find(node_id)

            if node.process_destroy
              {
                message: "Successfully",
                node: node,
              }
            else
              execution_error(errors: node.errors.messages, message: "Destroy Failed")
            end
          end
        end
      end
    end
  end
end
