module Mutations
  module Admins
    module Scenarios
      module Nodes
        class Create < Base
          graphql_name "AdminsScenariosNodesCreate"
          description "AdminsScenariosNodesCreate"

          argument :input, ::Types::Arguments::NodeInput, required: false, description: "Data Node Input"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          field :message, String, null: true, description: "Created Message"
          field :node, ::Types::Payloads::NodeType, null: true, description: "Created Node"

          def resolve(scenario_id:, input:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            form = ::NodeForm.new.assign_model(scenario.nodes.new, input.to_h)

            if form.save
              {
                message: "Successfully",
                node: form.model,
              }
            else
              execution_error(errors: form.error_messages, message: "Create Failed")
            end
          end
        end
      end
    end
  end
end
