module Mutations
  module Admins
    module Scenarios
      class Update < Base
        graphql_name "AdminsScenariosUpdate"
        description "AdminsScenariosUpdate"

        argument :id, ID, required: false, description: "Scenario ID"
        argument :input, ::Types::Arguments::ScenarioInput, required: false, description: "Data Scenario Input"

        field :message, String, null: true, description: "Updated Message"
        field :scenario, ::Types::Payloads::Admins::ScenarioType, null: true, description: "Updated Scenario"

        def resolve(id:, input:)
          scenario = ::Scenario.in_charge_of(Current.user).find(id)

          form = ::ScenarioForm.new.assign_model(
            scenario, input.to_h
          )

          if form.save
            {
              message: "Successfully",
              scenario: form.model,
            }
          else
            execution_error(errors: form.error_messages, message: "Update Failed")
          end
        end
      end
    end
  end
end
