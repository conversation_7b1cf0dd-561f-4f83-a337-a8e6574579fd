module Mutations
  module Scenarios
    class HandleResponseResult < BaseMutation
      graphql_name "ScenariosHandleResponseResult"
      description "ScenariosHandleResponseResult"

      argument :input, ::Types::Arguments::HandleResponseInput, required: false, description: "Input Data"

      field :message, String, null: true, description: "Updated Message"

      def resolve(input:)
        response_data = input[:data]
        ssid = input[:ssid]
        user_id = input[:user_id]

        ActionCable.server.broadcast("system_message_channel_#{user_id}_#{ssid}", { data: response_data })

        {
          message: "success",
        }
      end
    end
  end
end
