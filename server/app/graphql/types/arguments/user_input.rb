module Types
  module Arguments
    class UserInput < Types::BaseInputObject
      graphql_name "AdminsUserInput"
      description "AdminsUserInput"

      argument :email, String, required: false, description: "User Email"
      argument :password, String, required: false, description: "User Password"
      argument :shop_id, String, required: false, description: "Shop Id"
      argument :user_type, String, required: false, description: "User Type"
    end
  end
end
