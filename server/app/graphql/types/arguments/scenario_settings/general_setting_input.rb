module Types
  module Arguments
    module ScenarioSettings
      class GeneralSettingInput < Types::BaseInputObject
        graphql_name "AdminsScenarioSettingsGeneralSettingInput"
        description "AdminsScenarioSettingsGeneralSettingInput"

        ::ScenarioSetting::GENERAL_SETTINGS_ATTRIBUTES.each do |item|
          argument :"#{item[:key]}", item[:type], required: false, description: "#{item[:key]}"
        end
      end
    end
  end
end
