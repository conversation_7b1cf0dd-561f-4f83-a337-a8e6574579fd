module Types
  module Arguments
    module ScenarioSettings
      class DesignSettingInput < Types::BaseInputObject
        graphql_name "AdminsScenarioSettingsDesignSettingInput"
        description "AdminsScenarioSettingsDesignSettingInput"

        ::ScenarioSetting::DESIGN_SETTINGS_ATTRIBUTES.each do |item|
          argument :"#{item[:key]}", item[:type], required: false, description: "#{item[:key]}"
        end
      end
    end
  end
end
