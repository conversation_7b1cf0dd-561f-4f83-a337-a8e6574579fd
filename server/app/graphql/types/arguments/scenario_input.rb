module Types
  module Arguments
    class ScenarioInput < Types::BaseInputObject
      graphql_name "AdminsScenarioInput"
      description "AdminsScenarioInput"

      argument :active, Boolean, required: false, description: "Scenario Active"
      argument :description, String, required: false, description: "Scenario Description"
      argument :match_type, String, required: false, description: "Scenario Match Type"
      argument :match_value, String, required: false, description: "Scenario Match Value"
      argument :name, String, required: false, description: "Scenario Name"
      argument :progress_bar_enable, Boolean, required: false, description: "Progress Bar Enable"
      argument :root_node_uid, String, required: false, description: "Scenario Root Node Uid"
      argument :support_ui_enable, Boolean, required: false, description: "Support Ui Enable"
    end
  end
end
