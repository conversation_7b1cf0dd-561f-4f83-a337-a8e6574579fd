module Types
  module Arguments
    class ScenarioSettingInput < Types::BaseInputObject
      graphql_name "AdminsScenarioSettingInput"
      description "AdminsScenarioSettingInput"

      argument :active, Boolean, required: false, description: "Is Setting Acitve ?"
      argument :description, String, required: false, description: "Description"
      argument :name, String, required: false, description: "Name"
      argument :setting_type, String, required: false, description: "Setting Type"

      argument :css_customize, String, required: false, description: "Css Customize"
      argument :javascript_customize, String, required: false, description: "Javascript Customize"

      argument :general_settings, ::Types::Arguments::ScenarioSettings::GeneralSettingInput, required: false, description: "General Setting"
      argument :theme_customize_settings, ::Types::Arguments::ScenarioSettings::DesignSettingInput, required: false, description: "Theme Customize Settings"
    end
  end
end
