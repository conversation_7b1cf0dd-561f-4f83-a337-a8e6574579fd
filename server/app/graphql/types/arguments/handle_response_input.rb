module Types
  module Arguments
    class HandleResponseInput < Types::BaseInputObject
      graphql_name "ScenariosHandleResponseInput"
      description "ScenariosHandleResponseInput"

      argument :data, BaseScalar, required: false, description: "Data"
      argument :ssid, String, required: false, description: "Session Id"
      argument :user_id, ID, required: false, description: "User ID"
    end
  end
end
