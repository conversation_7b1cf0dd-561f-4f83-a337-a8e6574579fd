module Types
  module Arguments
    class NodeInput < Types::BaseInputObject
      graphql_name "ScenariosNodeInput"
      description "ScenariosNodeInput"

      argument :body, BaseScalar, required: false, description: "Body"
      argument :label, String, required: false, description: "Label"
      argument :next_node_uid, [String], required: false, description: "Next Node Uid"
      argument :node_type, String, required: false, description: "Node Type"
      argument :position, BaseScalar, required: false, description: "Drag-and-drop coordinates for node"
      argument :root_node, <PERSON><PERSON><PERSON>, required: false, description: "Is it the root node ?"
    end
  end
end
