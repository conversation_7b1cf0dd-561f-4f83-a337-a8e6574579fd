module Types
  module Payloads
    class NodeType < Types::BaseObject
      graphql_name "NodeType"
      description "NodeType"

      field :body, Types::BaseScalar, null: true, description: "Node Body"
      field :label, String, null: true, description: "Node Label"
      field :next_node_uid, [String], null: true, description: "Next Node Uid"
      field :node_type, String, null: true, description: "Node Type"
      field :position, Types::BaseScalar, null: true, description: "Drag-and-drop coordinates for node"
      field :root_node, Boolean, null: true, description: "Is it the root node ?"
      field :uid, ID, null: true, description: "Node Id", method: :id
    end
  end
end
