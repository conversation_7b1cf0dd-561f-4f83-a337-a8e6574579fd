module Types
  module Payloads
    module ScenarioSettings
      class DesignAndGeneralType < Types::BaseObject
        graphql_name "ScenarioDesignAndGeneralType"
        description "ScenarioDesignAndGeneralType"

        field :scenario_design_setting, DesignType, null: true, description: "Scenario Setting General", method: :scenario_design_setting_active
        field :scenario_general_setting, GeneralType, null: true, description: "Scenario Setting Design", method: :scenario_general_setting_active
      end
    end
  end
end
