module Types
  module Payloads
    module Sc<PERSON>rioSettings
      class DesignType < Types::BaseObject
        graphql_name "ScenarioSettingDesignType"
        description "ScenarioSettingDesignType"

        implements ::Types::Payloads::Shared::CommonSettingInterface

        ::ScenarioSetting::DESIGN_SETTINGS_ATTRIBUTES.each do |item|
          field :"#{item[:key]}", item[:type], null: true, description: "#{item[:key]}"
        end
      end
    end
  end
end
