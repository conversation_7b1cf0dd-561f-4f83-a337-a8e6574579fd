module Types
  module Payloads
    module ScenarioSettings
      class GeneralType < Types::BaseObject
        graphql_name "ScenarioSettingGeneralType"
        description "ScenarioSettingGeneralType"

        implements ::Types::Payloads::Shared::CommonSettingInterface

        ::ScenarioSetting::GENERAL_SETTINGS_ATTRIBUTES.each do |item|
          field :"#{item[:key]}", item[:type], null: true, description: "#{item[:key]}"
        end
      end
    end
  end
end
