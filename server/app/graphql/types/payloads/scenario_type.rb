module Types
  module Payloads
    class ScenarioType < Types::BaseObject
      graphql_name "ScenarioType"
      description "ScenarioType"

      field :id, String, null: true, description: "Scenario Id"
      field :nodes, [::Types::Payloads::NodeType], null: true, description: "Nodes"
      field :progress_bar_enable, Bo<PERSON>an, null: true, description: "Progress Bar Enable"
      field :root_node_uid, String, null: true, description: "Root Node Uid"
      field :sdata, String, null: true, description: "Session Data"
      field :shop_id, String, null: true, description: "Shop Id"
      field :ssid, String, null: true, description: "Session Id"
      field :support_ui_enable, <PERSON><PERSON><PERSON>, null: true, description: "Support Ui Enable"
    end
  end
end
