module Types
  module Payloads
    class GetKanaType < Types::BaseObject
      graphql_name "GetKanaType"
      description "GetKanaType"

      field :first, String, null: true, description: "first"
      field :first_kana, String, null: true, description: "firstKana"
      field :last, String, null: true, description: "last"
      field :last_kana, String, null: true, description: "lastKana"
    end
  end
end
