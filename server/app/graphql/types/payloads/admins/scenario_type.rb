module Types
  module Payloads
    module Admins
      class ScenarioType < Types::BaseObject
        graphql_name "AdminsScenarioType"
        description "AdminsScenarioType"

        field :active, Boolean, null: true, description: "Is Active"
        field :description, String, null: true, description: "Description"
        field :id, ID, null: true, description: "ID"
        field :match_type, String, null: true, description: "Match Type"
        field :match_value, String, null: true, description: "Match Value"
        field :name, String, null: true, description: "Name"
        field :nodes, [::Types::Payloads::NodeType], null: true, description: "Nodes"
        field :progress_bar_enable, Bo<PERSON>an, null: true, description: "Progress Bar Enable"
        field :root_node_uid, String, null: true, description: "Root Node Uid"
        field :support_ui_enable, <PERSON><PERSON><PERSON>, null: true, description: "Support Ui Enable"
        field :user_id, String, null: true, description: "User Id"
      end
    end
  end
end
