module Types
  module Payloads
    module Admins
      class UserType < Types::BaseObject
        graphql_name "UserType"
        description "UserType"

        field :email, String, null: true, description: "User Email"
        field :id, ID, null: true, description: "User ID"
        field :shop_id, String, null: true, description: "Shop Id"
        field :user_type, String, null: true, description: "User Type"
      end
    end
  end
end
