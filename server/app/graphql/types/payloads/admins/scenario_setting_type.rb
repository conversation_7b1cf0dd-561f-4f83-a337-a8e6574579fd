module Types
  module Payloads
    module Admins
      class ScenarioSettingType < Types::BaseObject
        graphql_name "AdminsScenarioSettingType"
        description "AdminsScenarioSettingType"

        implements ::Types::Payloads::Shared::CommonSettingInterface

        field :general_settings, GeneralSettingType, null: true, description: "General Settings"
        field :theme_customize_settings, DesignSettingType, null: true, description: "Design Settings"
      end
    end
  end
end
