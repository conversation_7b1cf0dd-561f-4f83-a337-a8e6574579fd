module Types
  module Payloads
    module Shared
      module CommonSettingInterface # rubocop:disable GraphQL/ObjectDescription
        include Types::BaseInterface

        field :css_customize, String, null: true, description: "Css Customize"
        field :javascript_customize, String, null: true, description: "Javascript Customize"

        field :description, String, null: true, description: "Description"
        field :active, Boolean, null: true, description: "Is Active"
        field :name, String, null: true, description: "Name"
        field :id, String, null: true, description: "Id"
        field :setting_type, String, null: true, description: "Setting Type"
      end
    end
  end
end
