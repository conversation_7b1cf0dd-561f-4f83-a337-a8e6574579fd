module Types
  class MutationType < Types::BaseObject
    description "MutationType"

    # Admin #
    field :admins_sign_in, mutation: ::Mutations::Admins::Auths::SignIn, description: "Admin Sign In"

    ## Scenario Setting ##
    field :admins_scenario_settings_create, mutation: ::Mutations::Admins::Scenarios::ScenarioSettings::Create, description: "Admin Create Scenario Setting"
    field :admins_scenario_settings_destroy, mutation: ::Mutations::Admins::Scenarios::ScenarioSettings::Destroy, description: "Admin Destroy Scenario Setting"
    field :admins_scenario_settings_update, mutation: ::Mutations::Admins::Scenarios::ScenarioSettings::Update, description: "Admin Update Scenario Setting"

    ## Scenario ##
    field :admins_scenarios_create, mutation: ::Mutations::Admins::Scenarios::Create, description: "Admin Create Scenario"
    field :admins_scenarios_destroy, mutation: ::Mutations::Admins::Scenarios::Destroy, description: "Admin Destroy Scenario"
    field :admins_scenarios_update, mutation: ::Mutations::Admins::Scenarios::Update, description: "Admin Update Scenario"

    ## User ##
    field :admins_users_create, mutation: ::Mutations::Admins::Users::Create, description: "Admin Create User"
    field :admins_users_destroy, mutation: ::Mutations::Admins::Users::Destroy, description: "Admin Destroy User"
    field :admins_users_update, mutation: ::Mutations::Admins::Users::Update, description: "Admin Update User"

    # ### Node ###
    field :admins_scenarios_nodes_create, mutation: ::Mutations::Admins::Scenarios::Nodes::Create, description: "Admin Create Node"
    field :admins_scenarios_nodes_destroy, mutation: ::Mutations::Admins::Scenarios::Nodes::Destroy, description: "Admin Destroy Node"
    field :admins_scenarios_nodes_update, mutation: ::Mutations::Admins::Scenarios::Nodes::Update, description: "Admin Update Node"

    # Public #
    ## Scenario ##
    field :scenarios_handle_response_result, mutation: ::Mutations::Scenarios::HandleResponseResult, description: "Handle Response Result"
  end
end
