module Types
  class QueryType < Types::BaseObject
    description "QueryType"

    field :fetch_scenario, resolver: ::Resolvers::Scenarios::FetchData, description: "Scenario Detail"
    field :fetch_scenario_setting, resolver: ::Resolvers::ScenarioSettings::FetchDesignAndGeneral, description: "Scenario Setting"
    field :get_kana_name, resolver: ::Resolvers::ConvertName::GetKana, description: "GetKana"
    field :scheduled_date_option, resolver: ::Resolvers::ScheduledDate::FetchOption, description: "FetchOption"

    # Admin #

    ## Scenario ##
    field :admin_scenario, resolver: ::Resolvers::Admins::Scenarios::Show, description: "Admin Scenarios Show"
    field :admin_scenarios, resolver: ::Resolvers::Admins::Scenarios::Index, description: "Admin Scenarios List"

    ## User ##
    field :admin_user, resolver: ::Resolvers::Admins::Users::Show, description: "Admin Users Show"
    field :admin_users, resolver: ::Resolvers::Admins::Users::Index, description: "Admin Users List"

    field :tasks, resolver: ::Resolvers::Admins::Tasks::Index, description: " Tasks List"

    ## Scenario Setting ##
    field :admin_scenario_setting, resolver: ::Resolvers::Admins::Scenarios::ScenarioSettings::Show, description: "Admin Scenario Setting"
    field :admin_scenario_settings, resolver: ::Resolvers::Admins::Scenarios::ScenarioSettings::Index, description: "Admin Scenario Settings"
  end
end
