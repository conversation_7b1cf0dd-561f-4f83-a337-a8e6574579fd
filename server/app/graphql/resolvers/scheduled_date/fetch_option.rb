module Resolvers
  module ScheduledDate
    class FetchOption < BaseResolver
      graphql_name "ScheduledDateFetchOption"
      description "Fetch Option"

      argument :days_after_current, String, required: false, description: "days_after_current"
      argument :range_days, String, required: false, description: "range_days"

      type ::Types::BaseScalar, null: true

      def resolve(days_after_current:, range_days:)
        if days_after_current.present? && range_days.present?
          service = ::Admins::ScheduleDateCalculatorService.new(days_after_current.to_i, range_days.to_i)

          { scheduled_date_option: service.scheduled_dates }
        else
          { scheduled_date_option: [] }
        end
      end
    end
  end
end
