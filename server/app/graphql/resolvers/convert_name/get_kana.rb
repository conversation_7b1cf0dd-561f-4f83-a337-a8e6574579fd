module Resolvers
  module ConvertName
    class GetKana < BaseResolver
      graphql_name "ConvertNameGetKana"
      description "ConvertNameGetKana"

      argument :full_name, String, required: false, description: "Name"

      type ::Types::Payloads::GetKanaType, null: true

      def resolve(full_name:)
        object = ::ConvertName.get_kana_readings(full_name)

        {
          last: object[:last],
          first: object[:first],
          last_kana: object[:lastKana],
          first_kana: object[:firstKana],
        }
      end
    end
  end
end
