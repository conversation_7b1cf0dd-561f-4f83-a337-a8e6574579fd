module Resolvers
  module Admins
    module Users
      class Index < Base
        graphql_name "AdminsUsersIndex"
        description "AdminsUsersIndex"

        argument :input, ::Types::Arguments::PagyInput, required: false, description: "Query Input"

        type Types::Payloads::Admins::UserType.pagy_type, null: true

        def resolve(input: {})
          q = input[:q] || {}

          collection = ::User.
            ransack(q).
            result(distinct: true).
            order(created_at: :desc)

          pagy(collection, per_page: input[:per_page], page: input[:page])
        end
      end
    end
  end
end
