module Resolvers
  module Admins
    module Scenarios
      module ScenarioSettings
        class Show < Base
          graphql_name "AdminsScenarioSettingsShow"
          description "AdminsScenarioSettingsShow"

          argument :id, ID, required: false, description: "Scenario Setting Id"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          type ::Types::Payloads::Admins::ScenarioSettingType, null: true

          def resolve(id:, scenario_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            scenario.scenario_settings.find(id)
          end
        end
      end
    end
  end
end
