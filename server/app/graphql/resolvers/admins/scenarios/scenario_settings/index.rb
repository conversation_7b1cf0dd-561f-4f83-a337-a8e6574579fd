module Resolvers
  module Admins
    module Scenarios
      module ScenarioSettings
        class Index < Base
          graphql_name "AdminsScenarioSettingsIndex"
          description "AdminsScenarioSettingsIndex"

          argument :input, ::Types::Arguments::PagyInput, required: false, description: "Query Input"
          argument :scenario_id, ID, required: false, description: "Scenario Id"

          type ::Types::Payloads::Admins::ScenarioSettingType.pagy_type, null: true

          def resolve(input: {}, scenario_id:)
            scenario = ::Scenario.in_charge_of(Current.user).find(scenario_id)

            q = input[:q] || {}

            collection = scenario.scenario_settings.
              ransack(q).
              result(distinct: true).
              order(created_at: :desc)

            pagy(collection, per_page: input[:per_page], page: input[:page])
          end
        end
      end
    end
  end
end
