module Resolvers
  module Admins
    module Scenarios
      class Index < Base
        graphql_name "AdminsScenariosIndex"
        description "AdminsScenariosIndex"

        argument :input, ::Types::Arguments::PagyInput, required: false, description: "Query Input"

        type Types::Payloads::Admins::ScenarioType.pagy_type, null: true

        def resolve(input: {})
          q = input[:q] || {}
          collection = ::Scenario.
            in_charge_of(Current.user).
            ransack(q).
            result(distinct: true).
            order(created_at: :desc)

          pagy(collection, per_page: input[:per_page], page: input[:page])
        end
      end
    end
  end
end
