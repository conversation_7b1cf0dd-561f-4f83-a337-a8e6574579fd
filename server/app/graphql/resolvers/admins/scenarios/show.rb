module Resolvers
  module Admins
    module Scenarios
      class Show < Base
        graphql_name "AdminsScenariosShow"
        description "AdminsScenariosShow"

        argument :id, ID, required: false, description: "Scenario Id"

        type Types::Payloads::Admins::ScenarioType, null: true

        def resolve(id:)
          ::Scenario.in_charge_of(Current.user).find(id)
        end
      end
    end
  end
end
