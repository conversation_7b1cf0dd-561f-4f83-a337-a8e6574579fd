module Resolvers
  module Admins
    module Tasks
      class Index < Base
        graphql_name "AdminsTasksIndex"
        description "AdminsTasksIndex"

        argument :input, ::Types::Arguments::PagyInput, required: false, description: "Query Input"

        type Types::Payloads::TaskType.pagy_type, null: true

        def resolve(input: {})
          q = input[:q] || {}
          collection = ::Task.
            ransack(q).
            result(distinct: true).
            order(created_at: :desc)

          pagy(collection, per_page: input[:per_page], page: input[:page])
        end
      end
    end
  end
end
