module Resolvers
  module ScenarioSettings
    class FetchDesignAndGeneral < BaseResolver
      graphql_name "ScenarioSettingsFetchDesignAndGeneral"
      description "ScenarioSettingsFetchDesignAndGeneral"

      argument :scenario_id, String, required: false, description: "Scenario Id"
      argument :url, String, required: false, description: "URl"
      argument :user_id, ID, required: false, description: "User Id"

      type ::Types::Payloads::ScenarioSettings::DesignAndGeneralType, null: true

      def resolve(user_id:, url:, scenario_id:)
        scenario = if scenario_id.present?
                     ::Scenario.active.find_by(id: scenario_id, user_id:) || ::Scenario.find_matching_url(user_id, url)
                   else
                     ::Scenario.find_matching_url(user_id, url)
                   end

        return execution_error(message: "Can not find <PERSON><PERSON><PERSON>") if scenario.nil?

        scenario
      end
    end
  end
end
