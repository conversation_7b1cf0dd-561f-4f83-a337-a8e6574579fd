module Resolvers
  module Scenarios
    class FetchData < BaseResolver
      graphql_name "ScenariosFetchData"
      description "ScenariosFetchData"

      argument :scenario_id, String, required: false, description: "SCENARIO ID"
      argument :ssid, String, required: false, description: "Session ID"
      argument :url, String, required: false, description: "SCENARIO URL"
      argument :user_id, ID, required: false, description: "USER ID"

      type ::Types::Payloads::ScenarioType, null: false

      def resolve(user_id:, url:, ssid:, scenario_id:)
        scenario = if scenario_id.present?
                     ::Scenario.active.find_by(id: scenario_id, user_id:) || ::Scenario.find_matching_url(user_id, url)
                   else

                     ::Scenario.find_matching_url(user_id, url)
                   end

        user = ::User.find(user_id)
        return execution_error(message: "Can not find <PERSON><PERSON><PERSON>") if scenario.nil?
        return execution_error(message: "Can not find User") if user.nil?

        shop_id = user.shop_id
        ssid, sdata = scenario.sid_data(ssid)

        { root_node_uid: scenario.root_node_uid,
          nodes: scenario.nodes,
          id: scenario.id,
          ssid:,
          shop_id:,
          sdata: sdata,
          support_ui_enable: scenario.support_ui_enable,
          progress_bar_enable: scenario.progress_bar_enable }
      end
    end
  end
end
