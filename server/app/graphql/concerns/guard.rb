module Guard
  extend ActiveSupport::Concern

  private

  def authenticate!(role)
    authenticated = case role
                    when :admin
                      admin?
                    when :public
                      true
                    when :user
                      user?
                    else
                      false
                    end

    raise UnauthenticatedError unless authenticated

    true
  end

  def admin?
    Current.user &&
      Current.user.instance_of?(User) &&
      Current.user.user_type_admin?
  end

  def user?
    Current.user &&
      Current.user.instance_of?(User)
  end
end
