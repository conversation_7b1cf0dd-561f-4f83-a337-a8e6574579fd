class SystemMessageChannel < ApplicationCable::Channel
  def subscribed
    stream_from "system_message_channel_#{params[:dataAccount]}_#{params[:ssid]}"
  end

  def post(data)
    # cache data form chatbot
    RedisCache.write(params[:ssid], data["data"])
  end

  def task_runner(data)
    ::Tasks::Executor.call(
      class_name: data["className"],
      inputs: data["inputs"],
      outputs: data["outputs"],
      user_id: params[:dataAccount],
      ssid: params[:ssid],
    )
  end

  def task_crawler(data)
    ::Tasks::Crawler.call(
      class_name: data["className"],
      inputs: data["inputs"],
      outputs: data["outputs"],
      user_id: params[:dataAccount],
      ssid: params[:ssid],
    )
  end
end
