module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
    end

    private

    def find_verified_user
      user = ::User.find(request.params.dig("dataAccount"))
      ssid = request.params.dig("ssid")

      if verified_scenario = RedisCache.exists?(ssid) && user
        verified_scenario
      else
        reject_unauthorized_connection
      end
    rescue StandardError => _e
      reject_unauthorized_connection
    end
  end
end
