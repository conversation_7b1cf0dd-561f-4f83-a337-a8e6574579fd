# == Schema Information
#
# Table name: users
#
#  id                 :uuid             not null, primary key
#  email              :string           not null
#  encrypted_password :string           not null
#  lock_version       :integer          not null
#  user_type          :integer          default("admin"), not null
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  shop_id            :string
#
# Indexes
#
#  index_users_on_email  (email) UNIQUE
#
FactoryBot.define do
  factory :user do
    email { Faker::Internet.unique.email }
    password { "12345678" }
  end
end
