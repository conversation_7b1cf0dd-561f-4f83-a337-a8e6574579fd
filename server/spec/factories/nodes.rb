# == Schema Information
#
# Table name: nodes
#
#  id            :uuid             not null, primary key
#  body          :jsonb
#  label         :string
#  lock_version  :integer          not null
#  next_node_uid :string           is an Array
#  node_type     :integer          default("message"), not null
#  position      :jsonb
#  root_node     :boolean          default(FALSE)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  scenario_id   :string           not null
#
# Indexes
#
#  index_nodes_on_scenario_id  (scenario_id)
#
module NodeExtend
  def generate_body(node_type)
    send(:"body_#{node_type}")
  end

  def body_message
    {
      settings: [
        type: "text",
        content: Faker::Lorem.word,
      ],
    }
  end

  def body_input
    {
      type: "text",
      settings: [
        {
          label: Faker::Lorem.word,
          variable: "coupon",
          type: "input",
          placeholder: Faker::Lorem.word,
          required: false,
        },
      ],
    }
  end

  def body_trigger
    {
      settings: [
        {
          task: {
            content: File.read("db/seeds/scenario/iqdum_submit.js"),
          },
        },
      ],
    }
  end

  def body_button
    {
      settings: [
        {
          content: "購入確認画面へ",
        },
      ],
    }
  end

  def body_condition
    {
      settings: [
        {
          variable: "product",
          value: Faker::Lorem.word,
        },
      ],
    }
  end

  def body_modal
    {
      settings: [
        type: "text",
        content: Faker::Lorem.word,
      ],
    }
  end
end

FactoryBot.define do
  factory :node do
    node_type { ::Node.node_types.keys.sample }
    label { Faker::Lorem.word }
    root_node { [true, false].sample }

    scenario

    after(:build) do |node, _evaluator|
      extend NodeExtend

      node.body = generate_body(node.node_type)
    end
  end
end
