# == Schema Information
#
# Table name: scenarios
#
#  id                :uuid             not null, primary key
#  active            :boolean          default(FALSE)
#  data_json         :jsonb
#  description       :string
#  lock_version      :integer          not null
#  match_type        :integer          default("match"), not null
#  match_value       :string           not null
#  name              :string           not null
#  root_node_uid     :string
#  support_ui_enable :boolean          default(FALSE)
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  user_id           :string           not null
#
# Indexes
#
#  index_scenarios_on_user_id  (user_id)
#
FactoryBot.define do
  factory :scenario do
    name { Faker::Lorem.unique.word }
    description { Faker::Lorem.word }
    active { [true, false].sample }
    match_value { Faker::Internet.url }

    user
  end
end
