# == Schema Information
#
# Table name: nodes
#
#  id            :uuid             not null, primary key
#  body          :jsonb
#  label         :string
#  lock_version  :integer          not null
#  next_node_uid :string           is an Array
#  node_type     :integer          default("message"), not null
#  position      :jsonb
#  root_node     :boolean          default(FALSE)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  scenario_id   :string           not null
#
# Indexes
#
#  index_nodes_on_scenario_id  (scenario_id)
#
require "rails_helper"

RSpec.describe Node do
  let!(:scenario) { create(:scenario, active: true) }

  describe "associations" do
    it do
      expect(described_class.reflect_on_association(:scenario).macro).to eq(:belongs_to)
    end
  end

  describe ".modify_others_record" do
    let!(:node1) { build(:node, scenario: scenario, root_node: false) }

    context "when node is not root node" do
      it do
        expect(node1.save).to be true
        expect(scenario.root_node_uid).to be_nil
      end
    end

    context "when node is root node" do
      before do
        node1.root_node = true
      end

      it "update root node uid success" do
        expect(node1.save).to be true
        expect(node1.new_record?).to be false
        expect(scenario.root_node_uid).to eq node1.id
      end

      it "update root node uid failed" do
        allow_any_instance_of(::Node).to receive(:update_root_node!).and_raise(ActiveRecord::Rollback)

        expect(node1.save).to be true
        expect(node1.new_record?).to be true
        expect(scenario.root_node_uid).to be_nil
      end
    end
  end

  describe ".process_destroy" do
    # rubocop:disable RSpec/IndexedLet
    let!(:node1) { create(:node, scenario: scenario, root_node: false) }
    let!(:node2) { create(:node, scenario: scenario, root_node: true, next_node_uid: [node1.id, node3.id]) }
    let!(:node3) { create(:node, scenario: scenario, root_node: false) }
    # rubocop:enable RSpec/IndexedLet

    context "when destroy root node" do
      it "success" do
        # before destroy
        expect(scenario.root_node_uid).to eq node2.id

        # destroy
        expect(node2.process_destroy).to be true

        # after destroy
        expect(scenario.root_node_uid).to be_nil
        expect(::Node.count).to eq 2
      end

      it "failed" do
        allow_any_instance_of(::Node).to receive(:destroy!).and_raise(StandardError.new("errors"))

        # before destroy
        expect(scenario.root_node_uid).to eq node2.id

        # destroy
        expect(node2.process_destroy).to be false

        # after destroy
        expect(node2.errors.messages.present?).to be true
        expect(scenario.reload.root_node_uid).to eq node2.id
        expect(::Node.count).to eq 3
      end
    end

    context "when destroy normal node" do
      it "success" do
        # before destroy
        expect(scenario.root_node_uid).to eq node2.id

        # destroy
        expect(node1.process_destroy).to be true

        # after destroy
        expect(scenario.root_node_uid).to eq node2.id
        expect(::Node.count).to eq 2
        expect(node2.reload.next_node_uid).to eq [node3.id]
      end

      it "failed" do
        allow_any_instance_of(::Node).to receive(:destroy!).and_raise(StandardError.new("errors"))

        # before destroy
        expect(scenario.root_node_uid).to eq node2.id

        # destroy
        expect(node1.process_destroy).to be false

        # after destroy
        expect(node1.errors.messages.present?).to be true
        expect(scenario.root_node_uid).to eq node2.id
        expect(::Node.count).to eq 3
        expect(node2.next_node_uid).to eq [node1.id, node3.id]
      end
    end
  end
end
