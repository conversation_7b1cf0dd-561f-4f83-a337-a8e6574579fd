require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Server
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Configuration for the application, engines, and railties goes here.
    config.i18n.default_locale = :ja
    config.time_zone = "Asia/Tokyo"
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    # config.action_cable.mount_path = nil
    config.action_cable.disable_request_forgery_protection = true
    config.after_initialize do
      config.action_cable.url = ActionCable.server.config.url = ENV.fetch("ACTION_CABLE_URL", "ws://localhost:9090/cable") if AnyCable::Rails.enabled?
    end
  end
end
