# This file contains per-environment settings for AnyCable.
#
# Since AnyCable config is based on anyway_config (https://github.com/palkan/anyway_config), all AnyCable settings
# can be set or overridden through the corresponding environment variables.
# E.g., `rpc_host` is overridden by ANYCABLE_RPC_HOST, `debug` by ANYCABLE_DEBUG etc.
#
# Note that AnyCable recognizes REDIS_URL env variable for Redis pub/sub adapter. If you want to
# use another Redis instance for AnyCable, provide ANYCABLE_REDIS_URL variable.
#
# Read more about AnyCable configuration here: https://docs.anycable.io/ruby/configuration
#
default: &default
  # Turn on/off access logs ("Started..." and "Finished...")
  access_logs_disabled: false
  # Whether to enable gRPC level logging or not
  log_grpc: false
  # Use Redis to broadcast messages to AnyCable server
  broadcast_adapter: redis
  # You can use REDIS_URL env var to configure Redis URL.
  # Localhost is used by default.
  # redis_url: "redis://localhost:6379/1"
  # Use the same channel name for WebSocket server, e.g.:
  #   $ anycable-go --redis_channel="__anycable__"
  redis_channel: "__anycable__"
  # Use HTTP RPC mounted at the specified path of your web server
  # Read more about AnyCable RPC:  https://docs.anycable.io/anycable-go/rpc
  # http_rpc_mount_path: "/_anycable"


development:
  <<: *default
  # WebSocket endpoint of your AnyCable server for clients to connect to
  # Make sure you have the `action_cable_meta_tag` in your HTML layout
  # to propogate this value to the client app
  # websocket_url: "ws://localhost:8080/cable"

test:
  <<: *default

production:
  <<: *default
  # websocket_url: ~
