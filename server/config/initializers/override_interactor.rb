module Interactor
  module Organizer
    module InstanceMethods
      def call
        requires_new_transaction = context.requires_new_transaction || false

        ActiveRecord::Base.transaction(requires_new: requires_new_transaction) do
          self.class.organized.each do |interactor|
            if context.required_validation
              interactor.new(context).validate!

              interactor.call!(context) if context.valid
            else
              interactor.call!(context)
            end
          end
        end
      end
    end
  end
end

module Interactor
  class Context < OpenStruct
    def fail(context = {})
      context.each { |key, value| self[key.to_sym] = value }
      @failure = true
    end

    def fail_with_message!(context = {})
      context.each { |key, value| self[key.to_sym] = value }
      @failure = true

      error_messages = message.presence || context
      raise Failure, error_messages.presence || self
    end
  end

  def fail_with_error_messages
    return if context.error_messages.blank?

    errors = context.error_messages.deep_dup

    construct_base_errors(errors)
    context.error_messages[:base_errors].flatten! if context.error_messages[:base_errors].present?

    context.fail!
  end

  def construct_base_errors(errors)
    errors.each do |k, value|
      key = k.to_s.split(".").last.to_sym

      if key == :base_errors
        context.error_messages[:base_errors] ||= []
        context.error_messages[:base_errors] << value

        next
      end

      case value.class.to_s
      when "Array"
        value.flatten.each do |item|
          next unless item.is_a?(Hash)

          construct_base_errors(item)
        end
      when "Hash"
        construct_base_errors(value)
      else
        next
      end
    end
  end
end
