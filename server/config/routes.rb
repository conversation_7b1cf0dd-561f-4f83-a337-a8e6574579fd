Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  # root "articles#index"

  mount GraphiQL::Rails::Engine, at: "/graphiql", graphql_path: "/graphql" if Rails.env.development?

  get "/", to: "home#index"
  get "/healthz", to: "health#index"

  post "/graphql", to: "graphql#execute"

  namespace :api do
    namespace :v1 do
      resources :form_prefills, only: [:show, :destroy]
    end
  end
end
