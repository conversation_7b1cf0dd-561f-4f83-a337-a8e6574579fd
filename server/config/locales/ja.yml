---
ja:
  activerecord:
    errors:
      messages:
        invalid: は不正な値です
        duplicated: は存在しました
        record_invalid: "バリデーションに失敗しました: %{errors}"
        restrict_dependent_destroy:
          has_one: "%{record}が存在しているので削除できません"
          has_many: "%{record}が存在しているので削除できません"
        is_required: "必須項目です。"
        max_string: は%255文字以内で入力してください
  date:
    abbr_day_names:
      - 日
      - 月
      - 火
      - 水
      - 木
      - 金
      - 土
    abbr_month_names:
      -
      - 1月
      - 2月
      - 3月
      - 4月
      - 5月
      - 6月
      - 7月
      - 8月
      - 9月
      - 10月
      - 11月
      - 12月
    day_names:
      - 日曜日
      - 月曜日
      - 火曜日
      - 水曜日
      - 木曜日
      - 金曜日
      - 土曜日
    formats:
      default: "%Y/%m/%d"
      long: "%Y年%m月%d日(%a)"
      short: "%m/%d"
    month_names:
      -
      - 1月
      - 2月
      - 3月
      - 4月
      - 5月
      - 6月
      - 7月
      - 8月
      - 9月
      - 10月
      - 11月
      - 12月
    order:
      - :year
      - :month
      - :day
  datetime:
    distance_in_words:
      about_x_hours:
        one: 約1時間
        other: 約%{count}時間
      about_x_months:
        one: 約1ヶ月
        other: 約%{count}ヶ月
      about_x_years:
        one: 約1年
        other: 約%{count}年
      almost_x_years:
        one: 1年弱
        other: "%{count}年弱"
      half_a_minute: 30秒前後
      less_than_x_seconds:
        one: 1秒以内
        other: "%{count}秒未満"
      less_than_x_minutes:
        one: 1分以内
        other: "%{count}分未満"
      over_x_years:
        one: 1年以上
        other: "%{count}年以上"
      x_seconds:
        one: 1秒
        other: "%{count}秒"
      x_minutes:
        one: 1分
        other: "%{count}分"
      x_days:
        one: 1日
        other: "%{count}日"
      x_months:
        one: 1ヶ月
        other: "%{count}ヶ月"
      x_years:
        one: 1年
        other: "%{count}年"
    prompts:
      second: 秒
      minute: 分
      hour: 時
      day: 日
      month: 月
      year: 年
  errors:
    format: "%{attribute}%{message}"
    messages:
      accepted: を受諾してください
      blank: "必須項目です"
      confirmation: と%{attribute}の入力が一致しません
      empty: を入力してください
      equal_to: は%{count}にしてください
      even: は偶数にしてください
      exclusion: は正しくありません
      greater_than: は%{count}より大きい値にしてください
      greater_than_or_equal_to: "*%{count}以上の数値にしてください"
      greater_than_or_equal_to_specific: "*%{value}以上の数値にしてください"
      inclusion: は一覧にありません
      invalid: は不正な値です
      missing_keys: "次のキーが欠けています: %{keys}"
      invalid_keys: "無効なキーが含まれています: %{keys}"
      invalid_key_pairs: "無効なキーのペアが含まれています: %{keys}"
      less_than: は%{count}より小さい値にしてください
      less_than_or_equal_to: は%{count}以下の値にしてください
      must_less_than: 購入数の上限は下限よりも大きな数字を入力してください。
      model_invalid: "バリデーションに失敗しました: %{errors}"
      not_a_number: "* 整数を半角で入力してください"
      not_an_integer: は整数で入力してください
      odd: は奇数にしてください
      other_than: は%{count}以外の値にしてください
      present: は入力しないでください
      required: を入力してください
      taken: はすでに存在します
      too_long: は%{count}文字以内で入力してください
      too_short: は%{count}文字以上で入力してください
      wrong_length: は%{count}文字で入力してください
      not_found: は見つかりません
      invalid_password: 小文字大文字を含めた8文字以上の英数字で入力してください
      must_be_in_katakana: はカタカナで入力してください
      incorrect: は正しくありません
      invalid_upsell_only: はアップセールできません
      invalid_multiple_purchase: は複数導入できません
      invalid_cv_xsell: はサンクスアップセールできません
      invalid_update: 更新に失敗しました
      invalid_slug: .(ピリオド）; (セミコロン)は使用不可です。
      taken_combination_option_value: 規格のバリエーションが重複しています
      duplicated: は重複しています。
      cant_be_decimal: 整数を半角で入力してください
      invalid_base_url: 使用可能な半角記号は、 - (ハイフン) . (ピリオド) _ (アンダースコア) ~ (チルダ)のみです。
      invalid_ip_address: アドレスが正しくありません
      invalid_url: URLが正しくありません
      is_required: 必須項目です
      in: は%{min}個以上%{max}個以下までしか購入できません。
      default_shipping_fee: 送料テンプレートは必ず1つのデフォルト値を設定してください
      default_payment_fee_template: 手数料テンプレートは必ず1つのデフォルト値を設定してください
      invalid_full_address_name: は255文字以内で入力してください。
      invalid_payment_state: 決済ステータスが無効です
      already_exist: "* 既に使われています"
      invalid_product: LPの表示と異なる商品が選択されています
    template:
      body: 次の項目を確認してください
      header:
        one: "%{model}にエラーが発生しました"
        other: "%{model}に%{count}個のエラーが発生しました"
  helpers:
    select:
      prompt: 選択してください
    submit:
      create: 登録する
      submit: 保存する
      update: 更新する
  number:
    currency:
      format:
        delimiter: ","
        format: "%n%u"
        precision: 0
        separator: "."
        significant: false
        strip_insignificant_zeros: false
        unit: 円
    format:
      delimiter: ","
      precision: 3
      separator: "."
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: "%n %u"
        units:
          billion: 十億
          million: 百万
          quadrillion: 千兆
          thousand: 千
          trillion: 兆
          unit: ""
      format:
        delimiter: ""
        precision: 3
        significant: true
        strip_insignificant_zeros: true
      storage_units:
        format: "%n%u"
        units:
          byte: バイト
          eb: EB
          gb: GB
          kb: KB
          mb: MB
          pb: PB
          tb: TB
    percentage:
      format:
        delimiter: ""
        format: "%n%"
    precision:
      format:
        delimiter: ""
  support:
    array:
      last_word_connector: "、"
      two_words_connector: "、"
      words_connector: "、"
  time:
    am: 午前
    formats:
      default: "%Y年%m月%d日(%a) %H時%M分%S秒 %z"
      long: "%Y/%m/%d %H:%M"
      short: "%m/%d %H:%M"
    pm: 午後
  search_filter:
    with_deleted: 含む
    without_deleted: 含まない
  boolean:
    yes_no:
      true: はい
      false: いいえ
    display:
      true: 表示
      false: 非表示
    actives:
      true: 有効
      false: 無効
  emails:
    customers:
      password_reset_email:
        title: |
          パスワードの再設定について
        body: |
          このメールは、{{ shop_name }}公式ショップより自動的に送信されています。
          このメールに心当たりのない場合やご不明な点がある場合は、
          その旨を当ショップの運営までご連絡ください。
          {{ name }} 様
          （ご登録メールアドレス：{{ email }}）

          いつも{{ shop_name }}公式ショップをご利用いただきありがとうございます。
          お客様のログインIDのパスワード設定・変更URLを発行させていただきました。
          以下の専用URLより、パスワードの設定・変更をお願いいたします。

            {{ url }}
          ※上記のURLは発行されてから24時間有効です。

          今後とも、{{ shop_name }}公式ショップをよろしくお願いいたします。
      create_card_customer:
        title: |
          Create Cards
        body: |
          Card Creation Successfully
      update_card_customer:
        title: |
          Update Cards
        body: |
          Card Update Successfully
      destroy_card_customer:
        title: |
          Destroy Cards
        body: |
          Card Removed Successfully
      register_customer:
        title: |
          Registration Customer
        body: |
          Registration New Customer
      update_customer:
        title: |
          会員情報が更新されました
        body: |
          マイページ上でお客様が会員情報を更新しました。

          顧客番号: {{ number }}
          顧客更新日時: {{ updated_at }}

          URL: {{ url_customer }}

          ※ 変更された項目
          {{ field_updated }}

          {{ time_send_email }}
      field_change:
        sex_id: 性別
        optin: メールマガジン受け取り
        birthday: 生年月日
        name01: 姓
        name02: 名
        kana01: セイ
        kana02: メイ
        zip01: 郵便番号1
        zip02: 郵便番号2
        addr01: 住所1
        addr02: 住所2
        tel01: 電話番号(1カラム目)
        tel02: 電話番号(2カラム目)
        tel03: 電話番号(3カラム目)
        prefecture_id: 都道府県
  services:
    payments:
      cards:
        destroy:
          in_use: 利用中のため削除することができません
          success: 削除されました。
          failed: 削除失敗しました。
        create:
          success: 作成されました。
