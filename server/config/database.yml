# SQLite. Versions 3.8.0 and up are supported.
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem "sqlite3"
#
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 10 } %>
  username: <%= ENV.fetch("DB_USERNAME", "root") %>
  password: <%= ENV.fetch("DB_PASSWORD", "12345678") %>
  host: <%= ENV.fetch("DB_HOST", "localhost") %>
  port: <%= ENV.fetch("DB_PORT", "5432") %>
  schema_search_path: <%= ENV.fetch("DB_SCHEMA_PATH", "public") %>
  reconnect: true

development:
  <<: *default
  database: <%= ENV.fetch("DB_DATABASE", "united_cart_chat_bot") %>

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: united_cart_chat_bot_test

production:
  <<: *default
  database: <%= ENV.fetch("DB_DATABASE", "united_cart_chat_bot_production") %>
