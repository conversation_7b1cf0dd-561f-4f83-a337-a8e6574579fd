# README

### Install anycable-go

```
brew install anycable-go

```

### Bundle install

```
bundle install
```

### Run server

```
bundle exec foreman start
```

### DB

1. Create `.env` file ( copy from `.env.example` )
2. Run migration

```bash
bundle exec rails db:create
bundle exec rails ridge:apply ALLOW_DROP_TABLE=1 ALLOW_REMOVE_COLUMN=1
```

### Important data init

```
bundle exec rails data:init_essentials
```

## Annotation ( For development only )

```bash
bundle exec rake remove_annotation && bundle exec rake annotate_models
```

## DB Seed ( for development only )

```bash
bundle exec rails db:seed
```

## DB Reset ( for development only )

```bash
bundle exec rails db:custom_reset ALLOW_DROP_TABLE=1 ALLOW_REMOVE_COLUMN=1
```

## DB Seed ( for development only )

```bash
bundle exec rails db:seeds SCENARIO_NAME=scenario/hawaiiwater
```
