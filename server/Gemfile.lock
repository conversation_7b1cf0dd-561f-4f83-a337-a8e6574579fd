GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_hash (3.2.1)
      activesupport (>= 5.0.0)
    active_type (2.5.0)
      activerecord (>= 3.2)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (1.5.1)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    anycable (1.5.1)
      anycable-core (= 1.5.1)
      grpc (~> 1.53)
    anycable-core (1.5.1)
      anyway_config (~> 2.2)
      google-protobuf (~> 3.25)
    anycable-rails (1.5.1)
      anycable (~> 1.5.0)
      anycable-rails-core (= 1.5.1)
    anycable-rails-core (1.5.1)
      actioncable (>= 6.0)
      anycable-core (~> 1.5.0)
      globalid
    anycable-rails-jwt (0.2.0)
      anycable-rails-core (~> 1.1)
      jwt (~> 2.2)
    anyway_config (2.6.4)
      ruby-next-core (~> 1.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bigdecimal (3.1.7)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    builder (3.2.4)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    coderay (1.1.3)
    concurrent-ruby (1.2.3)
    connection_pool (2.4.1)
    crass (1.0.6)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    debug (1.9.1)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    diffy (3.4.2)
    docile (1.4.0)
    domain_name (0.6.20240107)
    dotenv (3.1.0)
    dotenv-rails (3.1.0)
      dotenv (= 3.1.0)
      railties (>= 6.1)
    drb (2.2.1)
    enum_help (0.0.19)
      activesupport (>= 3.0.0)
    erubi (1.12.0)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.2.3)
      i18n (>= 1.8.11, < 2)
    faraday (2.9.0)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-net_http (3.1.0)
      net-http
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86_64-darwin)
    foreman (0.88.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-protobuf (3.25.3-arm64-darwin)
    google-protobuf (3.25.3-x86_64-darwin)
    googleapis-common-protos-types (1.15.0)
      google-protobuf (>= 3.18, < 5.a)
    graphiql-rails (1.10.0)
      railties
    graphql (2.2.13)
      base64
    grpc (1.64.0-arm64-darwin)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.64.0-x86_64-darwin)
      google-protobuf (~> 3.25)
      googleapis-common-protos-types (~> 1.0)
    http-accept (1.7.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    i18n (1.14.4)
      concurrent-ruby (~> 1.0)
    interactor (3.1.2)
    io-console (0.7.2)
    irb (1.12.0)
      rdoc
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.7.1)
    jwt (2.8.1)
      base64
    language_server-protocol (3.17.0.3)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0820)
    mini_mime (1.1.5)
    minitest (5.22.3)
    msgpack (1.7.2)
    mutex_m (0.2.0)
    natto (1.2.0)
      ffi (>= 1.9.0)
    net-http (0.4.1)
      uri
    net-imap (0.4.10)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.4.0.1)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.0)
    nokogiri (1.16.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.2-x86_64-darwin)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    parallel (1.24.0)
    parser (3.3.0.5)
      ast (~> 2.4.1)
      racc
    pg (1.5.6)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (5.1.2)
      stringio
    public_suffix (5.0.4)
    puma (6.4.2)
      nio4r (~> 2.0)
    racc (1.7.3)
    rack (3.0.9.1)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.1.0)
    ransack (4.1.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.6.2)
      psych (>= 4.0.0)
    redis (5.1.0)
      redis-client (>= 0.17.0)
    redis-client (0.21.0)
      connection_pool
    regexp_parser (2.9.0)
    reline (0.4.3)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.6)
    ridgepole (2.0.2)
      activerecord (>= 6.1, < 7.2)
      diffy
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.1)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-support (3.13.1)
    rubocop (1.62.1)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.2)
      parser (>= *******)
    rubocop-capybara (2.20.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.25.1)
      rubocop (~> 1.41)
    rubocop-graphql (1.5.0)
      rubocop (>= 0.90, < 2)
    rubocop-rails (2.24.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rspec (2.27.1)
      rubocop (~> 1.40)
      rubocop-capybara (~> 2.17)
      rubocop-factory_bot (~> 2.22)
    ruby-next-core (1.0.3)
    ruby-progressbar (1.13.0)
    rubyzip (2.3.2)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shoulda-matchers (6.1.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stringio (3.1.0)
    thor (1.3.1)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    uri (0.13.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webrick (1.8.1)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.13)

PLATFORMS
  arm64-darwin-21
  arm64-darwin-22
  arm64-darwin-23
  x86_64-darwin-23

DEPENDENCIES
  active_hash (~> 3.2)
  active_type (~> 2.5)
  activerecord-import (~> 1.5)
  annotate (~> 3.2)
  anycable-rails (~> 1.5)
  anycable-rails-jwt
  bootsnap
  capybara (~> 3.40)
  database_cleaner-active_record (~> 2.1)
  debug
  devise (~> 4.9)
  dotenv-rails (~> 3.1)
  enum_help (~> 0.0.19)
  factory_bot_rails (~> 6.4)
  faker (~> 3.2)
  faraday (~> 2.9)
  foreman
  graphiql-rails (~> 1.10)
  graphql (~> 2.2)
  interactor (~> 3.1)
  jbuilder
  jwt (~> 2.8)
  natto
  pg (~> 1.5)
  pry-rails (~> 0.3.9)
  puma (~> 6.4)
  rack-cors (~> 2.0)
  rails (~> 7.1, >= *******)
  ransack (~> 4.1)
  redis (~> 5.1)
  rest-client
  rexml (~> 3.2)
  ridgepole (~> 2.0)
  rspec (~> 3.13)
  rspec-rails (~> 6.1)
  rubocop (~> 1.62)
  rubocop-graphql (~> 1.5)
  rubocop-rails (~> 2.24)
  rubocop-rspec (~> 2.27)
  selenium-webdriver (~> 4.10)
  shoulda-matchers (~> 6.1)
  simplecov (~> 0.22.0)
  sprockets-rails
  tzinfo-data
  webdrivers (~> 5.3)

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.5.6
