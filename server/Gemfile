source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.2.2"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.1", ">= *******"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 6.4"

# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
# gem "importmap-rails"

# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
# gem "turbo-rails"

# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
# gem "stimulus-rails"

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Use Redis adapter to run Action Cable in production
gem "redis", "~> 5.1"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[mingw mswin x64_mingw jruby]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Sass to process CSS
# gem "sassc-rails"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[mri mingw x64_mingw]
  gem "dotenv-rails", "~> 3.1"
  gem "foreman"
  gem "pry-rails", "~> 0.3.9"
  gem "rexml", "~> 3.2"
  gem "rubocop", "~> 1.62"
  gem "rubocop-graphql", "~> 1.5"
  gem "rubocop-rails", "~> 2.24"
  gem "rubocop-rspec", "~> 2.27"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  # gem "annotate"

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "database_cleaner-active_record", "~> 2.1"
  gem "factory_bot_rails", "~> 6.4"
  gem "rspec", "~> 3.13"
  gem "rspec-rails", "~> 6.1"
  gem "shoulda-matchers", "~> 6.1"
  gem "simplecov", "~> 0.22.0", require: false
end

gem "active_hash", "~> 3.2"
gem "activerecord-import", "~> 1.5"
gem "active_type", "~> 2.5"
gem "annotate", "~> 3.2"
gem "anycable-rails", "~> 1.5"
gem "anycable-rails-jwt"
gem "capybara", "~> 3.40"
gem "devise", "~> 4.9"
gem "enum_help", "~> 0.0.19"
gem "faker", "~> 3.2"
gem "faraday", "~> 2.9"
gem "graphiql-rails", "~> 1.10"
gem "graphql", "~> 2.2"
gem "interactor", "~> 3.1"
gem "jwt", "~> 2.8"
gem "natto"
gem "pg", "~> 1.5"
gem "rack-cors", "~> 2.0"
gem "ransack", "~> 4.1"
gem "rest-client"
gem "ridgepole", "~> 2.0"
gem "selenium-webdriver", "~> 4.10"
gem "webdrivers", "~> 5.3"
