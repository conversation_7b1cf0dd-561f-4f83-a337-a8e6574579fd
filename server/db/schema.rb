# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 0) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "uuid-ossp"

  create_table "nodes", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "scenario_id", null: false
    t.integer "node_type", default: 0, null: false
    t.boolean "root_node", default: false
    t.string "label"
    t.string "next_node_uid", array: true
    t.jsonb "body"
    t.jsonb "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version", null: false
    t.index ["scenario_id"], name: "index_nodes_on_scenario_id"
  end

  create_table "scenario_settings", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "description", null: false
    t.boolean "active", default: false
    t.integer "setting_type", default: 1
    t.jsonb "general_settings"
    t.jsonb "theme_customize_settings"
    t.jsonb "css_customize"
    t.jsonb "javascript_customize"
    t.string "scenario_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version", null: false
    t.index ["scenario_id"], name: "index_scenario_settings_on_scenario_id"
  end

  create_table "scenarios", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.string "root_node_uid"
    t.boolean "active", default: false
    t.string "match_value", null: false
    t.integer "match_type", default: 1, null: false
    t.string "user_id", null: false
    t.jsonb "data_json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version", null: false
    t.boolean "support_ui_enable", default: false
    t.boolean "progress_bar_enable", default: false
    t.index ["user_id"], name: "index_scenarios_on_user_id"
  end

  create_table "tasks", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "class_name", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version", null: false
    t.index ["class_name"], name: "index_tasks_on_class_name", unique: true
  end

  create_table "users", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "email", null: false
    t.string "encrypted_password", null: false
    t.integer "user_type", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "lock_version", null: false
    t.string "shop_id"
    t.index ["email"], name: "index_users_on_email", unique: true
  end

end
