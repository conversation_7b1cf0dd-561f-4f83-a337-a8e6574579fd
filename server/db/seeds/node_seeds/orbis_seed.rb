sc = ::Scenario.find_by(match_value: "https://pr.orbis.co.jp/cosmetics/udot/413-1/?test=ug",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

# sc = Scenario.create!(
#   name: "Scenario 1",
#   description: "orbis",
#   active: true,
#   match_value: "https://pr.orbis.co.jp/cosmetics/udot/413-1/?test=ug",
#   match_type: "include",
#   user_id: User.find_by(email: "<EMAIL>").id,
# )
# sc.scenario_settings.create!(
#   description: "This is an example of general setting",
#   name: "Example General Setting",
#   active: true,
#   setting_type: "general",
#   general_settings: {
#     chat_button_title: "ご購入はこちらから！",
#     chat_operator_img_url: "https://storage.googleapis.com/cart-test/uploads/56y8ds7jwopn9mruec6h52uj6c2aom7d.png",
#     chat_operator_name: "",
#     chat_window_position: "bottom_right",
#     mobile_chat_window_position: "bottom_right",
#     chat_window_title: "【公式】ORBIS U dot",
#     chat_window_subtitle: "お1人様1回限りのトライアル！",
#     confirmation_text: "チャットを本当に閉じますか？",
#     pc_custom_chat_window_height: "800",
#     pc_custom_chat_window_width: "450",
#     show_button_close: true,
#     show_chat_start_button: true,
#     start_chatbot_immediately: true,
#     show_confirmation_close_modal: true
#   },
# )

# sc.scenario_settings.create!(
#   description: "This is an example of design setting",
#   name: "Example Design Setting",
#   active: true,
#   setting_type: "design",
#   theme_customize_settings: {
#     chat_design_theme_id: "",
#     chat_window_bg_color: "#f9f7f2",
#     css_customize: ".header-top {\n    color: #ffdf80;\n    font-size: 13px;\n}\n\n.my-4 {\n    margin: 0px!important;\n}\n\n.py-3 {\n    padding-top: 0rem !important;\n    padding-bottom: 0rem!important;\n}\n\n.form-input .input-s{\n    font-size:18px!important;\n}\n\n.bot-typing {\n    font-size: 20px !important;\n}\n\n.bot-message {\n    font-size: 18px!important;\n}\n\n.mt-3 {\n    margin-top: 0.3rem !important;\n}\n\n.mt-3 p {\n    font-size: 18px;\n    margin-bottom: 0px!important;\n}\n\ninput[type=radio][value=\"男性\"]+label {\n    background-image: url(https://cms.ugchatcms.net/files/orbis/images/male.png);\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center;\n    opacity: 0.3;\n    height: 60px;\n}\n\ninput[type=radio][value=\"女性\"]+label {\n    background-image: url(https://cms.ugchatcms.net/files/orbis/images/female.png);\n    background-size: cover;\n    background-repeat: no-repeat;\n    background-position: center;\n    opacity: 0.3;\n    height: 60px;\n}\n\n\ninput[type=radio][value=\"男性\"]:checked+label {\n    opacity:1;\n}\n\ninput[type=radio][value=\"女性\"]:checked+label {\n    opacity:1;\n}\n\n.text-danger {\n    display: none;\n}\n\n.form-input {\n    position: relative;\n    padding: 10px 20px 30px 20px!important;\n}\n\nbutton.name_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\nbutton.sex_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\nbutton.address_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\nbutton.mail_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\nbutton.password_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\n\n.credit_submit_button {\n    position: absolute;\n    width: 90%;\n    background-color: #9e304b;\n    color: #fff;\n    padding: 10px;\n    left: 20px;\n    border: none;\n    border-radius: 60px;\n    font-size: 18px;\n}\n\n.form-input .form-btn-submit {\n    font-size: 18px;\n    padding: 10px;\n}\n\n.textbox {\n    max-height: 100%!important;\n    overflow: hidden!important;\n}\n\n.confirm_p {\n    display: flex;\n    margin: 5px;\n}\n\nspan.confirm_title {\n    width: 30%;\n}\n\nspan.confirm_right {\n    width: 70%;\n    overflow-wrap: break-word !important;\n}\n\n.kiyaku_none {\n    display: none!important;\n}\n\n#kiyaku_box {\n    overflow: auto;\n    border: 1px solid #000;\n    background-color: #fff;\n    padding: 2%;\n    height: 130px;\n}\n\n#law_box {\n    overflow: auto;\n    border: 1px solid #000;\n    background-color: #fff;\n    padding: 2%;\n    height: 130px;\n}\n\n#return_box {\n    overflow: auto;\n    border: 1px solid #000;\n    background-color: #fff;\n    padding: 2%;\n    height: 130px;\n}\n\n/* button#submit:not([disabled]) {\n    animation: poyon 1.2s infinite;\n    background-color: #ff4500 !important;\n    background-image: none !important;\n}\n\n@keyframes poyon {\n    0%   { transform: scale(1.0, 1.0) translate(0%, 0%); }\n    15%  { transform: scale(0.9, 0.9) translate(0%, 5%); }\n    50%  { transform: scale(0.8, 1.3) translate(0%, -10%); }\n    70%  { transform: scale(1.1, 0.9) translate(0%, 5%); }\n    100% { transform: scale(1.0, 1.0) translate(0%, 0%); }\n} */\n\n.convert-btn {\n    padding: 5px 20px;\n    font-size: 16px;\n    background-color:#9e304b!important;\n}\n\n.confirm_bold {\n    font-weight: bold;\n}\n\n.confirm_border {\n    font-weight: bold;\n    font-size: 16px;\n    padding-left: 0px;\n}\n\n.policy {\n    border: none !important;\n}\n\n.address_form {\n    display:none!important;\n}\n\n.address_form_flex {\n    display:flex;\n    width:100%;\n}\n    \n.address_label {\n    width: 50% !important;\n}\n\n.pre_form {\n    width: 100%;\n}\n    \n\n.progress-wrapper {\n    position: relative;\n    width: 200px;\n    height: 200px;\n}\n\n.progress-circle {\n    fill: none;\n    stroke: #dddddd;\n    stroke-width: 20;\n    stroke-linecap: round;\n}\n\n.progress-bar {\n    fill: none;\n    stroke: #59a6f9;\n    stroke-width: 20;\n    stroke-linecap: round;\n    transform: rotate(-90deg);\n    transform-origin: 50% 50%;\n}\n\n.progress-count {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    font-size: 24px;\n    font-weight: bold;\n    color: #333;\n}\n\nbutton.btn.btn-md.btn-secondary.form-btn-submit {\n    background-color: #fff;\n}\n\ndiv#submit_text {\n    color: red;\n    font-weight: bold;\n}\n\n.confirm-btn {\n    background-color:#9e304b!important;\n    background-image:none!important;\n}\n\n.confirm_textbox {\n    border: none !important;\n}\n\n.policy {\n    display: none !important;\n}\n\n.notion {\n    border: 1px solid #0a5dd0;\n    padding: 5px;\n    text-wrap: wrap;\n    overflow-y: scroll;\n    overflow-x: hidden;\n    max-height: 200px;\n}\n\n.checkbox-container {\n    padding-top: 3%;\n}\n\n.app-content {\n    top: 60px!important;\n}\n\n.col-form-label {\n    font-size: 18px!important;\n    font-weight: bold;\n    flex-wrap: nowrap;\n}\n\n.password_label {\n    flex-wrap: wrap!important;\n}\n\n\n@media only screen and (max-device-width: 480px) {\n    .py-3 {\n        margin-bottom: 0%;\n    }\n}",
  
#     customer_msg_body_bg_color: "",
#     customer_msg_body_txt_color: "",
#     date_system_message_txt_color: "",
#     form_bg_color: "#fff",
#     form_border_color: "#fff",
#     form_btn_bg_color: "#9e304b",
#     form_btn_txt_color: "#fff",
#     form_input_border_color: "#d1d5db",
#     header_bg_color: "#9e304b",
#     title_txt_color: "#fff",
#     initiate_btn_bg_color: "#9e304b",
#     initiate_btn_txt_color: "#fff",
#     message_input_color: "",
#     operator_msg_body_bg_color: "#de5c7d",
#     operator_msg_body_txt_color: "#fff",
#     operator_name_color: "",
#     option_active_bg_color: "#f1f5f9",
#     option_active_txt_color: "#d1d5db",
#     option_bg_color: "#fefce8",
#     option_txt_color: "#333333",
#     theme_color: "",
#     progress_bar_bg_color: "",
#     progress_percentage_bg_color: "",
#     progress_percentage_color: "",
#   },
# )

welcome = sc.nodes.create!(
  root_node: true,
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "こちらから簡単60秒でお手続きいただけます。案内に従ってお手続きください。",
      },
    ],
  },
)

name_message = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "checkbox",
    settings: [
      {
        type: "checkbox",
        label: "「\u003ca href=\"https://www.orbis.co.jp/contents/terms/index/\" target=\"blank\"\u003e利用規約\u003c/a\u003e」 「\u003ca href=\"https://www.orbis.co.jp/contents/company/privacy05/\" target=\"blank\"\u003e個人情報取得について\u003c/a\u003e」 「\u003ca href=\"https://www.orbis.co.jp/contents/terms/index/\" target=\"blank\"\u003eORBIS ポイントサービス利用規約\u003c/a\u003e」に同意する",
        required: false,
        variable: "agreement1",
      },
    ],
  },
  label: "Analy_利用規約チェックボックス",
)

welcome.next_node_uid = [name_message.id]
welcome.save!

name_input = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "name",
    settings: [
      {
        label: "お名前",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "姓",
            variable: "sei",
            type: "input",
            required: true,
            placeholder: "山田",
            ratio: 1,
          },
          {
            label: "名",
            variable: "mei",
            type: "input",
            required: true,
            placeholder: "花子",
            ratio: 1,
          },
        ],
      },
      {
        label: "フリガナ",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            type: "input",
            required: true,
            placeholder: "ヤマダ",
            ratio: 1,
          },
          {
            label: "メイ",
            variable: "meifuri",
            type: "input",
            required: true,
            placeholder: "ハナコ",
            ratio: 1,
          },
        ],
      },
    ],
  },
)

name_message.next_node_uid = [name_input.id]
name_message.save!

gender_message = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "次に、お客様情報を入力してください。",
      },
    ],
  },
)

name_input.next_node_uid = [gender_message.id]
name_input.save!

gender_input = sc.nodes.create!(
  node_type: :input,
  label: "性別と生年月日",
  body: {
    type: "sex_and_birthday",
    settings: [
      {
        type: "radio",
        label: "性別",
        options: [
          { text: "女性" },
          { text: "男性" },
        ],
        required: false,
        variable: "sex",
      },
      {
        type: "date",
        label: "生年月日",
        layout: "horizontal",
        ratios: 3,
        settings: [
          {
            type: "select",
            label: "年",
            ratio: 1,
            required: false,
            variable: "year",
          },
          {
            type: "select",
            label: "月",
            ratio: 1,
            required: false,
            variable: "month",
          },
          {
            type: "select",
            label: "日",
            ratio: 1,
            required: false,
            variable: "day",
          },
        ],
      },
    ],
  },
)

gender_message.next_node_uid = [gender_input.id]
gender_message.save!

thank_you_message = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "ご入力ありがとうございます。",
      },
    ],
  },
)

gender_input.next_node_uid = [thank_you_message.id]
gender_input.save!

address_node = sc.nodes.create!(
  node_type: :input,
  label: "住所",
  body: {
    type: "address",
    settings: [
      {
        type: "input",
        label: "郵便番号",
        required: true,
        variable: "zipcode",
        placeholder: "例：1420051",
      },
      {
        type: "select",
        label: "都道府県",
        required: true,
        variable: "prefectures",
      },
      {
        type: "input",
        label: "市区町村",
        required: true,
        variable: "address01",
        placeholder: "例：品川区平塚",
      },
      {
        type: "input",
        label: "丁目-番地-号",
        required: true,
        variable: "address02",
        placeholder: "例：2丁目1-14",
      },
      {
        type: "input",
        label: "建物名・号室 (任意)",
        required: false,
        variable: "address03",
        placeholder: "例：オルビス801号室",
      },
    ],
  },
  next_node_uid: [],
)

thank_you_message.next_node_uid = [address_node.id]
thank_you_message.save!

tel_email_password = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "tel_email_password",
    settings: [
      {
        label: "電話番号",
        type: "input",
        layout: "horizontal",
        ratios: 3,
        settings: [
          {
            variable: "tel1",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "03",
          },
          {
            variable: "tel2",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "0000",
          },
          {
            variable: "tel3",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "0000",
          },
        ],
      },
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
        required: true,
        placeholder: "例）<EMAIL>",
      },

    ],
  },
)

address_node.next_node_uid = [tel_email_password.id]
address_node.save!

password = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "password",
    settings: [
      {
        label: "Password",
        variable: "password",
        type: "input",
        required: true,
      },
    ],
  },
)

tel_email_password.next_node_uid = [password.id]
tel_email_password.save!

# payment_method_node = sc.nodes.create!(
#   node_type: "input",
#   body: {
#     repeatable: true,
#     type: "radio_button_reselectable",
#     settings: [
#       {
#         variable: "payment_method",
#         type: "radio",
#         required: true,
#         options: [
#           {
#             text: "ID決済・スマホ決済",
#           },
#           {
#             text: "クレジットカード（手数料0円）",
#           },
#         ],
#       },
#     ],
#   },
# )

# password.next_node_uid = [payment_method_node.id]
# password.save!


# node_check_payment_method_step1 = sc.nodes.create!(
#   node_type: "condition",
#   body: {
#     type: "condition",
#     settings: [
#       {
#         variable: "payment_method",
#         value: "ID決済・スマホ決済",
#       },
#     ],
#   },
# )

# node_check_payment_method_step2 = sc.nodes.create!(
#   node_type: "condition",
#   body: {
#     type: "condition",
#     settings: [
#       {
#         variable: "payment_method",
#         value: "クレジットカード（手数料0円）",
#       },
#     ],
#   },
# )

# payment_method_node.update!(next_node_uid: [node_check_payment_method_step1.id, node_check_payment_method_step2.id])

# credit_card  = sc.nodes.create!(
#   node_type: :input,
#   label: "お名前",
#   body: {
#     type: "credit_card",
#     repeatable: true,
#     settings: [
#       {
#         variable: "credit_card",
#         cvv: {
#           enabled: false,
#           required: false,
#           label: "セキュリティコード",
#           placeholder: "123",
#         },
#         brand: {
#           label: "カードブランド",
#           enabled: false,
#           required: false,
#           options: [
#             { text: "Visa" },
#             { text: "MasterCard" },
#             { text: "American Express" },
#           ],
#         },
#         name: { enabled: false,
#                 required: false,
#                 label: "カード名義(ローマ字氏名)",
#                 placeholder: "YAMADA HANAKO" },
#         number: {
#           label: "カード番号",
#           placeholder: "4897XXXXXXXXXXXX",
#         },
#         expired: {
#           label: "有効期限",
#           month: { label: "月" },
#           year: { label: "年" },
#         },
#       },
#     ],
#   },
#   next_node_uid: [],
# )

# node_check_payment_method_step2.update!(next_node_uid: [credit_card.id])

button_submit_node = sc.nodes.create!(
  node_type: "button",
  body: {
    repeatable: true,
    settings: [
      {
        content: I18n.t("submit.order_details"),
        type: "template",
      },
      {
        content: I18n.t("submit.policies"),
        type: "policy",
      },
      {
        content: "購入確認画面へ",
        type: "button",
      },
    ],
  },
)

password.update!(next_node_uid: [button_submit_node.id])
# credit_card.update!(next_node_uid: [button_submit_node.id])

# password.next_node_uid = [button_submit_node.id]

# password.save!

processing_node = sc.nodes.create!(
  node_type: "html_tasks",
  body: {
    repeatable: true,
    settings: [
      {
        task: {
          content: File.read("db/seeds/scenario/orbis.js"),
        },
      },
    ],
  },
)

button_submit_node.update!(next_node_uid: [processing_node.id])
