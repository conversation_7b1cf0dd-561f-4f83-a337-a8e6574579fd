sc = ::Scenario.find_by(match_value: "radio_button",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

# sc = Scenario.create!(
#   name: "Scenario 1",
#   description: "Demo",
#   active: true,
#   match_value: "radio_button",
#   match_type: "include",
#   user_id: User.find_by(email: "<EMAIL>").id,
# )
# sc.scenario_settings.create!(
#   description: "This is an example of general setting",
#   name: "Example General Setting",
#   active: true,
#   setting_type: "general",
#   general_settings: {
#     chat_window_position: "bottom_right",
#     mobile_chat_window_position: "bottom_right",
#     chat_button_title: "ご購入はこちらから！",
#     chat_window_title: "マツゲ★DX ウモア 購入ページ",
#     chat_window_subtitle: "ご購入はこちらから！",
#     chat_operator_name: "オペレーター(自動応答)",
#     chat_operator_img_url: "https://storage.googleapis.com/cart-test/uploads/56y8ds7jwopn9mruec6h52uj6c2aom7d.png",
#     pc_custom_chat_window_width: "450",
#     pc_custom_chat_window_height: "800",
#     show_button_close: true,
#     show_confirmation_close_modal: true,
#     confirmation_text: "初回限定チャットを閉じますか？",
#     show_chat_start_button: true,
#     start_chatbot_immediately: false,
#   },
# )

# sc.scenario_settings.create!(
#   description: "This is an example of design setting",
#   name: "Example Design Setting",
#   active: true,
#   setting_type: "design",
#   theme_customize_settings:
# {
#   chat_design_theme_id: 1,
#   theme_color: "green",
#   initiate_btn_bg_color: "#013e6d",
#   initiate_btn_txt_color: "#ffffff",
#   header_bg_color: "#013e6d",
#   title_txt_color: "#ffffff",
#   chat_window_bg_color: "#d3e3ef",
#   date_system_message_txt_color: "#9e9e9e",
#   message_input_color: "#333333",
#   operator_name_color: "#9e9e9e",
#   operator_msg_body_bg_color: "#013e6d",
#   operator_msg_body_txt_color: "#ffffff",
#   customer_msg_body_bg_color: "#ffffff",
#   customer_msg_body_txt_color: "#333333",
#   option_bg_color: "#d3e3ef",
#   option_active_bg_color: "#013e6d",
#   option_txt_color: "#333333",
#   option_active_txt_color: "#ffffff",
#   form_bg_color: "#ffffff",
#   form_border_color: "#ffffff",
#   form_input_border_color: "#013e6d",
#   form_btn_bg_color: "#013e6d",
#   form_btn_txt_color: "#ffffff",
# }
# )
#

welcome = sc.nodes.create!(
  node_type: :message,
  root_node: true,
  body: {
    settings: [
      {
        type: "text",
        content: "投資メソッドに興味を持ってくださりありがとうございます!",
      },
    ],
  },
)

welcome2 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "はじめに、1分程度のカンタンな質問をさせてください。",
      },
    ],
  },
)
welcome.next_node_uid = [welcome2.id]
welcome.save!
welcome3 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "まずは、ご年齢を教えてください。",
      },
    ],
  },
)

welcome2.next_node_uid = [welcome3.id]
welcome2.save!

age_node = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_button_grid",
    settings: [
      {
        variable: "age",
        required: true,
        enable_edit_button: true,
        options: [
          {
            text: "20代",
          },
          {
            text: "30代",
          },
          {
            text: "40代",
          },
          {
            text: "50代",
          },
          {
            text: "60代",
          },
          {
            text: "70代",
          },
        ],
      },
    ],
  },
)

welcome3.next_node_uid = [age_node.id]
welcome3.save!

welcome4 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "ありがとうございます！\n現在投資は行っていますか？",
      },
    ],
  },
)
age_node.next_node_uid = [welcome4.id]
age_node.save!

investment_status = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "investment_status",
        required: true,
        options: [
          {
            text: "まったくやっていない",
          },
          {
            text: "興味はあるがやっていない",
          },
          {
            text: "すでにいくつかやっている",
          },
        ],
      },
    ],
  },
)

welcome4.next_node_uid = [investment_status.id]
welcome4.save!

welcome5 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "興味はあるがやっていない、ですね！現在投資で得たいものは何ですか？（※複数回答可）",
      },
    ],
  },
)

investment_status.next_node_uid = [welcome5.id]
investment_status.save!

investing_node = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_buttons_multi_select",
    settings: [
      {
        variable: "investment_goals",
        required: true,
        options: [
          {
            text: "インカムゲイン",
          },
          {
            text: "キャピタルゲイン",
          },
          {
            text: "インフレ対策",
          },
          {
            text: "ハワイ不動産",
          },
          {
            text: "税対策",
          },
          {
            text: "資産形成",
          },
          {
            text: "資産分散",
          },
          {
            text: "特になし",
          },
        ],
      },
    ],
  },
)

welcome5.next_node_uid = [investing_node.id]
welcome5.save!

investment_success_response_qa = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "投資が成功した場合は何がしたいですか？",
      },
    ],
  },
)

investing_node.next_node_uid = [investment_success_response_qa.id]
investing_node.save!

investment_success_response = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "investment_success_response",
        required: true,
        options: [
          {
            text: "さらに投資して資産を増やしたい",
          },
          {
            text: "家族に資産を残したい",
          },
          {
            text: "もしもの時に備えて確保したい",
          },
          {
            text: "買いたいものがたくさんほしい",
          },
          {
            text: "該当するものがない",
          },
        ],
      },
    ],
  },
)

investment_success_response_qa.next_node_uid = [investment_success_response.id]
investment_success_response_qa.save!

interested_features_qa = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "今が狙い目と言われている、オープンハウスの「米国不動産投資」が注目されています。下記の特徴の中で気になるものは？",
      },
    ],
  },
)

investment_success_response.next_node_uid = [interested_features_qa.id]
investment_success_response.save!

interested_features = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "interested_features",
        required: true,
        options: [
          {
            text: "大手企業だから安心",
          },
          {
            text: "アメリカ不動産投資で実績No.1",
          },
          {
            text: "信頼の充実サポート",
          },
          {
            text: "豊富な情報量",
          },
          {
            text: "該当するものがない",
          },
        ],
      },
    ],
  },
)

interested_features_qa.next_node_uid = [interested_features.id]
interested_features_qa.save!

area_qa = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "ありがとうございます！\n次はご本人様についてです！\nまずはお住まいの地域を教えてください。",
      },
    ],
  },
)

interested_features.next_node_uid = [area_qa.id]
interested_features.save!

# List of regions
region_details = {
  "北海道" => nil, # Hokkaido has no prefectures to display
  "東北" => ["青森県", "岩手県", "秋田県", "宮城県", "山形県", "福島県"],
  "関東甲信越" => ["埼玉県", "千葉県", "東京都", "神奈川県", "茨城県", "栃木県", "群馬県", "山梨県", "長野県", "新潟県"],
  "東海" => ["静岡県", "岐阜県", "愛知県", "三重県"],
  "北陸" => ["富山県", "石川県", "福井県", "新潟県"],
  "近畿" => ["滋賀県", "京都府", "大阪府", "兵庫県", "奈良県", "和歌山県"],
  "中国" => ["鳥取県", "島根県", "岡山県", "広島県", "山口県"],
  "四国" => ["徳島県", "香川県", "愛媛県", "高知県"],
  "九州" => ["福岡県", "佐賀県", "長崎県", "熊本県", "大分県", "宮崎県", "鹿児島県"],
  "沖縄" => nil, # Okinawa has no prefectures to display
  "海外" => nil, # Overseas has no prefectures to display
}

# Create the first node for "area"
area = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_grid",
    settings: [
      {
        variable: "area",
        required: true,
        options: region_details.keys.map { |area_name| { text: area_name } },
      },
    ],
  },
)

area_qa.update!(next_node_uid: [area.id])
# Create "condition" nodes for each region and store them in a Hash
area_conditions = region_details.keys.map do |area_name|
  sc.nodes.create!(
    node_type: "condition",
    body: {
      type: "condition",
      settings: [
        {
          variable: "area",
          value: area_name,
        },
      ],
    },
  )
end

area.update!(next_node_uid: area_conditions.map(&:id))

message_s = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "次に、現在のご年収を教えてください。",
      },
    ],
  },
)

region_details.each do |area_name, prefectures|
  condition_node = area_conditions.detect { |condition| condition.body["settings"].first["value"] == area_name }
  if prefectures
    msg = sc.nodes.create!(
      node_type: :message,
      body: {
        settings: [
          {
            type: "text",
            content: "どちらの都道府県にお住まいですか？",
          },
        ],
      },
    )
    condition_node.update!(next_node_uid: [msg.id])

    live_node = sc.nodes.create!(
      node_type: "input",
      body: {
        type: "radio_button_grid",
        settings: [
          {
            variable: "prefecture",
            required: true,
            options: prefectures.map { |prefecture| { text: prefecture } },
          },
        ],
      },
    )
    msg.update!(next_node_uid: [live_node.id])
    live_node.update!(next_node_uid: [message_s.id])
  else
    condition_node.update!(next_node_uid: [message_s.id])
  end
end

annual_income = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_grid",
    settings: [
      {
        variable: "annual_income",
        required: true,
        options: [
          {
            text: "1000万未満",
          },
          {
            text: "1000万～3000万",
          },
          {
            text: "3000万～5000万",
          },
          {
            text: "5000万～1億",
          },
          {
            text: "1億～3億",
          },
          {
            text: "3億以上",
          },
        ],
      },
    ],
  },
)
message_s.next_node_uid = [annual_income.id]
message_s.save!

message_occupation = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "続いて、ご職業を教えてください。",
      },
    ],
  },
)
annual_income.update!(next_node_uid: [message_occupation.id])

occupation = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_grid",
    settings: [
      {
        variable: "occupation",
        required: true,
        options: [
          {
            text: "会社経営者",
          },
          {
            text: "士業",
          },
          {
            text: "医師",
          },
          {
            text: "会社員(日系上場)",
          },
          {
            text: "会社員(外資上場)",
          },
          {
            text: "会社員(その他)",
          },
          {
            text: "その他",
          },
        ],
      },
    ],
  },
)
message_occupation.update!(next_node_uid: [occupation.id])

message_name = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "ありがとうございます！\n残り3問です！まず、\nお名前を教えてくださ",
      },
    ],
  },
)
occupation.update!(next_node_uid: [message_name.id])

full_name_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "name",
    settings: [
      {
        label: "お名前",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "姓",
            variable: "sei",
            type: "input",
            required: true,
            placeholder: "山田",
            ratio: 1,
          },
          {
            label: "名",
            variable: "mei",
            type: "input",
            required: true,
            placeholder: "太郎",
            ratio: 1,
          },
        ],
      },
      {
        label: "フリガナ",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            type: "input",
            required: true,
            placeholder: "ヤマダ",
            ratio: 1,
          },
          {
            label: "メイ",
            variable: "meifuri",
            type: "input",
            required: true,
            placeholder: "タロウ",
            ratio: 1,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)
message_name.update!(next_node_uid: [full_name_node.id])

message_tel = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "次に電話番号を入力してください。",
      },
    ],
  },
)
full_name_node.update!(next_node_uid: [message_tel.id])

tel_node = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "text",
    settings: [
      {
        label: "電話番号",
        variable: "tel",
        type: "input",
        required: true,
        placeholder: "例）09011112222",
      },
    ],
  },
  next_node_uid: [],
)
message_tel.update!(next_node_uid: [tel_node.id])

message_mail = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "最後に、メールアドレスを入力してください！",
      },
    ],
  },
)
tel_node.update!(next_node_uid: [message_mail.id])

mail_node = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "text",
    settings: [
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
        required: true,
        placeholder: "例）<EMAIL>",
      },
    ],
  },
  next_node_uid: [],
)
message_mail.update!(next_node_uid: [mail_node.id])

message_c1 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "ご回答ありがとうございました！",
      },
    ],
  },
)
mail_node.update!(next_node_uid: [message_c1.id])

message_c2 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "「資料を受け取る」を押して、投資メソッドをお受け取り下さい。",
      },
    ],
  },
)

message_c1.update!(next_node_uid: [message_c2.id])

btv2 = sc.nodes.create!(
  node_type: "button_v2",
  body: {
    settings: [
      {
        variable: "agreement1",
        content: '<a href="">個人情報のお取り扱い</a>個人情報のお取り扱いについて同意します。',
        type: "checkbox",
        required: true,
      },
      {
        content: "資料を受け取る",
        type: "button",
      },
    ],
  },
  next_node_uid: [],
)

message_c2.update!(next_node_uid: [btv2.id])

processing_node = sc.nodes.create!(
  node_type: "html_tasks",
  body: {
    repeatable: true,
    settings: [
      {
        task: {
          content: File.read("db/seeds/script/radio_scenario.js"),
        },
      },
    ],
  },
)
btv2.update!(next_node_uid: [processing_node.id])
