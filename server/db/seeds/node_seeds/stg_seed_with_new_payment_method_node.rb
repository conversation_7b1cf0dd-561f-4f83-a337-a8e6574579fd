sc = ::Scenario.find_by(match_value: "https://stg.unicorncart.jp/lp?u=thasi-test",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

welcome = sc.nodes.create!(
  node_type: :message,
  root_node: true,
  body: {
    settings: [
      {
        type: "text",
        content: "Welcome",
      },
    ],
  },
)

label_name = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Name",
      },
    ],
  },
)

welcome.next_node_uid = [label_name.id]
welcome.save!

sex_and_birthday_node = sc.nodes.create!(
  node_type: :input,
  label: "",
  body: {
    type: "name_birthday_tel_email",
    settings: [
      {
        label: "お名前",
        settings: [
          {
            label: "姓",
            variable: "sei",
            required: true,
            placeholder: "山田",
          },
          {
            label: "名",
            variable: "mei",
            required: true,
            placeholder: "太郎",
          },
        ],
      },
      {
        label: "フリガナ",
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            required: true,
            placeholder: "ヤマダ",
          },
          {
            label: "メイ",
            variable: "meifuri",
            required: true,
            placeholder: "タロウ",
          },
        ],
      },
      {
        label: "生年月日",
        settings: [
          { label: "日", variable: "day", required: true },
          {
            label: "月",
            variable: "month",
            required: true,
          },
          {
            label: "年",
            variable: "year",
            required: true,
          },
        ],
      },

      {
        label: "メールアドレス",
        variable: "mail",
        required: true,
        placeholder: "例）<EMAIL>",
      },
      {
        label: "電話番号",
        variable: "tel",
        required: true,
        placeholder: "09012345678",
      },
    ],
  },
  next_node_uid: [],
)

label_name.next_node_uid = [sex_and_birthday_node.id]
label_name.save!

label_address = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Address",
      },
    ],
  },
)
sex_and_birthday_node.next_node_uid = [label_address.id]
sex_and_birthday_node.save!

address_node = sc.nodes.create!(
  node_type: :input,
  label: "住所",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
        required: true,
        placeholder: "1300000",
      },
      {
        label: "都道府県名",
        variable: "prefectures",
        required: true,
        type: "select",
      },
      {
        label: "市区町村名",
        variable: "address01",
        type: "input",
        required: true,
        placeholder: "市区町村名 (千代田区神田神保町)",
      },
      {
        label: "丁目-番地-号",
        variable: "address02",
        type: "input",
        required: true,
        placeholder: "例：２０−１２３−３",
      },
      {
        label: "建物名・号室 (任意)",
        variable: "address03",
        type: "input",
        required: true,
        placeholder: "例：中野坂上サンブライトツインビル１４階",
      },
    ],
  },
  next_node_uid: [],
)

label_address.next_node_uid = [address_node.id]
label_address.save!

label_tes = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter password",
      },
    ],
  },
)
address_node.next_node_uid = [label_tes.id]
address_node.save!

tel_email_password = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "tel_email_password",
    settings: [
      {
        label: "パスワード",
        variable: "password",
        type: "input",
        required: true,
        placeholder: "パスワード",
      },
    ],
  },
)

label_tes.next_node_uid = [tel_email_password.id]
label_tes.save!

payment_method_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "payment_method",
    settings: [
      {
        payment_method: {
          label: "支払方法",
          options: [
            {
              text: "代金引換",
            },
            {
              text: "SBPS クレジットカード一括",
            },
          ],
        },
        credit_card_form: {
          show_on: ["SBPS クレジットカード一括"],
          cvv: {
            enabled: true,
            required: true,
            label: "セキュリティコード",
            placeholder: "123",
          },
          brand: {
            label: "カードブランド",
            options: [
              {
                text: "Visa",
              },
              {
                text: "MasterCard",
              },
              {
                text: "American Express",
              },
            ],
          },
          name: {
            label: "カード名義(ローマ字氏名)",
            placeholder: "YA1",
            enabled: true,
            required: true,
          },
          number: { label: "カード番号", placeholder: "22222" },
          expired: {
            label: "有効期限",
            month: { label: "月" },
            year: { label: "年" },
          },
        },
      },
    ],
  },
  next_node_uid: [],
)

tel_email_password.next_node_uid = [payment_method_node.id]
tel_email_password.save!

button_submit_node = sc.nodes.create!(
  node_type: "button",
  body: {
    repeatable: true,
    settings: [
      {
        content: I18n.t("submit.order_details"),
        type: "template",
      },
      {
        content: I18n.t("submit.policies"),
        type: "policy",
      },
      {
        content: "購入確認画面へ",
        type: "button",
      },
    ],
  },
)

payment_method_node.next_node_uid = [button_submit_node.id]
payment_method_node.save!

processing_node = sc.nodes.create!(
  node_type: "html_tasks",
  body: {
    repeatable: true,
    settings: [
      {
        task: {
          content: File.read("db/seeds/scenario/stg_submit.js"),
        },
      },
    ],
  },
)

button_submit_node.next_node_uid = [processing_node.id]
button_submit_node.save!

wait_message_node = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "注文を処理しています。",
        type: "text",
      },
    ],
  },
)

processing_node.next_node_uid = [wait_message_node.id]
processing_node.save!

wait_message_node1 = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "少々お待ちください。",
        type: "text",
      },
    ],
  },
)

wait_message_node.next_node_uid = [wait_message_node1.id]
wait_message_node.save!
message_result = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        type: "text",
        variable: "result_msg",
        content: "",
      },
    ],
  },
)
wait_message_node1.next_node_uid = [message_result.id]
wait_message_node1.save!

check_submit_success = sc.nodes.create!(
  node_type: "condition",
  label: "Check if submit success",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "error",
        value: false,
      },
    ],
  },
)

check_submit_fail = sc.nodes.create!(
  node_type: "condition",
  label: "Check if submit success",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "error",
        value: true,
      },
    ],
  },
)

message_result.next_node_uid = [check_submit_success.id, check_submit_fail.id]
message_result.save!

result_card_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if card error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "カード登録エラー",
      },
    ],
  },
)

result_np_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if NP error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "後払い",
      },
    ],
  },
)

result_payment_error_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if Payment error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "エラー",
      },
    ],
  },
)

check_submit_fail.next_node_uid = [result_card_condition.id, result_np_condition.id, result_payment_error_condition.id]
check_submit_fail.save!

result_card_condition.next_node_uid = [payment_method_node.id]
result_card_condition.save!

set_value_payment_method = sc.nodes.create!(
  node_type: "set_value",
  body: {
    repeatable: true,
    settings: [
      {
        variable: "payment_method",
        value: "Paygentクレジットカード一括",
      },
    ],
  },
)

result_np_condition.next_node_uid = [set_value_payment_method.id]
result_np_condition.save!

result_payment_error_condition.next_node_uid = [set_value_payment_method.id]
result_payment_error_condition.save!

set_value_payment_method.next_node_uid = [payment_method_node.id]
set_value_payment_method.save!
