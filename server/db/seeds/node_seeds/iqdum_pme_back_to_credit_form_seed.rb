# rubocop:disable Naming/VariableNumber
sc = ::Scenario.find_by(match_value: "https://iqdum.jp/",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

ads_step = sc.nodes.create!(
  root_node: true,
  node_type: :message,
  body: {
    settings: [
      {
        type: "image",
        content: "https://iqdum.jp/upload/lp/coupon_1/img/kv_00_saeko.png",
      },
    ],
  },
)

ads_step1 = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "image",
        content: "https://iqdum.jp/upload/lp/main_1/img/bnr_oos_last.png",
      },
    ],
  },
)

ads_step.next_node_uid = [ads_step1.id]
ads_step.save!

first_step = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "それでは受付を開始しますね。\nまずはお客様の電話番号とメールアドレスを入力してください。",
      },
    ],
  },
)
ads_step1.next_node_uid = [first_step.id]
ads_step1.save!

product_step = sc.nodes.create!(
  node_type: :input,
  label: "商品",
  body: {
    type: "select",
    settings: [
      {
        label: "商品",
        variable: "product",
        type: "select",
        required: true,
        options: [
          {
            text: "初回300円OFF_IQDUM定期コース｜[初回1本]イクダムハンドクリーム2本", # id: 9
          },
          {
            text: "初回300円OFF_【おまとめ配送】IQDUM定期コース｜イクダムハンドクリーム3本", # id: 10
          },
        ],
      },
    ],
  },
)

first_step.next_node_uid = [product_step.id]
first_step.save!

name_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "name",
    settings: [
      {
        label: "お名前",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "姓",
            variable: "sei",
            type: "input",
            required: true,
            placeholder: "山田",
            ratio: 1,
          },
          {
            label: "名",
            variable: "mei",
            type: "input",
            required: true,
            placeholder: "太郎",
            ratio: 1,
          },
        ],
      },
      {
        label: "フリガナ",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            type: "input",
            required: true,
            placeholder: "ヤマダ",
            ratio: 1,
          },
          {
            label: "メイ",
            variable: "meifuri",
            type: "input",
            required: true,
            placeholder: "タロウ",
            ratio: 1,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

product_step.update!(next_node_uid: [name_node.id])

tel_mail_node = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "text",
    settings: [
      {
        label: "電話番号",
        variable: "tel",
        type: "input",
        required: true,
        placeholder: "例）09011112222",
      },
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
        required: true,
        placeholder: "例）<EMAIL>",
      },
    ],
  },
  next_node_uid: [],
)
name_node.update!(next_node_uid: [tel_mail_node.id])

# password_node = sc.nodes.create!(
#   node_type: :input,
#   body: {
#     type: "password",
#     settings: [
#       {
#         label: "パスワード",
#         variable: "password",
#         type: "input",
#         required: true,
#         placeholder: "パスワード",
#       },
#     ],
#   },
#   next_node_uid: [],
# )

# tel_node.update!(next_node_uid: [password_node.id])
address_node = sc.nodes.create!(
  node_type: :input,
  label: "住所",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
        required: true,
        placeholder: "1300000",
      },
      {
        label: "都道府県名",
        variable: "prefectures",
        required: true,
        type: "select",
      },
      {
        label: "市区町村名",
        variable: "address01",
        type: "input",
        required: true,
        placeholder: "市区町村名 (千代田区神田神保町)",
      },
      {
        label: "番地・マンション名",
        variable: "address02",
        type: "input",
        required: true,
        placeholder: "番地・マンション名 (1-3-5 ○○マンション 201号室)",
      },
      {
        label: "建物名・号室 (任意)",
        variable: "address03",
        type: "input",
        required: true,
        placeholder: "例：中野坂上サンブライトツインビル１４階",
      },
    ],
  },
  next_node_uid: [],
)

tel_mail_node.update!(next_node_uid: [address_node.id])

sex_and_birthday_node = sc.nodes.create!(
  node_type: :input,
  label: "",
  body: {
    type: "sex_and_birthday",
    settings: [
      {
        label: "性別",
        variable: "sex",
        type: "radio",
        required: false,
        options: [
          {
            text: "男",
          },
          {
            text: "女",
          },
        ],
      },
      {
        label: "生年月日",
        # variable: "birthday", #TODO USE  birthday variable
        type: "date",
        layout: "horizontal",
        ratios: 3,
        settings: [
          {
            label: "日",
            variable: "day",
            type: "select",
            ratio: 1,
            required: false,
          },
          {
            label: "月",
            variable: "month",
            type: "select",
            ratio: 1,
            required: false,
          },
          {
            label: "年",
            variable: "year",
            type: "select",
            ratio: 1,
            required: false,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)
address_node.update!(next_node_uid: [sex_and_birthday_node.id])

# OK
coupon_node = sc.nodes.create!(
  node_type: :input,
  label: "クーポン",
  body: {
    type: "text",
    settings: [
      {
        label: "クーポンコード",
        variable: "coupon",
        type: "input",
        placeholder: "クーポンを入力",
        required: false,
      },
    ],
  },
  next_node_uid: [],
)

sex_and_birthday_node.update!(next_node_uid: [coupon_node.id])

payment_method_node = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "payment_method",
        type: "radio",
        required: true,
        options: [
          {
            text: "NP後払いwizRT(手数料:250円)",
          },
          {
            text: "クレジット決済(手数料:無料)",
          },
        ],
      },
    ],
  },
)

coupon_node.update!(next_node_uid: [payment_method_node.id])

node_check_payment_method_step1 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "NP後払いwizRT(手数料:250円)",
      },
    ],
  },
)

node_check_payment_method_step2 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "クレジット決済(手数料:無料)",
      },
    ],
  },
)

payment_method_node.next_node_uid = [node_check_payment_method_step1.id, node_check_payment_method_step2.id]
payment_method_node.save!

credit_card = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "credit_card",
    repeatable: true,
    settings: [
      {
        label: "カード番号",
        variable: "card_number",
        required: true,
        type: "input",
        placeholder: "4897XXXXXXXXXXXX",
      },
      {
        label: "有効期限",
        variable: "card_expired",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "月",
            variable: "card_expired_month",
            type: "select",
            required: true,
            ratio: 1,
          },
          {
            label: "年",
            variable: "card_expired_year",
            type: "select",
            required: true,
            ratio: 1,
          },
        ],
      },
      {
        label: "カード名義(ローマ字氏名)",
        variable: "card_name",
        type: "input",
        required: true,
        placeholder: "YAMADA HANAKO",
      },
    ],
  },
  next_node_uid: [],
)

node_check_payment_method_step2.next_node_uid = [credit_card.id]
node_check_payment_method_step2.save!
ms = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "注文を処理しています。",
        type: "text",
      },
    ],
  },
)
credit_card.next_node_uid = [ms.id]
credit_card.save!
# credit card
ms2 = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "注文を処理しています。",
        type: "text",
      },
    ],
  },
)
ms.next_node_uid = [ms2.id]
ms.save!

button_submit_node = sc.nodes.create!(
  node_type: "button",
  body: {
    repeatable: true,
    settings: [
      {
        content: I18n.t("submit.order_details"),
        type: "template",
      },
      {
        content: I18n.t("submit.policies"),
        type: "policy",
      },
      {
        content: "購入確認画面へ",
        type: "button",
      },
    ],
  },
)

sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "Test Text",
        type: "text",
      },
    ],
  },
)

ms2.next_node_uid = [button_submit_node.id]
ms2.save!

# np
node_check_payment_method_step1.next_node_uid = [button_submit_node.id]
node_check_payment_method_step1.save!

processing_node = sc.nodes.create!(
  node_type: "html_tasks",
  body: {
    repeatable: true,
    settings: [
      {
        task: {
          content: File.read("db/seeds/script/iqdum_submit.js"),
        },
      },
    ],
  },
)

button_submit_node.next_node_uid = [processing_node.id]
button_submit_node.save!

wait_message_node = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "注文を処理しています。",
        type: "text",
      },
    ],
  },
)

processing_node.next_node_uid = [wait_message_node.id]
processing_node.save!

wait_message_node1 = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "少々お待ちください。",
        type: "text",
      },
    ],
  },
)

wait_message_node.next_node_uid = [wait_message_node1.id]
wait_message_node.save!

message_result = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        type: "text",
        variable: "result_msg",
        content: "",
      },
    ],
  },
)

wait_message_node1.next_node_uid = [message_result.id]
wait_message_node1.save!

check_submit_success = sc.nodes.create!(
  node_type: "condition",
  label: "Check if submit success",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "error",
        value: false,
      },
    ],
  },
)

check_submit_fail = sc.nodes.create!(
  node_type: "condition",
  label: "Check if submit success",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "error",
        value: true,
      },
    ],
  },
)

message_result.next_node_uid = [check_submit_success.id, check_submit_fail.id]
message_result.save!

result_card_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if card error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "カード登録エラー",
      },
    ],
  },
)

result_np_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if NP error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "後払い",
      },
    ],
  },
)

result_payment_error_condition = sc.nodes.create!(
  node_type: "condition",
  label: "Check if Payment error",
  body: {
    repeatable: true,
    type: "condition",
    settings: [
      {
        variable: "result_msg",
        value: "エラー",
      },
    ],
  },
)

check_submit_fail.next_node_uid = [result_card_condition.id, result_np_condition.id, result_payment_error_condition.id]
check_submit_fail.save!

result_card_condition.next_node_uid = [credit_card.id]
result_card_condition.save!

set_value_payment_method = sc.nodes.create!(
  node_type: "set_value",
  body: {
    repeatable: true,
    settings: [
      {
        variable: "payment_method",
        value: "クレジット決済(手数料:無料)",
      },
    ],
  },
)

result_np_condition.next_node_uid = [set_value_payment_method.id]
result_np_condition.save!

result_payment_error_condition.next_node_uid = [set_value_payment_method.id]
result_payment_error_condition.save!

set_value_payment_method.next_node_uid = [credit_card.id]
set_value_payment_method.save!

# Thank Offer
node_check_product_1 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Product ID 9",
  body: {
    type: "condition",
    settings: [
      {
        variable: "product",
        value: "初回300円OFF_IQDUM定期コース｜[初回1本]イクダムハンドクリーム2本",
      },
    ],
  },
)

node_check_product_2 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Product ID 10",
  body: {
    type: "condition",
    settings: [
      {
        variable: "product",
        value: "初回300円OFF_【おまとめ配送】IQDUM定期コース｜イクダムハンドクリーム3本",
      },
    ],
  },
)

check_submit_success.next_node_uid = [node_check_product_1.id, node_check_product_2.id]
check_submit_success.save!

## Flow Product ID 9
thank_offer_flow_12 = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_button",
    settings: [
      {
        variable: "cv_upsell_1",
        type: "radio",
        required: true,
        options: [
          {
            text: "1本無料＆プレゼントをお得にゲットする！",
          },
          {
            text: "申し込まない",
          },
        ],
      },
    ],
  },
)

node_check_product_1.next_node_uid = [thank_offer_flow_12.id]
node_check_product_1.save!

node_check_flow_1 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Flow Part 1",
  body: {
    type: "condition",
    settings: [
      {
        variable: "cv_upsell_1",
        value: "1本無料＆プレゼントをお得にゲットする！",
      },
    ],
  },
)

node_check_flow_2 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Flow Part 2",
  body: {
    type: "condition",
    settings: [
      {
        variable: "cv_upsell_1",
        value: "申し込まない",
      },
    ],
  },
)

thank_offer_flow_12.next_node_uid = [node_check_flow_1.id, node_check_flow_2.id]
thank_offer_flow_12.save!

### Flow 1
node_flow_1_finish = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_button",
    settings: [
      {
        variable: "cv_upsell_2",
        type: "radio",
        required: true,
        options: [
          {
            text: "2本無料プレゼントに申し込む",
          },
          {
            text: "申し込まない",
          },
        ],
      },
    ],
  },
)

node_check_flow_1.next_node_uid = [node_flow_1_finish.id]
node_check_flow_1.save!

### Flow 2
node_flow_2_finish = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_button",
    settings: [
      {
        variable: "cv_upsell_2",
        type: "radio",
        required: true,
        options: [
          {
            text: "初回１本プレゼントを受け取る",
          },
          {
            text: "申し込まない",
          },
        ],
      },
    ],
  },
)

node_check_flow_2.next_node_uid = [node_flow_2_finish.id]
node_check_flow_2.save!

## Flow Product ID 10
### Flow 3
thank_offer_flow_3 = sc.nodes.create!(
  node_type: "input",
  body: {
    type: "radio_button",
    settings: [
      {
        variable: "cv_upsell_1",
        type: "radio",
        required: true,
        options: [
          {
            text: "2本無料プレゼントに申し込む",
          },
          {
            text: "申し込まない",
          },
        ],
      },
    ],
  },
)

node_check_product_2.next_node_uid = [thank_offer_flow_3.id]
node_check_product_2.save!

## Submit Thank Offer
node_submit_thank_offer = sc.nodes.create!(
  node_type: "html_tasks",
  body: {
    settings: [
      {
        task: {
          content: File.read("db/seeds/script/iqdum_thank_offer_submit.js"),
        },
      },
    ],
  },
)

node_flow_1_finish.update!(next_node_uid: [node_submit_thank_offer.id])
node_flow_2_finish.update!(next_node_uid: [node_submit_thank_offer.id])
thank_offer_flow_3.update!(next_node_uid: [node_submit_thank_offer.id])

node_result_message_thank_offer = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        variable: "result_msg_thank_offer",
        content: "",
      },
    ],
  },
)

node_submit_thank_offer.update!(next_node_uid: [node_result_message_thank_offer.id])
# rubocop:enable Naming/VariableNumber
