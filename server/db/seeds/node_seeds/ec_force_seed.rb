sc = ::Scenario.find_by(match_value: "https://laclulu.xyz/lp?u=upsell_chattest")

return if sc.blank?

sc.nodes.destroy_all

first_step = sc.nodes.create!(
  root_node: true,
  node_type: :message,
  label: "Hello",
  body: {
    settings: [
      {
        label: "https://st3.depositphotos.com/8950810/17657/v/450/depositphotos_176577870-stock-illustration-cute-smiling-funny-robot-chat.jpg",
        type: "img",
      },
      {
        label: "Hello",
        type: "text",
      },
    ],
  },
)

product_step = sc.nodes.create!(
  node_type: :input,
  label: "Product",
  body: {
    type: "product",
    settings: [
      {
        label: "product",
        variable: "product",
        type: "select",
        options: [
          {
            text: "テスト_アップセル後",
          },
          {
            text: "テスト_アップセル前",
          },
        ],
      },
    ],
  },
)

first_step.next_node_uid = [product_step.id]
first_step.save!

node_check_product_step1 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Product",
  body: {
    type: "condition",
    settings: [
      {
        condition_type: "product",
        condition_data: "テスト_アップセル後",
      },
    ],
  },
)

node_check_product_step2 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Product",
  body: {
    type: "condition",
    settings: [
      {
        condition_type: "product",
        condition_data: "テスト_アップセル前",
      },
    ],
  },
)

product_step.next_node_uid = [node_check_product_step1.id, node_check_product_step2.id]
product_step.save!

qty1 = sc.nodes.create!(
  node_type: "input",
  label: "お支払い方法",
  body: {
    type: "select",
    settings: [
      {
        label: "quantity",
        variable: "scheduled_date",
        type: "select",
        options: [],
        crawler_data: [
          {
            runWhen: "submit",
            task: {
              class_name: "HawaiiWaterDesiredInstallationDate",
            },
            inputs: {
              url: {
                value: "https://www.hawaiiwater.co.jp/regist/index",
                type: "string",
              },
              zipcode: {
                value: "",
                type: "string",
              },
              prefectures: {
                value: "",
                type: "string",
              },
            },
            outputs: {
              data: {
                message: "",
                error: "",
                optionValues: [],
              },
            },
          },
        ],
      },
    ],

  },
)

node_check_product_step1.next_node_uid = [qty1.id]
node_check_product_step1.save!

qty2 = sc.nodes.create!(
  node_type: "input",
  label: "Quantity",
  body: {
    type: "quantity",
    settings: [
      {
        label: "quantity",
        variable: "quantity",
        type: "select",
        options: [
          {
            text: "2 個",
          },
          {
            text: "30 個",
          },
        ],
        crawler_data: [
          {
            runWhen: "submit",
            task: {
              className: "CrawlerCheckProductQuantityService",
            },
            inputs: {
              url: {
                value: "",
                type: "string",
              },
              product: {
                value: "",
                type: "string",
              },
              quantity: {
                value: "",
                type: "string",
              },
            },
            outputs: ["status", "result"],
          },
        ],
      },
    ],
  },
)
node_check_product_step2.next_node_uid = [qty2.id]
node_check_product_step2.save!

variant = sc.nodes.create!(
  node_type: "input",
  label: "Variant",
  body: {
    type: "variant",
    settings: [{
      label: "variant",
      variable: "variant",
      type: "select",
      crawler_data: [
        {
          runWhen: "start",
          task: {
            className: "CrawlerProductVariantService",
          },
          inputs: {
            url: {
              value: "",
              type: "string",
            },
            product: {
              value: "",
              type: "string",
            },
          },
          outputs: ["status", "data"],
        },
        {
          runWhen: "submit",
          task: {
            className: "CrawlerCheckProductVariantService",
          },
          inputs: {
            url: {
              value: "",
              type: "string",
            },
            product: {
              value: "",
              type: "string",
            },
            variant: {
              value: [],
              type: "array",
            },
          },
          outputs: ["status", "data"],
        },
      ],
    }],
  },
)

qty2.next_node_uid = [variant.id]
qty1.next_node_uid = [variant.id]
qty2.save!
qty1.save!

name_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: :name,
    settings: [
      {
        label: "姓",
        variable: "sei",
        type: "input",
      },
      {
        label: "姓",
        variable: "mei",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

variant.update(next_node_uid: [name_node.id])
variant.save!

name_furi_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前（フリガナ）",
  body: {
    type: "namefuri",
    settings: [
      {
        label: "セイ",
        variable: "seifuri",
        type: "input",
      },
      {
        label: "セイ",
        variable: "meifuri",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

name_node.update(next_node_uid: [name_furi_node.id])
name_node.save!

email_node = sc.nodes.create!(
  node_type: :input,
  label: "メールアドレス",
  body: {
    type: "email_and_password",
    settings: [
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

name_furi_node.update(next_node_uid: [email_node.id])
name_furi_node.save!

tel_node = sc.nodes.create!(
  node_type: :input,
  label: "電話番号",
  body: {
    type: "tel",
    settings: [
      {
        label: "電話番号",
        variable: "tel",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

email_node.update(next_node_uid: [tel_node.id])
email_node.save!

address_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
      },
      {
        label: "都道府県",
        variable: "prefectures",
        type: "select",
      },
      {
        label: "住所1（市郡区／町・村）",
        variable: "address01",
        type: "input",
      },
      {
        label: "住所2（丁目・番地・マンション名・号室）",
        variable: "address02",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

tel_node.update(next_node_uid: [address_node.id])
tel_node.save!

sex_node = sc.nodes.create!(
  node_type: :input,
  label: "性別",
  body: {
    type: "sex",
    settings: [
      {
        label: "性別",
        variable: "sex",
        type: "select",
        options: [
          {
            text: "男性",
          },
          {
            text: "女性",
          },
          {
            text: "Man",
          },
          {
            text: "Woman",
          },
          {
            text: "ThichDuThu",
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

address_node.update(next_node_uid: [sex_node.id])
address_node.save!

job_node = sc.nodes.create!(
  node_type: :input,
  label: "職業",
  body: {
    type: "job",
    settings: [
      {
        label: "職業",
        variable: "job",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

sex_node.update(next_node_uid: [job_node.id])
sex_node.save!

shipping_address_node = sc.nodes.create!(
  node_type: :input,
  label: "お届け先情報",
  body: {
    type: "shipping_address",
    settings: [
      {
        label: "お届け先住所",
        variable: "shipping_address",
        type: "select",
        options: [
          {
            text: "上記住所と同じ",
          },
          {
            text: "お名前 お名前 | 〒1500031 東京都 住所1(市郡区/町・村) 住所2(丁目・番地・マンション名・号室) ",
          },
          {
            text: "新しく入力する",
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

job_node.update(next_node_uid: [shipping_address_node.id])
job_node.save!

node_check_shipping_address_step1 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Shipping Address",
  body: {
    type: "condition",
    settings: [
      {
        condition_type: "shipping_address",
        condition_data: "上記住所と同じ",
      },
    ],
  },
)

node_check_shipping_address_step2 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Shipping Address",
  body: {
    type: "condition",
    settings: [
      {
        condition_type: "shipping_address",
        condition_data: "お名前 お名前 | 〒1500031 東京都 住所1(市郡区/町・村) 住所2(丁目・番地・マンション名・号室) ",
      },
    ],
  },
)

node_check_shipping_address_step3 = sc.nodes.create!(
  node_type: "condition",
  label: "Check Shipping Address",
  body: {
    type: "condition",
    settings: [
      {
        condition_type: "shipping_address",
        condition_data: "新しく入力する",
      },
    ],
  },
)

shipping_address_node.update(next_node_uid: [node_check_shipping_address_step1.id, node_check_shipping_address_step2.id, node_check_shipping_address_step3.id])
shipping_address_node.save!

new_shipping_address_node = sc.nodes.create!(
  node_type: :input,
  label: "お届け先情報",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
      },
      {
        label: "都道府県",
        variable: "prefectures",
        type: "select",
      },
      {
        label: "住所1（市郡区／町・村）",
        variable: "address01",
        type: "input",
      },
      {
        label: "住所2（丁目・番地・マンション名・号室）",
        variable: "address02",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

node_check_shipping_address_step3.update(next_node_uid: [new_shipping_address_node.id])
node_check_shipping_address_step3.save!

coupon_node = sc.nodes.create!(
  node_type: :input,
  label: "クーポンコード",
  body: {
    type: "coupon",
    settings: [
      {
        label: "クーポンコード",
        variable: "coupon",
        type: "input",
      },
    ],
  },
  next_node_uid: [],
)

node_check_shipping_address_step1.update(next_node_uid: [coupon_node.id])
node_check_shipping_address_step1.save!

node_check_shipping_address_step2.update(next_node_uid: [coupon_node.id])
node_check_shipping_address_step2.save!

payment_method_node = sc.nodes.create!(
  node_type: :input,
  label: "お支払い方法",
  body: {
    type: "payment_method",
    settings: [
      {
        label: "お支払い方法",
        variable: "payment_method",
        type: "select",
        options: [
          {
            text: "代金引換",
          },
          {
            text: "GMO 後払い",
          },
          {
            text: "クレジットカードリボ",
          },
          {
            text: "LINE Pay",
          },
          {
            text: "atone",
          },
          {
            text: "GMO 後払い同梱",
          },
          {
            text: "クレジットカード一括",
          },
          {
            text: "クレジットカード分割",
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

coupon_node.update(next_node_uid: [payment_method_node.id])
coupon_node.save!

subs_order_node = sc.nodes.create!(
  node_type: :input,
  label: "お届け日の指定",
  body: {
    type: "subsorder",
    settings: [
      {
        label: "お届け日の指定",
        variable: "schedule_to_be_delivered_at",
        type: "select",
        options: [
          {
            text: "2024-2-25",
          },
          {
            text: "2024-2-26",
          },
        ],
      },
      {
        label: "お届け時間の指定",
        variable: "schedule_to_be_delivered_at",
        type: "select",
        options: [
          {
            text: "指定なし",
          },
          {
            text: "1212 qsa d12e13sa ",
          },
        ],
      },
      {
        label: "お届け予定日",
        variable: "scheduled_1st_delivery_date",
        type: "raw",
      },
    ],
  },
  next_node_uid: [],
)

payment_method_node.update(next_node_uid: [subs_order_node.id])
payment_method_node.save!
