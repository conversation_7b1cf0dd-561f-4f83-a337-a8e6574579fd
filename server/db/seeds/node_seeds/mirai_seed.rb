sc = ::Scenario.find_by(match_value: "https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

# sc = Scenario.create!(
#   name: "Scenario 1",
#   description: "Mirai",
#   active: true,
#   match_value: "https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw",
#   match_type: "include",
#   user_id: User.find_by(email: "<EMAIL>").id,
# )
# sc.scenario_settings.create!(
#   description: "This is an example of general setting",
#   name: "Example General Setting",
#   active: true,
#   setting_type: "general",
#   general_settings: {
#     chat_window_position: "bottom_right",
#     mobile_chat_window_position: "bottom_right",
#     chat_button_title: "ご購入はこちらから！",
#     chat_window_title: "マツゲ★DX ウモア 購入ページ",
#     chat_window_subtitle: "ご購入はこちらから！",
#     chat_operator_name: "オペレーター(自動応答)",
#     chat_operator_img_url: "https://storage.googleapis.com/cart-test/uploads/56y8ds7jwopn9mruec6h52uj6c2aom7d.png",
#     pc_custom_chat_window_width: "450",
#     pc_custom_chat_window_height: "800",
#     show_button_close: true,
#     show_confirmation_close_modal: true,
#     confirmation_text: "初回限定チャットを閉じますか？",
#     show_chat_start_button: true,
#     start_chatbot_immediately: false,
#   },
# )

# sc.scenario_settings.create!(
#   description: "This is an example of design setting",
#   name: "Example Design Setting",
#   active: true,
#   setting_type: "design",
#   theme_customize_settings: {
#     chat_design_theme_id: 1,
#     theme_color: "green",
#     initiate_btn_bg_color: "#df3772",
#     initiate_btn_txt_color: "#ffffff",
#     header_bg_color: "#df3772",
#     title_txt_color: "#ffffff",
#     chat_window_bg_color: "#f0d6d6",
#     date_system_message_txt_color: "#9e9e9e",
#     message_input_color: "#333333",
#     operator_name_color: "#9e9e9e",
#     operator_msg_body_bg_color: "#df3772",
#     operator_msg_body_txt_color: "#ffffff",
#     customer_msg_body_bg_color: "#ffffff",
#     customer_msg_body_txt_color: "#333333",
#     option_bg_color: "#f0d6d6",
#     option_active_bg_color: "#df3772",
#     option_txt_color: "#333333",
#     option_active_txt_color: "#ffffff",
#     form_bg_color: "#ffffff",
#     form_border_color: "#ffffff",
#     form_input_border_color: "#df3772",
#     form_btn_bg_color: "#df3772",
#     form_btn_txt_color: "#ffffff",
#   },
# )

welcome = sc.nodes.create!(
  root_node: true,
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Welcome",
      },
    ],
  },
)

lable_name = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Name",
      },
    ],
  },
)

welcome.next_node_uid = [lable_name.id]
welcome.save!

full_name_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "name",
    settings: [
      {
        label: "お名前",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "姓",
            variable: "sei",
            type: "input",
            required: true,
            placeholder: "山田",
            ratio: 1,
          },
          {
            label: "名",
            variable: "mei",
            type: "input",
            required: true,
            placeholder: "太郎",
            ratio: 1,
          },
        ],
      },
      {
        label: "フリガナ",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            type: "input",
            required: true,
            placeholder: "ヤマダ",
            ratio: 1,
          },
          {
            label: "メイ",
            variable: "meifuri",
            type: "input",
            required: true,
            placeholder: "タロウ",
            ratio: 1,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

lable_name.next_node_uid = [full_name_node.id]
lable_name.save!

lable_adress = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Address",
      },
    ],
  },
)
full_name_node.next_node_uid = [lable_adress.id]
full_name_node.save!

address_node = sc.nodes.create!(
  node_type: :input,
  label: "住所",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
        required: true,
        placeholder: "1300000",
      },
      {
        label: "都道府県名",
        variable: "prefectures",
        required: true,
        type: "select",
      },
      {
        label: "市区町村名",
        variable: "address01",
        type: "input",
        required: true,
        placeholder: "市区町村名 (千代田区神田神保町)",
      },
      {
        label: "丁目-番地-号",
        variable: "address02",
        type: "input",
        required: true,
        placeholder: "例：２０−１２３−３",
      },
    ],
  },
  next_node_uid: [],
)

lable_adress.next_node_uid = [address_node.id]
lable_adress.save!

lable_tes = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Email and Phone Number",
      },
    ],
  },
)
address_node.next_node_uid = [lable_tes.id]
address_node.save!

tel_email = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "text",
    settings: [
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
        required: true,
        placeholder: "例）<EMAIL>",
      },
      {
        label: "電話番号",
        variable: "tel",
        type: "input",
        required: true,
        placeholder: "0901111****",
      },
    ],
  },
)

lable_tes.next_node_uid = [tel_email.id]
lable_tes.save!

payment_method_node = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "payment_method",
        type: "radio",
        required: true,
        options: [
          {
            text: "後払い",
          },
          {
            text: "クレジットカード（手数料0円）",
          },
        ],
      },
    ],
  },
)
tel_email.next_node_uid = [payment_method_node.id]
tel_email.save!

node_check_payment_method_step1 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "後払い",
      },
    ],
  },
)

node_check_payment_method_step2 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "クレジットカード（手数料0円）",
      },
    ],
  },
)

payment_method_node.next_node_uid = [node_check_payment_method_step1.id, node_check_payment_method_step2.id]
payment_method_node.save!

credit_card  = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "credit_card",
    repeatable: true,
    settings: [
      {
        variable: "credit_card",
        cvv: {
          enabled: false,
          required: false,
          label: "セキュリティコード",
          placeholder: "123",
        },
        brand: {
          label: "カードブランド",
          enabled: false,
          required: false,
          options: [
            { text: "Visa" },
            { text: "MasterCard" },
            { text: "American Express" },
          ],
        },
        name: { enabled: false,
                required: false,
                label: "カード名義(ローマ字氏名)",
                placeholder: "YAMADA HANAKO" },
        number: {
          label: "カード番号",
          placeholder: "4897XXXXXXXXXXXX",
        },
        expired: {
          label: "有効期限",
          month: { label: "月" },
          year: { label: "年" },
        },
      },
    ],
  },
  next_node_uid: [],
)

node_check_payment_method_step2.next_node_uid = [credit_card.id]
node_check_payment_method_step2.save!

node_check_payment_method_step2.next_node_uid = [credit_card.id]
node_check_payment_method_step2.save!

offer_node = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "offer",
        type: "radio",
        required: true,
        options: [
          {
            text: "7日間お試しサイズのまま注文する",
          },
          {
            text: "全額返金も付いて30日間も試せるお得な定期ケアコースで始める",
          },
        ],
      },
    ],
  },
)

node_check_payment_method_step1.next_node_uid = [offer_node.id]
node_check_payment_method_step1.save!
credit_card.next_node_uid = [offer_node.id]
credit_card.save!

button_submit_node = sc.nodes.create!(
  node_type: "button",
  body: {
    repeatable: true,
    settings: [
      {
        content: I18n.t("submit.order_details"),
        type: "template",
      },
      {
        content: I18n.t("submit.policies"),
        type: "policy",
      },
      {
        content: "購入確認画面へ",
        type: "button",
      },
    ],
  },
)

offer_node.next_node_uid = [button_submit_node.id]
offer_node.save!

processing_node = sc.nodes.create!(
  label: "",
  node_type: "headless_tasks",
  body: {
    settings: [
      {
        task: {
          class_name: "MiraiSubmit",
        },
        inputs: {
          url: {
            value: "https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw",
            type: "string",
          },
          sei: {
            value: "",
            type: "string",
          },
          mei: {
            value: "",
            type: "string",
          },
          seifuri: {
            value: "",
            type: "string",
          },
          meifuri: {
            value: "",
            type: "string",
          },
          zipcode: {
            value: "",
            type: "string",
          },
          address02: {
            value: "",
            type: "string",
          },
          tel: {
            value: "",
            type: "string",
          },
          mail: {
            value: "",
            type: "string",
          },
          payment_method: {
            value: "",
            type: "string",
          },
          offer: {
            value: "",
            type: "string",
          },
          credit_card: {
            value: "",
            type: "string",
          },
        },
        outputs: {
          data: {
            message: "",
            error: "",
          },
        },
      },
    ],
  },
)
button_submit_node.next_node_uid = [processing_node.id]
button_submit_node.save!
message_result = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        type: "text",
        variable: "result_msg",
        content: "",
      },
    ],
  },
)
processing_node.next_node_uid = [message_result.id]
processing_node.save!
