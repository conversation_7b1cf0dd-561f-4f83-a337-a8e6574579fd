sc = ::Scenario.find_by(match_value: "https://stg.unicorncart.jp/lp?u=test",
                        user_id: User.find_by(email: "<EMAIL>").id)
return if sc.blank?

sc.nodes.destroy_all

# sc =  Scenario.create!(
#   name: "Scenario 2",
#   description: "Demo scenario",
#   active: true,
#   match_value: "https://stg.unicorncart.jp/lp?u=test",
#   user_id: User.find_by(email: "<EMAIL>").id,
# )

# sc.scenario_settings.create!(
#   description: "This is an example of general setting",
#   name: "Example General Setting",
#   active: true,
#   setting_type: "general",
#   general_settings: {
#     chat_window_position: "bottom_right",
#     mobile_chat_window_position: "bottom_right",
#     chat_button_title: "Purchase here!",
#     chat_window_title: "Matsuge★DX Umoa Purchase Page",
#     chat_window_subtitle: "Purchase here!",
#     chat_operator_name: "Operator (Automated Response)",
#     chat_operator_img_url: "https://storage.googleapis.com/cart-test/uploads/56y8ds7jwopn9mruec6h52uj6c2aom7d.png",
#     pc_custom_chat_window_width: "450",
#     pc_custom_chat_window_height: "800",
#     show_button_close: true,
#     show_confirmation_close_modal: true,
#     confirmation_text: "Do you want to close the chat for first-time users?",
#     show_chat_start_button: false,
#     start_chatbot_immediately: false,
#   },
# )

# sc.scenario_settings.create!(
#   description: "This is an example of design setting",
#   name: "Example Design Setting",
#   active: true,
#   setting_type: "design",
#   theme_customize_settings: {
#     chat_design_theme_id: 1,
#     theme_color: "green",
#     initiate_btn_bg_color: "#1E88E5",
#     initiate_btn_txt_color: "#ffffff",
#     header_bg_color: "#1E88E5",
#     title_txt_color: "#ffffff",
#     chat_window_bg_color: "#E3F2FD",
#     date_system_message_txt_color: "#9e9e9e",
#     message_input_color: "#333333",
#     operator_name_color: "#9e9e9e",
#     operator_msg_body_bg_color: "#1E88E5",
#     operator_msg_body_txt_color: "#ffffff",
#     customer_msg_body_bg_color: "#ffffff",
#     customer_msg_body_txt_color: "#333333",
#     option_bg_color: "#E3F2FD",
#     option_active_bg_color: "#1E88E5",
#     option_txt_color: "#333333",
#     option_active_txt_color: "#ffffff",
#     form_bg_color: "#ffffff",
#     form_border_color: "#ffffff",
#     form_input_border_color: "#1E88E5",
#     form_btn_bg_color: "#1E88E5",
#     form_btn_txt_color: "#ffffff",
#   },
# )

welcome = sc.nodes.create!(
  root_node: true,
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Welcome",
      },
    ],
  },
)

lable_name = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Name",
      },
    ],
  },
)

welcome.next_node_uid = [lable_name.id]
welcome.save!

name_node = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "name",
    settings: [
      {
        label: "お名前",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "姓",
            variable: "sei",
            type: "input",
            required: true,
            placeholder: "山田",
            ratio: 1,
          },
          {
            label: "名",
            variable: "mei",
            type: "input",
            required: true,
            placeholder: "太郎",
            ratio: 1,
          },
        ],
      },
      {
        label: "フリガナ",
        layout: "horizontal",
        ratios: 2,
        settings: [
          {
            label: "セイ",
            variable: "seifuri",
            type: "input",
            required: true,
            placeholder: "ヤマダ",
            ratio: 1,
          },
          {
            label: "メイ",
            variable: "meifuri",
            type: "input",
            required: true,
            placeholder: "タロウ",
            ratio: 1,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

lable_name.next_node_uid = [name_node.id]
lable_name.save!

lable_adress = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Address",
      },
    ],
  },
)
name_node.next_node_uid = [lable_adress.id]
name_node.save!

address_node = sc.nodes.create!(
  node_type: :input,
  label: "住所",
  body: {
    type: "address",
    settings: [
      {
        label: "郵便番号",
        variable: "zipcode",
        type: "input",
        required: true,
        placeholder: "1300000",
      },
      {
        label: "都道府県名",
        variable: "prefectures",
        required: true,
        type: "select",
      },
      {
        label: "市区町村名",
        variable: "address01",
        type: "input",
        required: true,
        placeholder: "市区町村名 (千代田区神田神保町)",
      },
      {
        label: "丁目-番地-号",
        variable: "address02",
        type: "input",
        required: true,
        placeholder: "例：２０−１２３−３",
      },
      {
        label: "建物名・号室 (任意)",
        variable: "address03",
        type: "input",
        required: true,
        placeholder: "例：中野坂上サンブライトツインビル１４階",
      },
    ],
  },
  next_node_uid: [],
)

lable_adress.next_node_uid = [address_node.id]
lable_adress.save!

lable_tes = sc.nodes.create!(
  node_type: :message,
  body: {
    settings: [
      {
        type: "text",
        content: "Please enter Email and Phone Number",
      },
    ],
  },
)
address_node.next_node_uid = [lable_tes.id]
address_node.save!

tel_email_password = sc.nodes.create!(
  node_type: :input,
  body: {
    type: "tel_email_password",
    settings: [
      {
        label: "電話番号",
        type: "input",
        layout: "horizontal",
        ratios: 3,
        settings: [
          {
            variable: "tel1",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "03",
          },
          {
            variable: "tel2",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "0000",
          },
          {
            variable: "tel3",
            type: "input",
            ratio: 1,
            required: true,
            placeholder: "0000",
          },
        ],
      },
      {
        label: "メールアドレス",
        variable: "mail",
        type: "input",
        required: true,
        placeholder: "例）<EMAIL>",
      },
      {
        label: "パスワード",
        variable: "password",
        type: "input",
        required: true,
        placeholder: "パスワード",
      },
    ],
  },
)

lable_tes.next_node_uid = [tel_email_password.id]
lable_tes.save!

sex_and_birthday_node = sc.nodes.create!(
  node_type: :input,
  label: "",
  body: {
    type: "sex_and_birthday",
    settings: [
      {
        label: "性別",
        variable: "sex",
        type: "radio",
        required: true,
        options: [
          {
            text: "男",
          },
          {
            text: "女",
          },
        ],
      },
      {
        label: "生年月日",
        # variable: "birthday", #TODO USE  birthday variable
        type: "date",
        layout: "horizontal",
        ratios: 3,
        settings: [
          {
            label: "日",
            variable: "day",
            type: "select",
            ratio: 1,
            required: true,
          },
          {
            label: "月",
            variable: "month",
            type: "select",
            ratio: 1,
            required: true,
          },
          {
            label: "年",
            variable: "year",
            type: "select",
            ratio: 1,
            required: true,
          },
        ],
      },
    ],
  },
  next_node_uid: [],
)

tel_email_password.next_node_uid = [sex_and_birthday_node.id]
tel_email_password.save!
payment_method_node = sc.nodes.create!(
  node_type: "input",
  body: {
    repeatable: true,
    type: "radio_button_reselectable",
    settings: [
      {
        variable: "payment_method",
        type: "radio",
        required: true,
        options: [
          {
            text: "代金引換",
          },
          {
            text: "Paygentクレジットカード一括",
          },
        ],
      },
    ],
  },
)
sex_and_birthday_node.next_node_uid = [payment_method_node.id]
sex_and_birthday_node.save!

node_check_payment_method_step1 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "代金引換",
      },
    ],
  },
)

node_check_payment_method_step2 = sc.nodes.create!(
  node_type: "condition",
  body: {
    type: "condition",
    settings: [
      {
        variable: "payment_method",
        value: "Paygentクレジットカード一括",
      },
    ],
  },
)

payment_method_node.next_node_uid = [node_check_payment_method_step1.id, node_check_payment_method_step2.id]
payment_method_node.save!

credit_card = sc.nodes.create!(
  node_type: :input,
  label: "お名前",
  body: {
    type: "credit_card",
    repeatable: true,
    settings: [
      {
        variable: "credit_card",
        cvv: {
          enabled: true,
          required: true,
          label: "セキュリティコード",
          placeholder: "123",
        },
        brand: {
          label: "カードブランド",
          enabled: true,
          required: false,
          options: [
            { text: "Visa" },
            { text: "MasterCard" },
            { text: "American Express" },
          ],
        },
        name: { enabled: true,
                required: true,
                label: "カード名義(ローマ字氏名)",
                placeholder: "YAMADA HANAKO" },
        number: {
          label: "カード番号",
          placeholder: "4897XXXXXXXXXXXX",
        },
        expired: {
          label: "有効期限",
          month: { label: "月" },
          year: { label: "年" },
        },
      },
    ],
  },
  next_node_uid: [],
)

node_check_payment_method_step2.next_node_uid = [credit_card.id]
node_check_payment_method_step2.save!

button_submit_node = sc.nodes.create!(
  node_type: "button",
  body: {
    repeatable: true,
    settings: [
      {
        content: I18n.t("submit.order_details"),
        type: "template",
      },
      {
        content: I18n.t("submit.policies"),
        type: "policy",
      },
      {
        content: "購入確認画面へ",
        type: "button",
      },
    ],
  },
)

node_check_payment_method_step1.next_node_uid = [button_submit_node.id]
node_check_payment_method_step1.save!
credit_card.next_node_uid = [button_submit_node.id]
credit_card.save!

processing_node = sc.nodes.create!(
  node_type: "headless_tasks",
  body: {
    repeatable: true,
    settings: [
      {
        task: {
          class_name: "UnicorncartSubmit",
        },
        inputs: {
          url: {
            value: "https://stg.unicorncart.jp/lp?u=1_button_1_bot",
            type: "string",
          },
          sei: {
            value: "",
            type: "string",
          },
          mei: {
            value: "",
            type: "string",
          },
          seifuri: {
            value: "",
            type: "string",
          },
          meifuri: {
            value: "",
            type: "string",
          },
          zipcode: {
            value: "",
            type: "string",
          },
          address02: {
            value: "",
            type: "string",
          },
          tel1: {
            value: "",
            type: "string",
          },
          tel2: {
            value: "",
            type: "string",
          },
          tel3: {
            value: "",
            type: "string",
          },
          mail: {
            value: "",
            type: "string",
          },
          password: {
            value: "",
            type: "string",
          },
          sex: {
            value: "",
            type: "string",
          },
          day: {
            value: "",
            type: "string",
          },
          month: {
            value: "",
            type: "string",
          },
          year: {
            value: "",
            type: "string",
          },
          payment_method: {
            value: "",
            type: "string",
          },
          credit_card: {
            value: "",
            type: "string",
          },
        },
        outputs: {
          data: {
            message: "",
            error: "",
          },
        },
      },
    ],
  },
)
button_submit_node.next_node_uid = [processing_node.id]
button_submit_node.save!

wait_message_node = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "注文を処理しています。",
        type: "text",
      },
    ],
  },
)

processing_node.next_node_uid = [wait_message_node.id]
processing_node.save!

wait_message_node1 = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        content: "少々お待ちください。",
        type: "text",
      },
    ],
  },
)

wait_message_node.next_node_uid = [wait_message_node1.id]
wait_message_node.save!

message_result = sc.nodes.create!(
  node_type: :message,
  body: {
    repeatable: true,
    settings: [
      {
        type: "text",
        variable: "result_msg",
        content: "",
      },
    ],
  },
)

wait_message_node1.next_node_uid = [message_result.id]
wait_message_node1.save!
