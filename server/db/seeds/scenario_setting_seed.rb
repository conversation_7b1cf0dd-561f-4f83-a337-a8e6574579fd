return if Scenario.exists?

emails = ["<EMAIL>", "<EMAIL>"]
match_value = "https://iqdum.jp/"

scenarios = emails.map do |email|
  user_id = User.find_by(email: email).id
  ::Scenario.find_by(match_value: match_value, user_id: user_id)
end

return if scenarios.blank?

scenarios.each do |scenario|
  next unless scenario.present?

  scenario.scenario_settings.destroy

  scenario.scenario_settings.create!(
    description: "This is an example of general setting",
    name: "Example General Setting",
    active: true,
    setting_type: "general",
    general_settings: {
      chat_window_position: "bottom_right",
      mobile_chat_window_position: "bottom_right",
      chat_button_title: "ご購入はこちらから！",
      chat_window_title: "マツゲ★DX ウモア 購入ページ",
      chat_window_subtitle: "ご購入はこちらから！",
      chat_operator_name: "オペレーター(自動応答)",
      chat_operator_img_url: "https://storage.googleapis.com/cart-test/uploads/56y8ds7jwopn9mruec6h52uj6c2aom7d.png",
      pc_custom_chat_window_width: "450",
      pc_custom_chat_window_height: "800",
      show_button_close: true,
      show_confirmation_close_modal: true,
      confirmation_text: "初回限定チャットを閉じますか？",
      show_chat_start_button: true,
      start_chatbot_immediately: false,
    }
  )

  theme_customize_settings = {
    chat_design_theme_id: 1,
    theme_color: "green",
    initiate_btn_bg_color: "#df3772",
    initiate_btn_txt_color: "#ffffff",
    header_bg_color: "#df3772",
    title_txt_color: "#ffffff",
    chat_window_bg_color: "#f0d6d6",
    date_system_message_txt_color: "#9e9e9e",
    message_input_color: "#333333",
    operator_name_color: "#9e9e9e",
    operator_msg_body_bg_color: "#df3772",
    operator_msg_body_txt_color: "#ffffff",
    customer_msg_body_bg_color: "#ffffff",
    customer_msg_body_txt_color: "#333333",
    option_bg_color: "#f0d6d6",
    option_active_bg_color: "#df3772",
    option_txt_color: "#333333",
    option_active_txt_color: "#ffffff",
    form_bg_color: "#ffffff",
    form_border_color: "#ffffff",
    form_input_border_color: "#df3772",
    form_btn_bg_color: "#df3772",
    form_btn_txt_color: "#ffffff",
  }

  theme_customize_settings.merge!(
    chat_design_theme_id: 2,
    initiate_btn_bg_color: "#1E88E5",
    header_bg_color: "#1E88E5",
    chat_window_bg_color: "#E3F2FD",
    operator_msg_body_bg_color: "#1E88E5",
    form_input_border_color: "#1E88E5",
    form_btn_bg_color: "#1E88E5"
  ) if scenario == scenarios[1]

  scenario.scenario_settings.create!(
    description: "This is an example of design setting",
    name: "Example Design Setting",
    active: true,
    setting_type: "design",
    theme_customize_settings: theme_customize_settings
  )
end
