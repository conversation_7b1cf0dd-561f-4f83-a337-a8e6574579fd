
return if Scenario.exists?
ActiveRecord::Base.connection.truncate_tables(:scenarios)

math_values = ["https://iqdum.jp/", "https://laclulu.xyz/lp?u=upsell_chattest"]
math_values.each do |mv|
  Scenario.create!(
    name: "Scenario 1",
    description: "NP and Credit Card Failed Return Credit Card Form",
    active: true,
    match_value: mv ,
    match_type: "include",
    user_id: User.find_by(email: "<EMAIL>").id,
  )
end

# Scenario.create!(
#   name: "Scenario 2",
#   description: "NP and Credit Card Error Return Payment Method Select Option",
#   active: true,
#   match_value: "https://iqdum.jp/",
#   user_id: User.find_by(email: "<EMAIL>").id,
# )
