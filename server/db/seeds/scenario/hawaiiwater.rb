def create_node(sc)
  # Root node: first_message
  first_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "いらっしゃいませ！",
        },
      ],
    },
    label: "first_message",
    node_type: "message",
    root_node: true,
  )

  sc.root_node_uid = first_message.id
  sc.save!

  addressee_node = sc.nodes.create!(
    node_type: "input",
    body: {
      type: "radio_button_reselectable",
      settings: [
        {
          variable: "addressee",
          type: "radio",
          required: true,
          options: [
            {
              text: "東京・神奈川・千葉・埼玉・茨城",
            },
            {
              text: "その他の本州・北海道・四国・九州",
            },
          ],
        },
      ],
    },
  )

  # 住所_メッセージ
  address_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "次にご住所を入力してください。",
        },
      ],
    },
    label: "住所_メッセージ",
    node_type: "message",
  )

  # Analy_住所入力
  analy_address_input = sc.nodes.create!(
    body: {
      type: "address",
      settings: [
        {
          type: "input",
          label: "郵便番号",
          required: true,
          variable: "zipcode",
          placeholder: "100000",
        },
        {
          type: "select",
          label: "都道府県",
          required: true,
          variable: "prefectures",
        },
        {
          type: "input",
          label: "市区町村",
          required: true,
          variable: "address01",
          placeholder: "港区○○",
        },
        {
          type: "input",
          label: "番地",
          required: true,
          variable: "address02",
          placeholder: "",
        },
        {
          type: "input",
          label: "建物名",
          required: false,
          variable: "address03",
          placeholder: "",
        },
      ],
    },
    label: "Analy_住所入力",
    node_type: "input",
  )

  # # headless_task
  # headless_task = sc.nodes.create!(
  #   body: {
  #     repeatable: true,
  #     settings: [
  #       {
  #         task: {
  #           class_name: "HawaiiWaterDesiredInstallationDate",
  #         },
  #         inputs: {
  #           url: {
  #             value: "https://www.hawaiiwater.co.jp/regist/index",
  #             type: "string",
  #           },
  #           zipcode: {
  #             value: "",
  #             type: "string",
  #           },
  #           prefectures: {
  #             value: "",
  #             type: "string",
  #           },
  #         },
  #         outputs: {
  #           data: {
  #             message: "",
  #             error: "",
  #             optionValues: []
  #           },
  #         },
  #       },
  #     ],
  #   },
  #   label: "headless_task",
  #   node_type: "headless_tasks",
  # )

  select_option = sc.nodes.create!(
    node_type: "input",
    label: "設置希望日",
    body: {
      type: "select",
      settings: [
        {
          label: "設置希望日",
          variable: "scheduled_date",
          type: "select",
          required: true,
          options: [],
          crawler_data:
            {
              task: {
                class_name: "HawaiiWaterDesiredInstallationDate",
              },
              inputs: {
                url: {
                  value: "https://www.hawaiiwater.co.jp/regist/index",
                  type: "string",
                },
                addressee: {
                  value: "",
                  type: "string",
                },
                zipcode: {
                  value: "",
                  type: "string",
                },
                prefectures: {
                  value: "",
                  type: "string",
                },
              },
              outputs: {
                data: {
                  message: "",
                  error: "",
                  optionValues: [],
                },
              },
              ui_texts: {
                button: "データを取得する",
                loading: "読み込み中...",
                error: "データの取得に失敗しました。入力内容をご確認ください。",
                success: "データが正常に取得されました",
                options_suffix: "オプション"
              }
            },

        },
      ],

    },
  )




  


    analy_tel_email_input = sc.nodes.create!(
    body: {
      type: "tel_email_password",
      settings: [
        {
          label: "生年月日",
          type: "input",
          layout: "horizontal",
          ratios: 3,
          settings: [
            {
              variable: "tel1",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "03",
            },
            {
              variable: "tel2",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "0000",
            },
            {
              variable: "tel3",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "0000",
            },
          ],
        },
        {
          type: "input",
          label: "メールアドレス",
          required: true,
          variable: "mail",
          placeholder: "例）<EMAIL>",
        },
      ],
    },
    label: "Analy_電話番号とメールアドレス入力",
    node_type: "input",
  )


  analy_sex = sc.nodes.create!(
    node_type: "input",
    label: "設置希望日",
    body: {
      type: "select",
      settings: [
        {
          label: "性別",
          variable: "sex",
          type: "select",
          required: true,
          options: [
            {
              text: "男性",
            },
            { 
              text: "女性",
            }
          ],
        },
      ],

    },
  )



  analy_birthday = sc.nodes.create!(
    node_type: "input",
    label: "設置希望日",
    body: {
      type: "select",
      settings: [
        {
          label: "性別",
          variable: "birthday_year",
          type: "select",
          required: true,
          options: [
            {
              text: "1990年以降",
            },
            { 
              text: "1990年以前",
            }
          ],
        },

        {
          label: "生年",
          variable: "birthday_month",
          type: "select",
          required: true,
          options: [
            {
              text: "1月",
            },
            { 
              text: "2月",
            }
          ],  
        },
      ],

    },
  )

  result_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "ありがとうございます",
        },
      ],
    },
    label: "result_message",
    node_type: "message",
  )



  # Assign next_node_uid relationships
  first_message.next_node_uid = [addressee_node.id]
  addressee_node.next_node_uid = [address_message.id]
  address_message.next_node_uid = [analy_address_input.id]
  analy_address_input.next_node_uid = [select_option.id]
  select_option.next_node_uid = [analy_tel_email_input.id]
  analy_tel_email_input.next_node_uid = [analy_sex.id]
  analy_sex.next_node_uid = [analy_birthday.id]
  analy_birthday.next_node_uid = [result_message.id]

  # Save all nodes
  first_message.save!
  addressee_node.save!
  address_message.save!
  analy_address_input.save!
  select_option.save!
  analy_tel_email_input.save!
  analy_sex.save!
  analy_birthday.save!

end

def create_scenario
  sc = Scenario.create!(
    name: "Scenario Genpeiseiyaku",
    description: "Genpeiseiyaku",
    active: true,
    match_value: "https://sb1.genpeiseiyaku.com/ab/gHLcGxwFwO_PNWlVbqSw",
    match_type: "include",
    user_id: User.find_by(email: "<EMAIL>").id,
  )
  sc.scenario_settings.create!(
    description: "This is an example of general setting",
    name: "Example General Setting",
    active: true,
    setting_type: "general",
    general_settings: {
      chat_button_title: "ご購入はこちらから！",
      chat_operator_img_url: "https://cms.ugchatcms.net/files/genpeiseiyaku/images/guid.png",
      chat_operator_name: "",
      chat_window_position: "bottom_right",
      mobile_chat_window_position: "bottom_right",
      chat_window_title: "~ビナキナールHG顆粒~",
      chat_window_subtitle: "お得にご注文",
      confirmation_text: "",
      pc_custom_chat_window_height: "800",
      pc_custom_chat_window_width: "450",
      show_button_close: true,
      show_chat_start_button: true,
      start_chatbot_immediately: true,
      show_confirmation_close_modal: true,
    },
  )

  sc.scenario_settings.create!(
    description: "This is an example of design setting",
    name: "Example Design Setting",
    active: true,
    setting_type: "design",
    theme_customize_settings: {
      chat_design_theme_id: nil,
      chat_window_bg_color: "#FAB0B3",
      customer_msg_body_bg_color: nil,
      customer_msg_body_txt_color: nil,
      date_system_message_txt_color: nil,
      form_bg_color: "#fff2cc",
      form_border_color: "#fff2cc",
      form_btn_bg_color: "#f4565e",
      form_btn_txt_color: "#fff",
      form_input_border_color: "#fff2cc",
      header_bg_color: "#FAB0B3",
      title_txt_color: "#fffff",
      initiate_btn_bg_color: "#FAB0B3",
      initiate_btn_txt_color: "#ffffff",
      message_input_color: nil,
      operator_msg_body_bg_color: "#fff",
      operator_msg_body_txt_color: "#000",
      operator_name_color: "#FAB0B3",
      option_active_bg_color: "#fff",
      option_active_txt_color: "#fff",
      option_bg_color: "#fff",
      option_txt_color: "#f4565e",
      theme_color: nil,
      progress_bar_bg_color: nil,
      progress_percentage_bg_color: nil,
      progress_percentage_color: nil,
    },
  )
  sc
end

sc = ::Scenario.find_by(match_value: "https://www.hawaiiwater.co.jp/regist/index",
                        user_id: User.find_by(email: "<EMAIL>").id)

ActiveRecord::Base.transaction do
  sc = create_scenario if sc.blank?

  puts "Scenario HawaiiWater create... #{sc.id}"

  return if sc.blank?

  puts "Scenario HawaiiWater delete node..."
  sc.nodes.destroy_all

  puts "Scenario Genpeiseiyaku create node..."
  create_node(sc)
end

puts "Scenario Genpeiseiyaku seed completed"
