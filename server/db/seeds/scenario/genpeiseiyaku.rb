def create_node(sc)
  # Root node: first_message
  first_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "いらっしゃいませ！",
        },
      ],
    },
    label: "first_message",
    node_type: "message",
    root_node: true,
  )
  sc.root_node_uid = first_message.id
  sc.save!

  # second_message
  second_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "こちらからご購入いただけます！ 案内に従ってお手続きください",
        },
      ],
    },
    label: "second_message",
    node_type: "message",
  )

  # FV_images
  fv_images = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "image",
          content: "https://cms.ugchatcms.net/files/genpeiseiyaku/images/FV.png\t",
        },
      ],
    },
    label: "FV_images",
    node_type: "message",
  )

  # 名前_メッセージ
  name_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "まずはお名前を入力してください。",
        },
      ],
    },
    label: "名前_メッセージ",
    node_type: "message",
  )

  # Analy_名前入力
  analy_name_input = sc.nodes.create!(
    body: {
      type: "name",
      settings: [
        {
          label: "お名前",
          layout: "horizontal",
          ratios: 2,
          settings: [
            {
              type: "input",
              label: "",
              ratio: 1,
              required: true,
              variable: "sei",
              placeholder: "山田",
            },
            {
              type: "input",
              label: "",
              ratio: 1,
              required: true,
              variable: "mei",
              placeholder: "太郎",
            },
          ],
        },
        {
          label: "フリガナ",
          layout: "horizontal",
          ratios: 2,
          settings: [
            {
              type: "input",
              label: "",
              ratio: 1,
              required: true,
              variable: "seifuri",
              placeholder: "ヤマダ",
            },
            {
              type: "input",
              label: "",
              ratio: 1,
              required: true,
              variable: "meifuri",
              placeholder: "タロウ",
            },
          ],
        },
      ],
    },
    label: "Analy_名前入力",
    node_type: "input",
  )

  # 住所_メッセージ
  address_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "次にご住所を入力してください。",
        },
      ],
    },
    label: "住所_メッセージ",
    node_type: "message",
  )

  # Analy_住所入力
  analy_address_input = sc.nodes.create!(
    body: {
      type: "address",
      settings: [
        {
          type: "input",
          label: "郵便番号",
          required: true,
          variable: "zipcode",
          placeholder: "100000",
        },
        {
          type: "select",
          label: "都道府県",
          required: true,
          variable: "prefectures",
        },
        {
          type: "input",
          label: "市区町村",
          required: true,
          variable: "address01",
          placeholder: "港区○○",
        },
        {
          type: "input",
          label: "番地",
          required: true,
          variable: "address02",
          placeholder: "",
        },
        {
          type: "input",
          label: "建物名",
          required: false,
          variable: "address03",
          placeholder: "",
        },
      ],
    },
    label: "Analy_住所入力",
    node_type: "input",
  )

  # 電話番号とメールアドレス_メッセージ
  tel_email_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "ご連絡先を入力してください。",
        },
      ],
    },
    label: "電話番号とメールアドレス_メッセージ",
    node_type: "message",
  )

  # Analy_電話番号とメールアドレス入力
  analy_tel_email_input = sc.nodes.create!(
    body: {
      type: "tel_email_password",
      settings: [
        {
          label: "生年月日",
          type: "input",
          layout: "horizontal",
          ratios: 3,
          settings: [
            {
              variable: "tel1",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "03",
            },
            {
              variable: "tel2",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "0000",
            },
            {
              variable: "tel3",
              type: "input",
              ratio: 1,
              required: true,
              placeholder: "0000",
            },
          ],
        },
        {
          type: "input",
          label: "メールアドレス",
          required: true,
          variable: "mail",
          placeholder: "例）<EMAIL>",
        },
      ],
    },
    label: "Analy_電話番号とメールアドレス入力",
    node_type: "input",
  )

  # パスワード_メッセージ
  password_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "パスワードを設定してください。",
        },
      ],
    },
    label: "パスワード_メッセージ",
    node_type: "message",
  )

  # Analy_パスワード入力
  analy_password_input = sc.nodes.create!(
    body: {
      type: "password",
      settings: [
        {
          type: "input",
          label: "パスワード半角英数字6～16文字",
          required: true,
          variable: "password",
          placeholder: "",
        },
      ],
    },
    label: "Analy_パスワード入力",
    node_type: "input",
  )

  # 支払い方法_メッセージ
  payment_method_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "お支払い方法を選んでください。",
        },
      ],
    },
    label: "支払い方法_メッセージ",
    node_type: "message",
  )

  # Analy_支払い方法選択
  analy_payment_method_selection = sc.nodes.create!(
    body: {
      type: "radio_button_reselectable",
      settings: [
        {
          type: "radio",
          options: [
            {
              text: "NP後払い",
            },
            {
              text: "クレジットカード",
            },
          ],
          required: true,
          variable: "payment_method",
        },
      ],
      repeatable: true,
    },
    label: "Analy_支払い方法選択",
    node_type: "input",
  )

  # 後払い_condition
  np_condition = sc.nodes.create!(
    body: {
      settings: [
        {
          value: "NP後払い",
          variable: "payment_method",
        },
      ],
      repeatable: true,
    },
    label: "後払い_condition",
    node_type: "condition",
  )

  # 後払い選択後_images
  np_selected_images = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "image",
          content: "https://cms.ugchatcms.net/files/genpeiseiyaku/images/np.gif\t",
        },
      ],
    },
    label: "後払い選択後_images",
    node_type: "message",
  )

  # クレジットカード_condition
  credit_card_condition = sc.nodes.create!(
    body: {
      settings: [
        {
          value: "クレジットカード",
          variable: "payment_method",
        },
      ],
      repeatable: true,
    },
    label: "クレジットカード_condition",
    node_type: "condition",
  )

  # クレジットカード選択後_メッセージ
  credit_card_selected_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "カード情報を入力してください。",
        },
      ],
    },
    label: "クレジットカード選択後_メッセージ",
    node_type: "message",
  )

  # Analy_クレジット入力
  analy_credit_input = sc.nodes.create!(
    body: {
      type: "credit_card",
      settings: [
        {
          cvv: {
            label: "セキュリティコード",
            enabled: true,
            required: true,
            placeholder: "123",
          },
          name: {
            label: "カード名義",
            enabled: true,
            required: true,
            placeholder: "",
          },
          brand: {
            label: "カードブランド",
            enabled: false,
            options: [
              {
                text: "Visa",
              },
              {
                text: "MasterCard",
              },
              {
                text: "American Express",
              },
            ],
          },
          number: {
            label: "カード番号",
            placeholder: "",
          },
          expired: {
            year: {
              label: "年",
            },
            label: "有効期限",
            month: {
              label: "月",
            },
          },
          variable: "credit_card",
        },
      ],
      repeatable: true,
    },
    label: "Analy_クレジット入力",
    node_type: "input",
  )

  # 注文確認＿メッセージ
  order_confirmation_message = sc.nodes.create!(
    body: {
      repeatable: true,
      settings: [
        {
          type: "text",
          content: "内容をご確認の上、注文確定ボタンを押してください。",
        },
      ],
    },
    label: "注文確認＿メッセージ",
    node_type: "message",
  )

  # Analy_注文確定ボタン
  analy_order_confirmation_button = sc.nodes.create!(
    body: {
      repeatable: true,
      settings: [
        {
          type: "button",
          content: "注文確定",
        },
      ],
    },
    label: "Analy_注文確定ボタン",
    node_type: "button",
  )

  # headless_task
  headless_task = sc.nodes.create!(
    body: {
      repeatable: true,
      settings: [
        {
          task: {
            class_name: "GenpeiseiyakuSubmit",
          },
          inputs: {
            url: {
              value: "https://sb1.genpeiseiyaku.com/ab/gHLcGxwFwO_PNWlVbqSw",
              type: "string",
            },
            sei: {
              value: "",
              type: "string",
            },
            mei: {
              value: "",
              type: "string",
            },
            seifuri: {
              value: "",
              type: "string",
            },
            meifuri: {
              value: "",
              type: "string",
            },
            zipcode: {
              value: "",
              type: "string",
            },
            address02: {
              value: "",
              type: "string",
            },
            address03: {
              value: "",
              type: "string",
            },
            tel: {
              value: "",
              type: "string",
            },
            tel1: {
              value: "",
              type: "string",
            },
            tel2: {
              value: "",
              type: "string",
            },
            tel3: {
              value: "",
              type: "string",
            },
            mail: {
              value: "",
              type: "string",
            },
            password: {
              value: "",
              type: "string",
            },
            payment_method: {
              value: "",
              type: "string",
            },
            credit_card: {
              value: "",
              type: "string",
            },
          },
          outputs: {
            data: {
              message: "",
              error: "",
            },
          },
        },
      ],
    },
    label: "headless_task",
    node_type: "headless_tasks",
  )

  # 注文処理中_メッセージ
  order_processing_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "ただいまご注文内容を確認しております。\u003cbr\u003e\nこのままで少々お待ちください（混雑時には最大1分程度かかる場合がございますが、画面を閉じずにお待ちください）",
        },
      ],
      repeatable: true,
    },
    label: "注文処理中_メッセージ",
    node_type: "message",
  )

  # result_msg
  result_msg = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "",
          fallback_error: "Quá nhiều yêu cầu. Xin vui lòng thử lại sau.",
          variable: "result_msg",
        },
      ],
      repeatable: true,
    },
    label: "result_msg",
    node_type: "message",
  )

  # エラーチェックOK_condition
  error_check_ok_condition = sc.nodes.create!(
    body: {
      settings: [
        {
          value: false,
          variable: "error",
        },
      ],
      repeatable: true,
    },
    label: "エラーチェックOK_condition",
    node_type: "condition",
  )

  # エラーチェック失敗_condition
  error_check_failed_condition = sc.nodes.create!(
    body: {
      settings: [
        {
          value: true,
          variable: "error",
        },
      ],
      repeatable: true,
    },
    label: "エラーチェック失敗_condition",
    node_type: "condition",
  )

  set_value_payment_method = sc.nodes.create!(
    node_type: "set_value",
    body: {
      repeatable: true,
      settings: [
        {
          variable: "payment_method",
          value: "クレジットカード",
        },
      ],
    },
  )

  thanks_message = sc.nodes.create!(
    body: {
      settings: [
        {
          type: "text",
          content: "ご注文いただきありがとうございます。",
        },
      ],
      repeatable: true,
    },
    label: "thanks_message",
    node_type: "message",
  )

  # Assign next_node_uid relationships
  first_message.next_node_uid = [second_message.id]
  second_message.next_node_uid = [fv_images.id]
  fv_images.next_node_uid = [name_message.id]
  name_message.next_node_uid = [analy_name_input.id]
  analy_name_input.next_node_uid = [address_message.id]
  address_message.next_node_uid = [analy_address_input.id]
  analy_address_input.next_node_uid = [tel_email_message.id]
  tel_email_message.next_node_uid = [analy_tel_email_input.id]
  analy_tel_email_input.next_node_uid = [password_message.id]
  password_message.next_node_uid = [analy_password_input.id]
  analy_password_input.next_node_uid = [payment_method_message.id]
  payment_method_message.next_node_uid = [analy_payment_method_selection.id]
  analy_payment_method_selection.next_node_uid = [np_condition.id, credit_card_condition.id]
  np_condition.next_node_uid = [np_selected_images.id]
  np_selected_images.next_node_uid = [order_confirmation_message.id]
  credit_card_condition.next_node_uid = [credit_card_selected_message.id]
  credit_card_selected_message.next_node_uid = [analy_credit_input.id]
  analy_credit_input.next_node_uid = [order_confirmation_message.id]
  order_confirmation_message.next_node_uid = [analy_order_confirmation_button.id]
  analy_order_confirmation_button.next_node_uid = [headless_task.id]
  headless_task.next_node_uid = [order_processing_message.id]
  order_processing_message.next_node_uid = [result_msg.id]
  result_msg.next_node_uid = [error_check_ok_condition.id, error_check_failed_condition.id]
  error_check_failed_condition.next_node_uid = [set_value_payment_method.id]
  set_value_payment_method.next_node_uid = [analy_credit_input.id]
  error_check_ok_condition.next_node_uid = [thanks_message.id]
  # analy_credit_reinput.next_node_uid = nil

  # Save all nodes
  first_message.save!
  second_message.save!
  fv_images.save!
  name_message.save!
  analy_name_input.save!
  address_message.save!
  analy_address_input.save!
  tel_email_message.save!
  analy_tel_email_input.save!
  password_message.save!
  analy_password_input.save!
  payment_method_message.save!
  analy_payment_method_selection.save!
  np_condition.save!
  np_selected_images.save!
  credit_card_condition.save!
  credit_card_selected_message.save!
  analy_credit_input.save!
  order_confirmation_message.save!
  analy_order_confirmation_button.save!
  headless_task.save!
  order_processing_message.save!
  result_msg.save!
  error_check_ok_condition.save!
  error_check_failed_condition.save!
  set_value_payment_method.save!
end

def create_scenario
  sc = Scenario.create!(
    name: "Scenario Genpeiseiyaku",
    description: "Genpeiseiyaku",
    active: true,
    match_value: "https://sb1.genpeiseiyaku.com/ab/gHLcGxwFwO_PNWlVbqSw",
    match_type: "include",
    user_id: User.find_by(email: "<EMAIL>").id,
  )
  sc.scenario_settings.create!(
    description: "This is an example of general setting",
    name: "Example General Setting",
    active: true,
    setting_type: "general",
    general_settings: {
      chat_button_title: "ご購入はこちらから！",
      chat_operator_img_url: "https://cms.ugchatcms.net/files/genpeiseiyaku/images/guid.png",
      chat_operator_name: "",
      chat_window_position: "bottom_right",
      mobile_chat_window_position: "bottom_right",
      chat_window_title: "~ビナキナールHG顆粒~",
      chat_window_subtitle: "お得にご注文",
      confirmation_text: "",
      pc_custom_chat_window_height: "800",
      pc_custom_chat_window_width: "450",
      show_button_close: true,
      show_chat_start_button: true,
      start_chatbot_immediately: true,
      show_confirmation_close_modal: true,
    },
  )

  sc.scenario_settings.create!(
    description: "This is an example of design setting",
    name: "Example Design Setting",
    active: true,
    setting_type: "design",
    theme_customize_settings: {
      chat_design_theme_id: nil,
      chat_window_bg_color: "#FAB0B3",
      customer_msg_body_bg_color: nil,
      customer_msg_body_txt_color: nil,
      date_system_message_txt_color: nil,
      form_bg_color: "#fff2cc",
      form_border_color: "#fff2cc",
      form_btn_bg_color: "#f4565e",
      form_btn_txt_color: "#fff",
      form_input_border_color: "#fff2cc",
      header_bg_color: "#FAB0B3",
      title_txt_color: "#fffff",
      initiate_btn_bg_color: "#FAB0B3",
      initiate_btn_txt_color: "#ffffff",
      message_input_color: nil,
      operator_msg_body_bg_color: "#fff",
      operator_msg_body_txt_color: "#000",
      operator_name_color: "#FAB0B3",
      option_active_bg_color: "#fff",
      option_active_txt_color: "#fff",
      option_bg_color: "#fff",
      option_txt_color: "#f4565e",
      theme_color: nil,
      progress_bar_bg_color: nil,
      progress_percentage_bg_color: nil,
      progress_percentage_color: nil,
    },
  )
  sc
end

sc = ::Scenario.find_by(match_value: "https://sb1.genpeiseiyaku.com/ab/gHLcGxwFwO_PNWlVbqSw",
                        user_id: User.find_by(email: "<EMAIL>").id)

ActiveRecord::Base.transaction do
  sc = create_scenario if sc.blank?

  puts "Scenario Genpeiseiyaku create... #{sc.id}"

  return if sc.blank?

  puts "Scenario Genpeiseiyaku delete node..."
  sc.nodes.destroy_all

  puts "Scenario Genpeiseiyaku create node..."
  create_node(sc)
end

puts "Scenario Genpeiseiyaku seed completed"
