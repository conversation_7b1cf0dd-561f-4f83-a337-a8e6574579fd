(function () {
  window.productFlag = 0;
  window.postFlag = 0;
  window.messageFlag = 0;

  var existingIframe = document.getElementById("iqdum_iframe");

  if (existingIframe) {
    existingIframe.parentNode.removeChild(existingIframe);
  }

  /**
   * Sets the value of an input element identified by the given selector.
   * Triggers the 'blur', 'change', and 'input' events on the element after setting the value.
   *
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the input element.
   * @param {string} val - The value to be set.
   * @returns {Promise<void>} A promise that resolves once the value is set and events are triggered.
   */
  var send_keys = (doc, selector, val) =>
    new Promise((resolve) => {
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          _target.value = val;
          ["blur", "change", "input"].forEach((e) => {
            _target.dispatchEvent(new Event(e, { bubbles: true }));
          });
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  /**
   * Sends the selected text to a specified element.
   *
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the target element.
   * @param {string} val - The value to be selected.
   * @returns {Promise<void>} - A promise that resolves when the text is sent.
   */
  var send_selected_text = (doc, selector, val) =>
    new Promise((resolve) => {
      if (!val) {
        return;
      }
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          Array.from(_target.options).filter((x) =>
            x.textContent.includes(val)
          )[0].selected = true;
          _target.dispatchEvent(new Event("change", { bubbles: true }));
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  function set_gender_input(doc, selector, sex) {
    var divElement = doc.querySelector(selector);

    var inputs = divElement.getElementsByTagName("input");

    for (var i = 0; i < inputs.length; i++) {
      var label = doc.querySelector(`label[for="${inputs[i].id}"]`);

      if (label.textContent === sex) {
        inputs[i].click();
        break;
      }
    }
  }

  /**
   * Waits for an element to be present in the document and returns it.
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the element to wait for.
   * @param {number} [delay=300] - The delay in milliseconds between each search attempt.
   * @param {number} [tries=50] - The maximum number of search attempts.
   * @returns {Promise<Element|null>} - A promise that resolves with the element if found, or null if not found within the specified number of tries.
   */
  function _waitForElement(doc, selector, delay = 300, tries = 50) {
    const element = doc.querySelector(selector);

    if (!window[`__${selector}`]) {
      window[`__${selector}`] = 0;
    }

    function _search() {
      return new Promise((resolve) => {
        window[`__${selector}`]++;
        setTimeout(resolve, delay);
      });
    }

    if (element === null) {
      if (window[`__${selector}`] >= tries) {
        window[`__${selector}`] = 0;
        return Promise.resolve(null);
      }

      return _search().then(() => _waitForElement(doc, selector));
    } else {
      return Promise.resolve(element);
    }
  }

  // パスワード自動入力
  function setPasswordInput(doc) {
    doc.querySelector("#order_name01").addEventListener("click", function () {
      doc.querySelector("#password").dispatchEvent(new Event("blur"));
    });
    doc
      .querySelector('select[name="year"]')
      .addEventListener("click", function () {
        doc.querySelector("#password").dispatchEvent(new Event("blur"));
      });
    doc.querySelector("#password").addEventListener("blur", function () {
      doc.querySelector("#password").value = "aaaa1234";
    });
  }

  var iframe = null;
  iframe = document.createElement("iframe");
  iframe.id = "iqdum_iframe";
  iframe.src = window.parent.location.href;
  iframe.style.width = "150px";
  iframe.style.height = "200px";
  iframe.style.left = "0";
  iframe.style.position = "fixed";
  iframe.style.bottom = "0";
  iframe.style.borderRadius = "10px";
  iframe.style.margin = "10px";
  iframe.style.boxShadow = "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px";
  iframe.style.zIndex = "-999";
  iframe.style.left = "-9999px";
  iframe.style.position = "absolute";
  document.body.appendChild(iframe);
  iframe.addEventListener("load", () => run());

  async function run() {
    var ifm = document.querySelector("iframe#iqdum_iframe");
    var ifmdoc = ifm.contentWindow.document;
    var ifmPath = ifm.contentWindow.location.pathname;
    console.log(ifm.contentWindow.location);
    try {
      if (window.productFlag === 0) {
        console.time("fill_data");
        await post();
        console.timeEnd("fill_data");
      }

      if (window.productFlag == 1 && window.postFlag == 0) {
        console.time("click_button_submit");
        if (
          ["/smp/shopping/confirm.php", "/shopping/confirm.php"].includes(
            ifmPath
          )
        ) {
          await confirm_page();
        } else if (
          ["/smp/shopping/lp.php", "/shopping/lp.php"].includes(ifmPath)
        ) {
          await parse_lp_error(ifmdoc);
        }
        console.timeEnd("click_button_submit");
      } else if (window.postFlag == 1 && window.messageFlag == 0) {
        console.time("thanks_page");
        await thanks_page();
        console.timeEnd("thanks_page");
      }
    } catch (error) {
      console.error("Error parsing JSON:", error.message);
    }
  }

  async function post() {
    var ifm = document.querySelector("body iframe#iqdum_iframe");
    var ifmdoc = ifm.contentWindow.document;
    var ifmPath = ifm.contentWindow.location.pathname;

    window.scrollTo(0, ifmdoc.body.scrollHeight);

    console.log("agreement1", `v.agreement1`);
    console.log("agreement2", `v.agreement2`);

    if (["/smp/shopping/lp.php", "/shopping/lp.php"].includes(ifmPath)) {
      // パスワード自動入力

      var login = false;
      var res_btn = ifmdoc.querySelector(".mgt20");
      if (res_btn) {
        login = res_btn.style.display == "none";
      } else {
        var register_btn = ifmdoc.querySelector(".register_btn");
        login = !register_btn;
      }

      // product
      send_selected_text(ifmdoc, "#product_id", `v.product`);

      if (!login) {
        setPasswordInput(ifmdoc);
        // name

        send_keys(ifmdoc, "#order_name01", `v.sei`);
        send_keys(ifmdoc, "#order_name02", `v.mei`);

        send_keys(ifmdoc, "#order_kana01", `v.seifuri`);
        send_keys(ifmdoc, "#order_kana02", `v.meifuri`);

        // address
        send_keys(ifmdoc, "#order_zip", `v.zipcode`);
        var order_addr02 = `v.address02` + `v.address03`;
        send_keys(ifmdoc, "#order_addr02", order_addr02);

        // tel
        var tel = `v.tel` + `v.tel1` + `v.tel2` + `v.tel3`;
        send_keys(ifmdoc, "input[name='order_tel']", tel);

        // email
        send_keys(ifmdoc, "#order_email", `v.mail`);

        // sex
        if (`v.sex` === "男") {
          set_gender_input(ifmdoc, "#order_sex_group", "男");
        } else {
          set_gender_input(ifmdoc, "#order_sex_group", "女");
        }

        // birthday
        send_selected_text(
          ifmdoc,
          ".birth-select select[name='year']",
          `v.year`
        );
        send_selected_text(
          ifmdoc,
          ".birth-select select[name='month']",
          `v.month`
        );
        send_selected_text(ifmdoc, ".birth-select select[name='day']", `v.day`);

        ifmdoc.querySelector("input[name='password']").value = "aaaa1234";
      }
      // coupon
      send_keys(ifmdoc, "#coupon_code", `v.coupon`);
      // payment
      await _waitForElement(ifmdoc, "#payment_id");
      await send_selected_text(ifmdoc, "#payment_id", `v.payment_method`);

      console.log(`v.scheduled_date`);
      console.log(`v.scheduled_time`);
      // credit
      if ("v.payment_method".includes("クレジット")) {
        await _waitForElement(
          ifmdoc,
          ".gp_paygent_token_input_error.js-validate"
        );

        var credit_data = Object.assign({}, window.creditCard);

        var credit_name = credit_data.card_name;
        var credit_number = credit_data.card_number;
        var credit_expiry_month = credit_data.card_expired_month;
        var credit_expiry_year = credit_data.card_expired_year;
        var credit_cvv = credit_data.card_cvv;
        console.log("card_cvv", credit_cvv);

        await send_keys(ifmdoc, "#gp_paygent_token_card_number", credit_number);
        await send_keys(
          ifmdoc,
          "#gp_paygent_token_card_expires_month",
          credit_expiry_month
        );
        await send_keys(
          ifmdoc,
          "#gp_paygent_token_card_expires_year",
          credit_expiry_year
        );
        await send_keys(ifmdoc, "#gp_paygent_token_card_name", credit_name);
      }

      if (!ifmdoc.querySelector("input[name='agree']").checked) {
        ifmdoc.querySelector("input[name='agree']").click();
      }

      await _waitForElement(ifmdoc, "#confirm_submit_image");
      ifmdoc.querySelector("#confirm_submit_image").click();

      await parse_lp_error(ifmdoc);
      console.log("click");
      window.productFlag = 1;
    }
  }

  async function confirm_page() {
    var ifm = document.querySelector("iframe#iqdum_iframe");
    var ifmdoc = ifm.contentWindow.document;

    var submitWeb = ifmdoc.querySelector(".ordercomp_bt");

    if (submitWeb) {
      ifmdoc.querySelector(".ordercomp_bt").click();
    } else {
      ifmdoc.querySelector(".nx_btn a").click();
    }

    window.postFlag = 1;
  }

  async function thanks_page() {
    var ifm = document.querySelector("iframe#iqdum_iframe");
    var ifmdoc = ifm.contentWindow.document;

    var alertContent = "";
    var textComplete = ifmdoc.querySelector("#completetext em")
      ? ifmdoc.querySelector("#completetext em").innerHTML.trim()
      : "";
    var messagearea = ifmdoc.querySelector(".messagearea p")
      ? ifmdoc.querySelector(".messagearea p").innerHTML.trim()
      : "";

    var idNumber = ifmdoc.querySelector("#completetext p")
      ? ifmdoc.querySelector("#completetext p").innerHTML.trim()
      : "";

    //  Smp
    var compbox = ifmdoc.querySelector("#compbox")
      ? ifmdoc.querySelector("#compbox").innerHTML.trim()
      : "";

    var centertd = ifmdoc.querySelector(".centertd")
      ? ifmdoc.querySelector(".centertd").innerHTML.trim()
      : "";
    var error = messagearea + compbox;
    alertContent = messagearea + textComplete + idNumber + compbox + centertd;

    if (alertContent.length !== 0) {
      var data = {
        message: `${alertContent}`,
        error: error != "",
      };
      window.parent.UnicornCartChatbot.handleResponse(data);
    }
    window.messageFlag == 1;
  }

  async function parse_lp_error(ifmdoc) {
    await _waitForElement(ifmdoc, "#alert_errors");
    var alert_errors = ifmdoc.querySelector("#alert_errors");

    if (alert_errors && alert_errors.style.display !== "none") {
      var textError = alert_errors.innerHTML.trim();

      if (textError) {
        var data = { message: `${textError}`, error: true };
        window.parent.UnicornCartChatbot.handleResponse(data);
      }
    }
  }
})();
