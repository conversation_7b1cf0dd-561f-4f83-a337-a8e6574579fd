(function () {
  window.productFlag = 0;
  window.postFlag = 0;
  window.messageFlag = 0;

  var existingIframe = document.getElementById("unicorncart_iframe");

  if (existingIframe) {
    existingIframe.parentNode.removeChild(existingIframe);
  }

  /**
   * Sets the value of an input element identified by the given selector.
   * Triggers the 'blur', 'change', and 'input' events on the element after setting the value.
   *
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the input element.
   * @param {string} val - The value to be set.
   * @returns {Promise<void>} A promise that resolves once the value is set and events are triggered.
   */
  var send_keys = (doc, selector, val) =>
    new Promise((resolve) => {
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          _target.value = val;
          ["blur", "change", "input"].forEach((e) => {
            _target.dispatchEvent(new Event(e, { bubbles: true }));
          });
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  /**
   * Sends the selected text to a specified element.
   *
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the target element.
   * @param {string} val - The value to be selected.
   * @returns {Promise<void>} - A promise that resolves when the text is sent.
   */
  var send_selected_text = (doc, selector, val) =>
    new Promise((resolve) => {
      if (!val) {
        return;
      }
      var _target = doc.querySelector(selector);
      if (_target) {
        setTimeout(() => {
          Array.from(_target.options).filter((x) =>
            x.textContent.includes(val)
          )[0].selected = true;
          _target.dispatchEvent(new Event("change", { bubbles: true }));
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });

  /**
   * Waits for an element to be present in the document and returns it.
   * @param {Document} doc - The document object.
   * @param {string} selector - The CSS selector of the element to wait for.
   * @param {number} [delay=300] - The delay in milliseconds between each search attempt.
   * @param {number} [tries=50] - The maximum number of search attempts.
   * @returns {Promise<Element|null>} - A promise that resolves with the element if found, or null if not found within the specified number of tries.
   */
  function _waitForElement(doc, selector, delay = 300, tries = 50) {
    const element = doc.querySelector(selector);

    if (!window[`__${selector}`]) {
      window[`__${selector}`] = 0;
    }

    function _search() {
      return new Promise((resolve) => {
        window[`__${selector}`]++;
        setTimeout(resolve, delay);
      });
    }

    if (element === null) {
      if (window[`__${selector}`] >= tries) {
        window[`__${selector}`] = 0;
        return Promise.resolve(null);
      }

      return _search().then(() => _waitForElement(doc, selector));
    } else {
      return Promise.resolve(element);
    }
  }

  var iframe = null;
  iframe = document.createElement("iframe");
  iframe.id = "unicorncart_iframe";
  iframe.src = window.parent.location.href;
  iframe.style.width = "900px";
  iframe.style.height = "600px";
  iframe.style.left = "0";
  iframe.style.position = "fixed";
  iframe.style.bottom = "0";
  iframe.style.borderRadius = "10px";
  iframe.style.margin = "10px";
  iframe.style.boxShadow = "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px";
  iframe.style.zIndex = "9999";
  document.body.appendChild(iframe);
  iframe.addEventListener("load", () => run());

  async function run() {
    var ifm = document.querySelector("iframe#unicorncart_iframe");
    var ifmdoc = ifm.contentWindow.document;
    var ifmPath = ifm.contentWindow.location.pathname;
    // try {
    if (window.productFlag === 0) {
      console.time("fill_data");
      await post();
      console.timeEnd("fill_data");
    }

    if (window.productFlag == 1 && window.postFlag == 0) {
      console.time("click_button_submit");
      if ((ifmPath = "/lp/confirm")) {
        await confirm_page();
      }
      console.timeEnd("click_button_submit");
    } else if (window.postFlag == 1 && window.messageFlag == 0) {
      console.time("thanks_page");
      if ((ifmPath = "/lp/finish")) {
        await thanks_page();
      }
      console.timeEnd("thanks_page");
    }
    // } catch (error) {
    //   console.error("Error parsing JSON:", error.message);
    // }
  }

  async function post() {
    var ifm = document.querySelector("body iframe#unicorncart_iframe");
    var ifmdoc = ifm.contentWindow.document;
    var ifmPath = ifm.contentWindow.location.pathname;

    console.log("post", ifmPath == "/lp");
    if (ifmPath == "/lp") {
      console.log("lp");
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // name
      send_keys(ifmdoc, "#order_name_family", `v.sei`); //v.sei mei
      send_keys(ifmdoc, "#order_name_first", `v.mei`);
      send_keys(ifmdoc, "#order_kana_family", `v.seifuri`); //v.seifuri v.meifuri
      send_keys(ifmdoc, "#order_kana_first", `v.meifuri`);

      console.log("full_name", `v.full_name`);
      console.log("full_name_kana", `v.full_name_kana`);
      // address
      send_keys(ifmdoc, "#order_address_postcode_0", `150`);
      send_keys(ifmdoc, "#order_address_postcode_1", `0031`);
      // var order_addr02 = `v.address02` + `v.address03`;
      send_keys(ifmdoc, "#order_address_1", "1-4マンション101号");
      // tel
      const tel = "v.tel";
      const len = tel.length;
      const tel1 = tel.slice(0, Math.floor(len / 3));
      const tel2 = tel.slice(Math.floor(len / 3), Math.floor((len * 2) / 3));
      const tel3 = tel.slice(Math.floor((len * 2) / 3));
      send_keys(ifmdoc, "#order_tel_0", tel1);
      send_keys(ifmdoc, "#order_tel_1", tel2);
      send_keys(ifmdoc, "#order_tel_2", tel3);

      // email
      send_keys(ifmdoc, "#order_email", `v.mail`);
      send_keys(ifmdoc, "#order_email_confirm", `v.mail`);

      send_keys(ifmdoc, "#order_password", "Aaaa1234");
      send_keys(ifmdoc, "#order_password_confirm", "Aaaa1234");
      // sex;
      send_selected_text(ifmdoc, "#order_sex", `女性`);

      // birthday
      send_keys(ifmdoc, "#order_birthday", "2012-12-02");
      // payment
      send_selected_text(ifmdoc, "#payment_method", `v.payment_method`);

      if (
        ["Paygentクレジットカード一括", "SBPS クレジットカード一括"].includes(
          "v.payment_method"
        )
      ) {
        var new_card = ifmdoc.querySelectorAll(".btn-outline-default")[
          ifmdoc.querySelectorAll(".btn-outline-default").length - 1
        ];
        new_card.click();

        await _waitForElement(ifmdoc, "#payment_card_number");

        var credit_data = Object.assign({}, window.creditCard);

        var credit_name = credit_data.card_name;
        var credit_number = credit_data.card_number;
        var credit_expiry_month = credit_data.card_expired_month;
        var credit_expiry_year = credit_data.card_expired_year;
        var credit_cvv = credit_data.card_cvv;
        var credit_brand = credit_data.card_brand;
        console.log("card_cvv", credit_cvv);
        console.log("card_brand", credit_brand);

        await send_keys(ifmdoc, "#payment_card_number", credit_number);
        await send_keys(
          ifmdoc,
          "#payment_card_valid_date",
          `${credit_expiry_month}/${credit_expiry_year}`
        );
        await send_keys(ifmdoc, "#payment_card_name", credit_name);

        await send_keys(ifmdoc, "#payment_card_cvc", 123);
      }

      if (!ifmdoc.getElementById("agree_check").checked) {
        ifmdoc.getElementById("agree_check").click();
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));
      ifmdoc.querySelector("#submit").click();
      window.productFlag = 1;
    }
  }

  async function confirm_page() {
    var ifm = document.querySelector("iframe#unicorncart_iframe");
    var ifmdoc = ifm.contentWindow.document;

    await _waitForElement(ifmdoc, "#submit_confirm");

    ifmdoc.getElementById("submit_confirm").click();
    window.postFlag = 1;
  }

  async function thanks_page() {
    var ifm = document.querySelector("iframe#unicorncart_iframe");
    var ifmdoc = ifm.contentWindow.document;
    alertContent = ifmdoc.querySelector(".order-number").innerHTML.trim();
    if (alertContent.length !== 0) {
      var data = {
        message: `${alertContent}`,
        error: true,
      };
      window.parent.UnicornCartChatbot.handleResponse(data);
    }
    window.messageFlag == 1;
  }
})();
