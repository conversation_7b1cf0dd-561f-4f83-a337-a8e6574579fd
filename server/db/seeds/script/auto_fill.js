async function run() {
  const sendKeys = (doc, selector, val) => {
    return new Promise((resolve) => {
      const target = doc.querySelector(selector);
      if (target) {
        setTimeout(() => {
          target.value = val;
          ["blur", "change", "input"].forEach((e) => {
            target.dispatchEvent(new Event(e, { bubbles: true }));
          });
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });
  };

  function waitForElement(doc, selector, delay = 300, tries = 50) {
    const search = () => new Promise((resolve) => setTimeout(resolve, delay));
    let count = window[`__${selector}`] || 0;

    const checkElement = () => {
      const element = doc.querySelector(selector);
      if (element !== null) {
        return Promise.resolve(element);
      }
      count++;
      if (count >= tries) {
        window[`__${selector}`] = 0;
        return Promise.resolve(null);
      }
      window[`__${selector}`] = count;
      return search().then(checkElement);
    };

    return checkElement();
  }

  const sendSelectedText = (doc, selector, val) => {
    return new Promise((resolve) => {
      if (!val) {
        resolve();
        return;
      }
      const target = doc.querySelector(selector);
      if (target) {
        setTimeout(() => {
          const option = Array.from(target.options).find((x) =>
            x.textContent.includes(val)
          );
          if (option) {
            option.selected = true;
            target.dispatchEvent(new Event("change", { bubbles: true }));
          }
          resolve();
        }, 100);
      } else {
        resolve();
      }
    });
  };

  const clickSubmitButton = () => {
    const buttons = document.querySelectorAll(".form-btn-submit");
    const lastButton = buttons[buttons.length - 1];
    if (lastButton) {
      lastButton.click();
    }
  };

  function set_gender_input(doc, selector, sex) {
    var divElement = doc.querySelector(selector);

    var inputs = divElement.getElementsByTagName("input");

    for (var i = 0; i < inputs.length; i++) {
      var label = doc.querySelector(`label[for="${inputs[i].id}"]`);

      if (label.textContent === sex) {
        inputs[i].click();
        break;
      }
    }
  }
  console.time("running");
  sendSelectedText(
    document,
    "#product",
    `初回300円OFF_IQDUM定期コース｜[初回1本]イクダムハンドクリーム2本`
  );

  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();
  await waitForElement(document, "#sei");
  await waitForElement(document, "#mei");
  await new Promise((resolve) => setTimeout(resolve, 100));

  await sendKeys(document, "#sei", `株式会社ゼクア`);
  await sendKeys(document, "#mei", `オーケー`);
  await sendKeys(document, "#seifuri", `オーケー`);
  await sendKeys(document, "#meifuri", `オーケー`);
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#mail");
  await new Promise((resolve) => setTimeout(resolve, 100));
  const expression = Math.floor(Math.random() * 100000);
  await sendKeys(document, "#mail", `vinhlq+${expression}@behemoth.vn`);
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#tel");
  await new Promise((resolve) => setTimeout(resolve, 100));
  await sendKeys(document, "#tel", `09012341234`);
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#zipcode");
  await new Promise((resolve) => setTimeout(resolve, 100));
  await sendKeys(document, "#zipcode", `1500031`);
  await sendKeys(document, "#address02", `1-123`);
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#sex");
  await new Promise((resolve) => setTimeout(resolve, 100));
  set_gender_input(document, "#sex", "女");
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#coupon");
  await new Promise((resolve) => setTimeout(resolve, 100));
  await sendKeys(document, "#coupon", `1-31`);
  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#payment_method");
  // await sendSelectedText(
  //   document,
  //   "#payment_method",
  //   `NP後払いwizRT(手数料:250円)`
  // );

  // AUTO FILL WITH CARD
  await sendSelectedText(
    document,
    "#payment_method",
    `クレジット決済(手数料:無料)`
  );

  await new Promise((resolve) => setTimeout(resolve, 100));
  clickSubmitButton();

  await waitForElement(document, "#card_number");

  await sendKeys(document, "#card_number", `****************`);
  await new Promise((resolve) => setTimeout(resolve, 100));

  await sendSelectedText(document, "#card_expired_month", `1`);
  await new Promise((resolve) => setTimeout(resolve, 100));

  await sendSelectedText(document, "#card_expired_year", `30`);
  await new Promise((resolve) => setTimeout(resolve, 100));

  await sendKeys(document, "#card_name", `HANAKO YAMADA`);
  await new Promise((resolve) => setTimeout(resolve, 100));

  clickSubmitButton();
}

run();
