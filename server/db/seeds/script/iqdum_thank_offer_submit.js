(function () {
  var iframe = document.getElementById("iqdum_iframe");

  if (!iframe) return;

  iframe.addEventListener("load", () => executeCvUpsell2());

  // Complete Page
  const executeCvUpsell1 = function () {
    var ifm = document.querySelector("#iqdum_iframe");
    var ifmDoc = ifm.contentWindow.document;
    var ifmLocation = ifm.contentWindow.location;

    console.log("upsell 1", ifmLocation);
    if (ifmLocation.origin != "https://iqdum.jp") return;

    const cvUpsell1 = "v.cv_upsell_1";
    const imgCvUpsell1 = ifmDoc.querySelector(`img[alt='${cvUpsell1}']`);

    if (!imgCvUpsell1) {
      console.log("Not Found Element Img has attribute alt= ", cvUpsell1);
      return;
    }

    console.log("cv upsell 1 click");
    imgCvUpsell1.parentElement.click();
  };

  executeCvUpsell1();

  // Thank Offer Page
  const executeCvUpsell2 = async function () {
    try {
      var ifm = document.querySelector("#iqdum_iframe");
      var ifmDoc = ifm.contentWindow.document;
      var ifmLocation = ifm.contentWindow.location;

      console.log("upsell 2", ifmLocation);
      if (ifmLocation.origin != "https://iqdum.jp") return;

      const cvUpsell2 = () => {
        const cvUpsell2 = "v.cv_upsell_2";
        const imgCvUpsell2 = ifmDoc.querySelector(`img[alt='${cvUpsell2}']`);

        if (!imgCvUpsell2) {
          console.log("Not Found Element Img has attribute alt= ", cvUpsell2);
          return;
        }

        console.log("cv upsell 2 click");
        imgCvUpsell2.parentElement.click();
      };

      const formWrapper = ifmDoc.querySelector("#formWrapper");
      const thxMessage = ifmDoc.querySelector("#thxMessage");

      if (formWrapper) {
        // plan 08, 01, 02
        console.log("formWrapper click");
        formWrapper.querySelector("button[type=submit]").click();

        if (thxMessage.querySelector(".offerWrap")) {
          await waitForElm(ifmDoc, "#thxMessage", { "style.display": "" });

          cvUpsell2();
        } else {
          console.log("finish");
          var tksText = ifmDoc.querySelector(".texWrapper p").textContent;

          window.parent.UnicornCartChatbot.handleResponse({
            message: tksText,
            error: false,
          });
        }
      } else {
        if (ifmDoc.querySelector(".thanksWrapper")) {
          cvUpsell2();
        } else {
          if (thxMessage && thxMessage.style.display != "none") {
            console.log("finish");
            var tksText = ifmDoc.querySelector(".texWrapper p").textContent;

            window.parent.UnicornCartChatbot.handleResponse({
              message: tksText,
              error: false,
            });
          }
        }
      }
    } catch (e) {
      console.log("error", e);

      window.parent.UnicornCartChatbot.handleResponse({
        message: `${e}`,
        error: true,
      });
    }

    // https://iqdum.jp/gold/thx_program/plan04/07.html?10248
    // flow 1: no -> plan04/07 -> no -> plan04/06 -> ok
    // flow 2: no -> plan04/07 -> yes -> plan04/08 -> yes -> plan04/08 ->ok
    // flow 3: yes -> plan04/01 -> yes -> plan04/01 -> no -> plan04/03 ->ok
    // flow 4: yes -> plan04/01 -> yes -> plan04/01 -> yes -> plan04/02 -> yes ->  plan04/02 -> ok

    // product ID=10
    // plan04/02 -> yess ->plan04/02 -> ok
    // plan04/03-> noo -> plan04/03 -> ok
  };

  //  Funtion Wait Element
  function waitForElm(doc, selector, attributes = {}) {
    const resolvePromise = () => {
      const element = doc.querySelector(selector);

      if (!element) return false;

      const keys = Object.keys(attributes);
      let keysLength = keys.length;

      for (let key in attributes) {
        const expectValue = attributes[key];
        let currentValue;

        key = key.split(".");

        for (let index = 0; index < key.length; index++) {
          currentValue =
            index == 0 ? element[key[index]] : currentValue[key[index]];
        }

        if (expectValue !== currentValue) break;

        keysLength--;
      }

      return keysLength == 0;
    };

    return new Promise((resolve) => {
      if (resolvePromise()) return resolve();

      const observer = new MutationObserver((_mutations) => {
        if (!resolvePromise()) return;

        observer.disconnect();
        resolve();
      });

      observer.observe(doc.body, {
        attributes: true,
        childList: true,
        subtree: true,
      });
    });
  }
})();
