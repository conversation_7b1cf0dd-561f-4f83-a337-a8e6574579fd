create_table 'scenario_settings', id: :uuid, default: 'uuid_generate_v4()', force: :cascade do |t|
  t.string :name, null: false
  t.string :description, null: false
  t.boolean :active, default: false
  t.integer :setting_type, default: 1

  t.jsonb :general_settings
  t.jsonb :theme_customize_settings

  t.jsonb :css_customize
  t.jsonb :javascript_customize

  t.string :scenario_id, null: false

  t.timestamps

  t.integer :lock_version, null: false

  t.index [:scenario_id]
end
