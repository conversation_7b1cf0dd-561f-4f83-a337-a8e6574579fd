create_table 'scenarios', id: :uuid, default: 'uuid_generate_v4()', force: :cascade do |t|
  t.string :name, null: false
  t.string :description
  t.string :root_node_uid
  t.boolean :active, default: false
  t.boolean :support_ui_enable, default: false
  t.boolean :progress_bar_enable, default: false
  t.string :match_value, null: false
  t.integer :match_type, default: 1, null: false
  t.string :user_id, null: false
  
  t.jsonb :data_json

  t.timestamps

  t.integer :lock_version, null: false
  t.index [:user_id]
end
