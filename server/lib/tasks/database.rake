

namespace :db do
  task custom_reset: :environment do
    Rake::Task['db:drop'].invoke
    Rake::Task['db:create'].invoke
    Rake::Task['ridge:apply'].invoke
    Rake::Task['db:seed'].invoke
  end

  desc 'Run specific seed file'
  task seeds: :environment do 
    file = ENV['SCENARIO_NAME']
    seed_file = Rails.root.join('db', 'seeds', "#{file}.rb")

    if File.exist?(seed_file)
      puts "Seeding #{file}..."
      load(seed_file)
    else
      puts "Seed file not found: #{seed_file}"
    end
  end
end
