FROM ruby:3.2.2

ENV BUNDLE_APP_CONFIG="/app/.bundle"
ENV RAILS_LOG_TO_STDOUT=true
ENV RAILS_SERVE_STATIC_FILES=true

WORKDIR /app

# SSL通信でCaused by OpenSSL::SSL::SSLError: SSL_connect returned=1 errno=0 state=error: wrong signature type が出たらOpenSSLの設定を変える
RUN sed -i 's/DEFAULT@SECLEVEL=2/DEFAULT@SECLEVEL=1/' /etc/ssl/openssl.cnf
RUN apt-get update && apt-get install -y mecab libmecab-dev mecab-ipadic-utf8

COPY ./server/Gemfile ./server/Gemfile.lock ./
RUN gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)" \
    && bundle config set without development test \
    && bundle config set --local path 'vendor/bundle' \
    && bundle install \
    && rm -rf $GEM_HOME/cache/*.gem \
    && find $GEM_HOME/gems/ -name "*.c" -delete \
    && find $GEM_HOME/gems/ -name "*.o" -delete

COPY ./server ./
COPY ./docker/server/entrypoint.sh ./

CMD [ "/bin/bash", "-c", "/app/entrypoint.sh" ]
