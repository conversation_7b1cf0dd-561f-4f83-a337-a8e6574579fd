FROM ruby:3.2.2

ENV BUNDLE_APP_CONFIG="/app/.bundle"
ENV RAILS_LOG_TO_STDOUT=true
ENV RAILS_SERVE_STATIC_FILES=true

WORKDIR /app

# SSL通信でCaused by OpenSSL::SSL::SSLError: SSL_connect returned=1 errno=0 state=error: wrong signature type が出たらOpenSSLの設定を変える
RUN sed -i 's/DEFAULT@SECLEVEL=2/DEFAULT@SECLEVEL=1/' /etc/ssl/openssl.cnf

RUN apt-get update \
    && apt-get install -y \
    bash \
    build-essential \
    libcurl4-gnutls-dev \
    git \
    libmariadb-dev \
    libyaml-dev \
    zlib1g-dev \
    tzdata \
    libxslt-dev \
    libxml2-dev \
    libimlib2 \
    libimlib2-dev \
    libexif12 \
    libexif-dev \
    curl \
    wget \
    unzip \
    bc \
    xvfb \
    libjpeg62-turbo \
    libxrender1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update \
    && apt-get install -y libnss3 libgconf-2-4 libxi6 libx11-xcb1 libxcomposite1 libxcursor1 libxdamage1 libxtst6 libpangocairo-1.0-0 libxrandr2

# Install Chromium and Chromium-Driver
RUN apt-get update && apt-get install -y chromium chromium-driver && apt-get clean && rm -rf /var/lib/apt/lists/*
RUN mkdir -p /root/.webdrivers && \
    ln -nfs /usr/bin/chromedriver /root/.webdrivers/chromedriver && \
    /usr/bin/chromedriver --version | cut -d ' ' -f 2 | cat > /root/.webdrivers/chromedriver.version
RUN mkdir -p "/root/.config/chromium/Crash Reports/pending/"

COPY ./task_runner/Gemfile ./task_runner/Gemfile.lock ./
RUN gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)" \
    && bundle config set without development test \
    && bundle config set --local path 'vendor/bundle' \
    && bundle install \
    && rm -rf $GEM_HOME/cache/*.gem \
    && find $GEM_HOME/gems/ -name "*.c" -delete \
    && find $GEM_HOME/gems/ -name "*.o" -delete

COPY ./task_runner ./

CMD [ "bundle", "exec", "rails", "s" ]
