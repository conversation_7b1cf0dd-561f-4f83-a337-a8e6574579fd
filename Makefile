# ------------ Docker Build & Deploy -----------------
REGISTRY=us-west1-docker.pkg.dev/ec-united-cart/docker
NAMESPACE=chat-bot
COMMIT_SHA=$(shell git rev-parse HEAD)

# Contexts for different environments
STG_CONTEXT=gke_ec-united-cart_us-west1-c_cart-stg
PROD_CONTEXT=gke_ec-united-cart_us-west1_cart-prod

.PHONY: docker-build docker-push k9s-release release-stg-server release-stg-anycable-rpc release-stg-runner release-prod-server release-prod-anycable-rpc release-prod-runner

docker-build:
	docker build --platform=linux/amd64 --rm -f docker/$(COMPONENT)/Dockerfile -t $(IMAGE_NAME):latest .
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):latest
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

docker-push:
	docker push $(REGISTRY)/$(IMAGE_NAME):latest
	docker push $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

k9s-release:
	kubectl --context $(CONTEXT) set image deployment -n $(NAMESPACE) $(DEPLOYMENT_NAME) $(DEPLOYMENT_CONTAINER)=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

# Staging Releases
release-stg-server:
	$(MAKE) docker-build docker-push k9s-release COMPONENT=server IMAGE_NAME=united-cart-chatbot/server DEPLOYMENT_NAME=bot-server DEPLOYMENT_CONTAINER=server CONTEXT=$(STG_CONTEXT)

release-stg-anycable-rpc:
	$(MAKE) docker-build docker-push k9s-release COMPONENT=server IMAGE_NAME=united-cart-chatbot/server DEPLOYMENT_NAME=anycable-rpc DEPLOYMENT_CONTAINER=anycable-rpc CONTEXT=$(STG_CONTEXT)

# Production Releases
release-prod-server:
	$(MAKE) docker-build docker-push k9s-release COMPONENT=server IMAGE_NAME=united-cart-chatbot/server DEPLOYMENT_NAME=bot-server DEPLOYMENT_CONTAINER=server CONTEXT=$(PROD_CONTEXT)

release-prod-anycable-rpc:
	$(MAKE) docker-build docker-push k9s-release COMPONENT=server IMAGE_NAME=united-cart-chatbot/server DEPLOYMENT_NAME=anycable-rpc DEPLOYMENT_CONTAINER=anycable-rpc CONTEXT=$(PROD_CONTEXT)
