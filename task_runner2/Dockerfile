FROM node:22.14.0-slim

WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
  fonts-ipafont-gothic \
  fonts-wqy-zenhei \
  fonts-thai-tlwg \
  fonts-khmeros \
  fonts-kacst \
  fonts-freefont-ttf \
  dbus dbus-x11 \
  libnss3-dev \
  libgdk-pixbuf2.0-dev \
  libgtk-3-dev \
  libxss-dev \
  libasound2

COPY . /app/
RUN npm install && npx puppeteer browsers install chrome

CMD ["npm", "run", "start"]
