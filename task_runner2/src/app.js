import dotenv from "dotenv";
import express from "express";
import { format } from "date-fns";
import taskRoutes from "./controllers/taskController.js";
import { logger } from "./utils/logger.js";
import { requestIdMiddleware } from "./middleware/index.js";
import { requestContextMiddleware } from "./utils/asyncContext.js";
import { AppError, NotFoundError } from "./errors/index.js";
import helmet from "helmet";
import cors from "cors";

if (process.env.APP_ENV !== "production") {
  dotenv.config();
}

const app = express();
const port = process.env.APP_PORT || 3000;

app.use(helmet());
app.use(cors());
app.use(express.json({ limit: "10kb" }));

app.use(requestIdMiddleware);
app.use(requestContextMiddleware);

// Custom middleware for Rails-like request logging with request ID
app.use((req, res, next) => {
  const timestamp = format(new Date(), "yyyy-MM-dd HH:mm:ss");
  const start = Date.now();

  const clientIp = req.ip.replace(/^::ffff:/, "");

  logger.info(
    `Started ${req.method} "${req.url}" for ${clientIp} at ${timestamp}`
  );

  res.on("finish", () => {
    const { statusCode } = res;

    if (statusCode === 200) {
      logger.info(`Completed ${statusCode} in ${Date.now() - start} ms`);
    }
  });

  next();
});

// Routes
app.get("/", (req, res) => {
  res.status(200).send();
});

// Health check endpoint
app.get("/healthz", (req, res) => {
  const healthcheck = {
    uptime: process.uptime(),
    message: "OK",
    timestamp: Date.now(),
    environment: process.env.APP_ENV || "development",
  };
  res.status(200).json(healthcheck);
});

app.use("/api/v1/tasks", taskRoutes);

// Handle 404 - Route not found
app.use((req, res, next) => {
  next(new NotFoundError(`Can't find ${req.originalUrl} on this server!`));
});

// Centralized Error Handling Middleware
app.use((err, req, res, next) => {
  let statusCode = err.statusCode || 500;
  let status = err.status || "error";
  let message = err.message || "Something went very wrong!";
  const isOperational = err.isOperational || false;

  logger.error(err.message, {
    err,
    stack: err.stack,
    statusCode,
    originalUrl: req.originalUrl,
    method: req.method,
    ip: req.ip,
    isOperational,
  });

  if (err.name === "ValidationError" && err.isJoi) {
    statusCode = 400;
    status = "fail";
    message = err.details.map((el) => el.message).join(". ");
  } else if (err instanceof AppError) {
    statusCode = err.statusCode;
    status = err.status;
    message = err.message;
  } else if (!isOperational) {
    statusCode = err.statusCode || 500;
    message = err.message || "An unexpected error occurred.";
  }

  res.status(statusCode).json({
    status,
    message,
    ...{
      errorDetails: { name: err.name, message: err.message },
      stack: err.stack,
    },
  });
});

// Start the server
const server = app.listen(port, () => {
  logger.info(`Server running on port ${port}`);
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  logger.error("UNHANDLED REJECTION! Shutting down...", { err });
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  logger.error("UNCAUGHT EXCEPTION! Shutting down...", { err });
  process.exit(1);
});

export default app;
