import express from "express";
const router = express.Router();
import TaskRunner from "../tasks/runner/index.js";
import { logger } from "../utils/logger.js";
import TaskCrawler from "../tasks/crawlers/index.js";

router.post("/execute", async (req, res) => {
  const { class_name, inputs, outputs } = req.body;

  if (!class_name || !inputs || !outputs) {
    logger.error("Missing required fields class_name , inputs and outputs");
    return res.json({
      data: {
        message:
          "Missing required fields: class_name and inputs and outputs are mandatory.",
        error: true,
      },
    });
  }

  try {
    const parsedInputs = parseInputs(inputs);
    if (!parsedInputs) {
      logger.error("Input parse error", { inputs });
      return res.json({
        data: {
          message: "Invalid inputs format",
          error: true,
        },
      });
    }

    const ScenarioClass = TaskRunner[class_name];

    if (!ScenarioClass) {
      logger.error("Sc<PERSON>rio not found" + { class_name });
      return res.json({
        data: {
          message: `Scenario class not found: ${class_name}`,
          error: true,
        },
      });
    }

    const scenarioInstance = new ScenarioClass(parsedInputs);
    const result = await scenarioInstance.execute();

    return res.json(result);
  } catch (error) {
    logger.error("Scenario execution error" + error);
    return res.json({
      data: {
        message: "予期しないエラーが発生しました。後でもう一度お試しください。",
        error: true,
      },
    });
  }
});

router.post("/crawlers", async (req, res) => {
  const { class_name, inputs, outputs } = req.body;

  if (!class_name || !inputs || !outputs) {
    logger.error("Missing required fields class_name , inputs and outputs");
    return res.json({
      data: {
        message:
          "Missing required fields: class_name and inputs and outputs are mandatory.",
        error: true,
        optionValues: [],
      },
    });
  }

  try {
    const parsedInputs = parseInputs(inputs);
    if (!parsedInputs) {
      logger.error("Input parse error", { inputs });
      return res.json({
        data: {
          message: "Invalid inputs format",
          error: true,
          optionValues: [],
        },
      });
    }

    const CrawlerClass = TaskCrawler[class_name];

    if (!CrawlerClass) {
      logger.error("Crawler not found", { class_name });
      return res.json({
        data: {
          message: `Crawler class not found: ${class_name}`,
          error: true,
          optionValues: [],
        },
      });
    }

    const crawlerInstance = new CrawlerClass(parsedInputs);
    const result = await crawlerInstance.execute();

    return res.json(result);
  } catch (error) {
    logger.error("Crawler execution error" + error);
    return res.json({
      data: {
        error: true,
        message: "予期しないエラーが発生しました。後でもう一度お試しください。",
        optionValues: [],
      },
    });
  }
});

const parseInputs = (inputString) => {
  try {
    const parsed = JSON.parse(inputString);
    return parsed;
  } catch (error) {
    return null;
  }
};

export default router;
