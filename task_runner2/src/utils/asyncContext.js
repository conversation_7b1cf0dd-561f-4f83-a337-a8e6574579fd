import { AsyncLocalStorage } from "async_hooks";

export const asyncLocalStorage = new AsyncLocalStorage();

export const runWithContext = (store, callback) => {
  return asyncLocalStorage.run(store, callback);
};

export const requestContextMiddleware = (req, res, next) => {
  const store = { requestId: req.requestId };
  runWithContext(store, next);
};

export const getContext = () => {
  return asyncLocalStorage.getStore();
};
