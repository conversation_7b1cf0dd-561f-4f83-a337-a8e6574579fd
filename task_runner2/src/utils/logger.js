import winston from "winston";
import { getContext } from "./asyncContext.js";

const logFormat = winston.format.combine(
  winston.format.splat(),
  winston.format.timestamp({
    format: "YYYY-MM-DD HH:mm:ss",
  }),
  winston.format.colorize(),
  winston.format.printf((log) => {
    if (log.message && log.message.includes(".well-known")) {
      return false;
    }
    const requestId = log.requestId ? `[${log.requestId}] ` : "";
    if (log.stack)
      return `[${log.timestamp}] [${log.level}] ${requestId}${log.stack}`;
    return `[${log.timestamp}] [${log.level}] ${requestId}${log.message}`;
  })
);

export const logger = winston.createLogger({
  level: "info",
  format: logFormat,
  transports: [new winston.transports.Console()],
});

const originalInfo = logger.info;
const originalError = logger.error;
const originalWarn = logger.warn;
const originalDebug = logger.debug;

logger.info = (message, meta = {}) => {
  const store = getContext();
  const requestId = store?.requestId;
  return originalInfo(message, { ...meta, requestId });
};

logger.error = (message, meta = {}) => {
  const store = getContext();
  const requestId = store?.requestId;
  return originalError(message, { ...meta, requestId });
};

logger.warn = (message, meta = {}) => {
  const store = getContext();
  const requestId = store?.requestId;
  return originalWarn(message, { ...meta, requestId });
};

logger.debug = (message, meta = {}) => {
  const store = getContext();
  const requestId = store?.requestId;
  return originalDebug(message, { ...meta, requestId });
};
