import { logger } from "../../utils/logger.js";
import BaseSubmit from "./BaseSubmit.js";

class HawaiiWaterDesiredInstallationDate extends BaseSubmit {
  constructor(inputs) {
    super(inputs);
  }

  async execute() {
    await this.initBrowser();

    const startRun = Date.now();
    const result = await this.crawl();
    logger.info(`Time run: ${(Date.now() - startRun) / 1000} seconds`);
    return result;
  }

  async crawl() {
    try {
      logger.info("Step 1: Clicking Add Address button");
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const isFirstAddress =
        this.inputs.addressee === "東京・神奈川・千葉・埼玉・茨城";

      if (isFirstAddress) {
        await this.page.evaluate(() => {
          const addresseeRadioButtons = document.querySelectorAll(
            "label.input-radio-type-01"
          );
          addresseeRadioButtons[0].click();
        });
      } else {
        await this.page.evaluate(() => {
          const addresseeRadioButtons = document.querySelectorAll(
            "label.input-radio-type-01"
          );
          addresseeRadioButtons[1].click();
        });
      }
      // Step 2: Fill zip
      logger.info("Step 2: Filling zip");
      if (!(await this.waitForElement("input#zip"))) {
        return this.createErrorResponse("Element not found");
      }

      await this.withRetry(async () => {
        // Fill personal information
        await this.fillField("input#zip", this.inputs.zipcode, "zip");
      });

      // // Step 3: Click submit button
      logger.info("Step 3: Clicking submit button");

      if (
        !(await this.waitForElement("#submit-button-container", {
          visible: true,
          timeout: 5000,
        }))
      ) {
        return this.createErrorResponse("Element not found");
      }

      this.page.evaluate(() => {
        document.querySelector(".btn-submit").click();
      });

      logger.info("Step 4: Selecting desired installation date");

      if (
        !(await this.waitForElement(
          'select[name="desired_installation_date"]',
          {
            visible: true,
            timeout: 3000,
          }
        ))
      ) {
        const optionValues = await this.page.evaluate(() => {
          const select = document.querySelector(
            'select[name="desired_shipping_date"]'
          );
          return select
            ? Array.from(select.options)
                .slice(1)
                .map((opt) => opt.text)
            : [];
        });

        return this.createSuccessResponse(optionValues);
      } else {
        const optionValues = await this.page.evaluate(() => {
          const select = document.querySelector(
            'select[name="desired_installation_date"]'
          );
          return select
            ? Array.from(select.options)
                .slice(1)
                .map((opt) => opt.text)
            : [];
        });

        return this.createSuccessResponse(optionValues);
      }
    } catch (error) {
      logger.error(
        "Error occurred during Genpeiseiyaku submission:" + error.message
      );
      return this.createErrorResponse(error.message);
    } finally {
      await this.cleanup();
    }
  }
}

export default HawaiiWaterDesiredInstallationDate;
