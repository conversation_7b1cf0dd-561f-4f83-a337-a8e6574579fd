import puppeteer from "puppeteer";
import { logger } from "../../utils/logger.js";
import { Storage } from "@google-cloud/storage";

/**
 * Base class for handling web submission tasks using Puppeteer.
 * @param {Object} inputs - Input object containing key-value pairs for configuration.
 * @throws {Error} If required input 'url' is missing.
 */
class BaseSubmit {
  constructor(inputs) {
    this.inputs = Object.fromEntries(
      Object.entries(inputs).map(([key, { value }]) => [key, value])
    );

    if (!this.inputs.url) {
      const errorMsg = "Missing required input: url";
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    this.browser = null;
    this.page = null;

    this.storage = new Storage({
      keyFilename: process.env.GCS_KEY_FILE,
      projectId: process.env.GCS_PROJECT_ID,
    });

    this.bucketName = process.env.GCS_BUCKET_NAME;
  }

  /**
   * Initializes Puppeteer browser and navigates to the specified URL.
   * @throws {Error} If browser initialization or page navigation fails.
   */

  async initBrowser() {
    const headless = !(process.env.CRAWLER_DEBUGGING === "true");
    try {
      const startInitBrowser = Date.now();
      this.browser = await puppeteer.launch({
        headless,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-gpu",
        ],
      });
      logger.info(
        `Browser initialized in ${
          (Date.now() - startInitBrowser) / 1000
        } seconds`
      );

      const startInitPage = Date.now();
      this.page = await this.browser.newPage();
      await this.page.setUserAgent(
        this.inputs.userAgent ||
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
      );
      await this.page.goto(this.inputs.url, {
        waitUntil: "domcontentloaded",
      });
      logger.info(
        `Page loaded in ${(Date.now() - startInitPage) / 1000} seconds`
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Cleans up browser and page resources.
   * @throws {Error} If screenshot capture or upload fails.
   */
  async cleanup() {
    try {
      // this.screenshotUrl = await this.takeAndUploadScreenshot();

      // if (this.screenshotUrl) {
      //   logger.info(
      //     "No local file saved; screenshot uploaded directly to bucket."
      //   );
      // }

      if (this.page) await this.page.close();
      if (this.browser) await this.browser.close();
    } catch (error) {
      logger.error("Error during cleanup:", error.message);
    }
  }

  /**
   * Waits for navigation to complete.
   * @throws {Error} If navigation fails.
   */
  async waitForNavigation() {
    await this.page.waitForNavigation({
      waitUntil: "networkidle0",
    });
  }

  /**
   * Retry wrapper for flaky operations
   */
  async withRetry(fn, maxAttempts = 10, delayMs = 500) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        if (attempt === maxAttempts) throw error;
        logger.warn(`Retry ${attempt}/${maxAttempts} failed: ${error.message}`);
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
  }

  /**
   * Click button with error handling - common utility
   */
  async clickButton(selector, description = "button") {
    const button = await this.page.$(selector);
    if (!button) {
      logger.warn(`${description} not found: ${selector}`);
      return false;
    }
    await button.click();
    logger.info(`Clicked ${description}: ${selector}`);
    return true;
  }

  /**
   * Fill form field with error handling - common utility
   */
  async fillField(selector, value, description = "field") {
    if (!value) return false;
    await this.page.type(selector, value);
    logger.info(`Filled ${description}: ${selector}`);
    return true;
  }

  /**
   * Wait for selector with timeout - common utility
   */
  async waitForElement(selector, options = { visible: true, timeout: 5000 }) {
    try {
      await this.page.waitForSelector(selector, options);
      return true;
    } catch (error) {
      logger.warn(`Element not found: ${selector}`);
      return false;
    }
  }

  /**
   * Wait for navigation with timeout - common utility
   */
  async waitForPageNavigation(
    options = { waitUntil: "domcontentloaded", timeout: 15000 }
  ) {
    try {
      await this.page.waitForNavigation(options);
      return true;
    } catch (error) {
      logger.warn("Navigation timeout or failed");
      return false;
    }
  }

  /**
   * Get error messages from page - common utility
   */
  async getErrorMessages(errorSelector = ".formError") {
    try {
      await this.page.waitForSelector(errorSelector, { timeout: 3000 });
      return await this.page.$$eval(errorSelector, (errors) => {
        return errors.map((error) => error.textContent.trim()).join("\n");
      });
    } catch (error) {
      return "";
    }
  }

  /**
   * Create standard error response
   */
  createErrorResponse(message) {
    return {
      data: {
        error: true,
        message: message,
      },
    };
  }

  /**
   * Create standard success response
   */
  createSuccessResponse(message) {
    return {
      data: {
        error: false,
        message: message,
      },
    };
  }

  /**
   * Select dropdown option - common utility
   */
  async selectOption(selector, value, description = "dropdown") {
    try {
      await this.page.select(selector, value);
      logger.info(`Selected ${description}: ${selector} = ${value}`);
      return true;
    } catch (error) {
      logger.warn(`Failed to select ${description}: ${selector}`);
      return false;
    }
  }

  /**
   * Click element by evaluation - common utility
   */
  async clickByEvaluation(evaluationFn, description = "element") {
    try {
      await this.page.evaluate(evaluationFn);
      logger.info(`Clicked ${description} by evaluation`);
      return true;
    } catch (error) {
      logger.warn(`Failed to click ${description} by evaluation`);
      return false;
    }
  }

  /**
   * Get element value - common utility
   */
  async getElementValue(selector) {
    try {
      return await this.page.$eval(selector, (el) => el.value);
    } catch (error) {
      return null;
    }
  }
  async takeAndUploadScreenshot() {
    try {
      if (!this.page) {
        logger.warn("Page is not available for screenshot.");
        return null;
      }

      const dateNow = new Date();
      const formattedDate = dateNow
        .toISOString()
        .slice(0, 19)
        .replace("T", "_")
        .replace(/:/g, "-");

      if (process.env.APP_ENV !== "production") {
        this.page.screenshot({
          path: `./src/screenshots/${formattedDate}_submission.png`,
        });
        return;
      }

      const destinationPath = `staging/${formattedDate}_submission.png`;

      logger.info("Taking screenshot...");
      const screenshotBuffer = await this.page.screenshot({ type: "png" });

      logger.info(
        `Uploading screenshot to bucket: ${this.bucketName}/${destinationPath}`
      );
      const bucket = this.storage.bucket(this.bucketName);
      const file = bucket.file(destinationPath);

      logger.info("Uploading screenshot to bucket...");
      await file.save(screenshotBuffer, {
        metadata: {
          contentType: "image/png",
        },
        resumable: false,
      });
    } catch (err) {
      logger.error("Failed to take or upload screenshot:" + err.message);
      return null;
    }
  }
}

export default BaseSubmit;
