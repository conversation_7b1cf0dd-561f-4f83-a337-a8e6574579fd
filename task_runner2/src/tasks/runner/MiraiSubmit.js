import { logger } from "../../utils/logger.js";
import BaseSubmit from "./BaseSubmit.js";

class MiraiSubmit extends BaseSubmit {
  constructor(inputs) {
    super(inputs);
  }

  async execute() {
    await this.initBrowser();

    const startRun = Date.now();
    const result = await this.run();
    logger.info(`Time run: ${(Date.now() - startRun) / 1000} seconds`);
    return result;
  }

  async run() {
    try {
      logger.info("Starting Mirai submission process...");

      // Step 1: Click Add to Cart button
      logger.info("Step 1: Clicking Add to Cart button");
      const startClickButton1 = Date.now();
      await this.withRetry(async () => {
        if (
          !(await this.clickButton(".p-cta__bodyBtn_a", "Add to Cart button"))
        ) {
          throw new Error("Add to Cart button not found");
        }
        await this.waitForPageNavigation({
          waitUntil: "domcontentloaded",
          timeout: 15000,
        });
      });
      logger.info(
        `Time clicking Add to Cart button: ${
          (Date.now() - startClickButton1) / 1000
        } seconds`
      );

      // Step 2: Click Checkout button
      logger.info("Step 2: Clicking Checkout button");
      const startClickButton2 = Date.now();
      await this.withRetry(async () => {
        if (!(await this.clickButton(".btn-checkout", "Checkout button"))) {
          throw new Error("Checkout button not found");
        }
        await this.waitForPageNavigation({
          waitUntil: "networkidle0",
          timeout: 15000,
        });
      });
      logger.info(
        `Time clicking Checkout button: ${
          (Date.now() - startClickButton2) / 1000
        } seconds`
      );

      // Step 3: Fill customer information form
      logger.info("Step 3: Filling customer information form");
      const startFillForm = Date.now();
      await this.withRetry(async () => {
        // Fill personal information
        await this.fillField(".name1", this.inputs.sei, "Family Name");
        await this.fillField(".name2", this.inputs.mei, "First Name");
        await this.fillField(".kana1", this.inputs.seifuri, "Family Name Kana");
        await this.fillField(".kana2", this.inputs.meifuri, "First Name Kana");

        // Fill postal code and address
        await this.fillField(".postalCode", this.inputs.zipcode, "Postal Code");
        await this.page.click(".address3");
        await this.page.keyboard.type(this.inputs.address02, { delay: 10 });

        // Fill phone number
        await this.fillField(".phoneNumber", this.inputs.tel, "Phone Number");

        // Fill email
        await this.page.click(".loginid");
        await this.page.keyboard.type(this.inputs.mail, { delay: 10 });

        // Verify and refill if necessary
        const addressValue = await this.getElementValue(".address3");
        const postalCodeValue = await this.getElementValue(".postalCode");
        const phoneNumberValue = await this.getElementValue(".phoneNumber");

        if (!postalCodeValue) {
          await this.fillField(
            ".postalCode",
            this.inputs.zipcode,
            "Postal Code (retry)"
          );
        }
        if (!phoneNumberValue) {
          await this.fillField(
            ".phoneNumber",
            this.inputs.tel,
            "Phone Number (retry)"
          );
        }
        if (!addressValue) {
          await this.fillField(
            ".address3",
            this.inputs.address02,
            "Address (retry)"
          );
        }
      });
      logger.info(
        `Form filled in ${(Date.now() - startFillForm) / 1000} seconds`
      );

      // Step 4: Select payment method
      logger.info("Step 4: Selecting payment method");
      await this.withRetry(async () => {
        await this.waitForElement(".payment-method-name", { timeout: 3000 });
        const paymentSelected = await this.clickByEvaluation(
          (paymentMethod) => {
            const paymentLabels = Array.from(
              document.querySelectorAll(".payment-method-name label")
            );
            const targetLabel = paymentLabels.find((label) =>
              label.textContent.includes(paymentMethod)
            );
            if (targetLabel) {
              targetLabel.click();
              return true;
            }
            return false;
          },
          this.inputs.payment_method,
          "Payment method"
        );

        if (!paymentSelected) {
          throw new Error(
            `Payment method not found: ${this.inputs.payment_method}`
          );
        }
      });

      // Step 5: Fill credit card information if needed
      if (
        this.inputs.payment_method === "クレジットカード（手数料0円）" &&
        this.inputs.credit_card
      ) {
        logger.info("Step 5: Filling credit card information");
        await this.withRetry(async () => {
          const creditCardData = JSON.parse(
            Buffer.from(this.inputs.credit_card, "base64").toString("utf-8")
          );

          await this.fillField(
            "input[name='zeus_token_card_number']",
            creditCardData.card_number,
            "Card Number"
          );
          await this.selectOption(
            "#zeus_token_card_expires_month",
            creditCardData.card_expired_month,
            "Expiry Month"
          );
          await this.selectOption(
            "#zeus_token_card_expires_year",
            `20${creditCardData.card_expired_year}`,
            "Expiry Year"
          );
        });
      }

      // Step 6: Submit the form
      logger.info("Step 6: Submitting the form");
      await this.withRetry(async () => {
        await this.waitForElement("button.btn-submit", { timeout: 3000 });
        const submitted = await this.clickByEvaluation(() => {
          const buttons = document.querySelectorAll("button.btn-submit");
          if (buttons.length > 0) {
            const lastButton = buttons[buttons.length - 1];
            lastButton.click();
            return true;
          }
          return false;
        }, "Submit button");

        if (!submitted) {
          throw new Error("Submit button not found");
        }
      });

      // Step 7: Check for form validation errors
      logger.info("Step 7: Checking for form validation errors");
      const startCheckError = Date.now();

      // Check for .nmessage-default errors
      let errorMessages = await this.getErrorMessages(".nmessage-default");
      if (errorMessages) {
        logger.error(
          "Form validation errors (.nmessage-default):",
          errorMessages
        );
        return this.createErrorResponse(errorMessages);
      }

      // Check for #error-msg errors
      try {
        await this.page.waitForSelector("#error-msg", { timeout: 1000 });
        const errorAlert = await this.page.evaluate(() =>
          document.querySelector("#error-msg")?.textContent?.trim()
        );
        if (errorAlert) {
          logger.error("Form validation errors (#error-msg):", errorAlert);
          return this.createErrorResponse(errorAlert);
        }
      } catch (error) {
        // No error found, continue
      }

      logger.info(
        `Error check completed in ${
          (Date.now() - startCheckError) / 1000
        } seconds`
      );

      // Step 8: Final confirmation if available
      logger.info("Step 8: Final confirmation");
      await this.withRetry(async () => {
        try {
          await this.waitForElement("button[type='submit']", { timeout: 5000 });
          await this.clickButton(
            "button[type='submit']",
            "Final confirmation button"
          );
        } catch (error) {
          logger.info(
            "No final confirmation button found or already submitted"
          );
        }
      });

      // Return success result
      logger.info("Mirai submission completed successfully");
      return this.createSuccessResponse("ご注文が正常に完了しました。");
    } catch (error) {
      logger.error("Error occurred during Mirai submission:", error.message);
      return this.createErrorResponse(
        "予期しないエラーが発生しました。後でもう一度お試しください。"
      );
    } finally {
      await this.cleanup();
    }
  }
}

export default MiraiSubmit;
