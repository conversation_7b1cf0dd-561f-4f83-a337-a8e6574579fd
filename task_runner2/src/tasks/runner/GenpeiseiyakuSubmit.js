import { logger } from "../../utils/logger.js";
import BaseSubmit from "./BaseSubmit.js";

class GenpeiseiyakuSubmit extends BaseSubmit {
  constructor(inputs) {
    super(inputs);
  }

  async execute() {
    await this.initBrowser();

    const startRun = Date.now();
    const result = await this.run();
    logger.info(`Time run: ${(Date.now() - startRun) / 1000} seconds`);
    return result;
  }

  async run() {
    try {
      logger.info("Starting Genpeiseiyaku submission process...");

      // Step 1: Click "Add Product" button
      logger.info("Step 1: Clicking Add Product button");
      await this.withRetry(async () => {
        if (!(await this.clickButton(".sb-custom a", "Add Product button"))) {
          throw new Error("Add Product button not found");
        }
      });

      // Step 2: Click "Checkout" button
      logger.info("Step 2: Clicking Checkout button");
      await this.withRetry(async () => {
        await this.waitForElement("#cart_submit input");
        if (
          !(await this.clickButton("#cart_submit input", "Checkout button"))
        ) {
          throw new Error("Checkout button not found");
        }
      });

      // Step 3: Fill shipping information form
      logger.info("Step 3: Filling shipping information form");
      await this.withRetry(async () => {
        // Fill personal information
        await this.fillField(
          "#shipping_address_family_name",
          this.inputs.sei,
          "Family Name"
        );
        await this.fillField(
          "#shipping_address_first_name",
          this.inputs.mei,
          "First Name"
        );
        await this.fillField(
          "#shipping_address_family_name_kana",
          this.inputs.seifuri,
          "Family Name Kana"
        );
        await this.fillField(
          "#shipping_address_first_name_kana",
          this.inputs.meifuri,
          "First Name Kana"
        );

        // Fill postal code and address
        await this.page.click("#shipping_address_zip");
        await this.fillField(
          "#shipping_address_zip",
          this.inputs.zipcode,
          "Zip Code"
        );

        // Click postal code lookup button
        await this.page.waitForSelector("#hide_display_shipping_address a", {
          visible: true,
          timeout: 3000,
        });

        await this.page.click("#hide_display_shipping_address a");

        // Wait for address to be filled
        await this.page.waitForFunction(
          () => {
            const address = document.querySelector(
              "input#shipping_address_address"
            );
            return address && address.value.trim() !== "";
          },
          { timeout: 3000 }
        );

        // Fill building address
        await this.fillField(
          "input[name='shipping_address[building]']",
          this.inputs.address02 + this.inputs.address03,
          "Building Address"
        );

        // Fill contact information
        await this.fillField(
          "#shipping_address_tel",
          this.inputs.tel,
          "Phone Number"
        );
        await this.fillField("#user_email", this.inputs.mail, "Email");
        await this.fillField(
          "#user_password",
          this.inputs.password,
          "Password"
        );
        await this.fillField(
          "#user_password_confirmation",
          this.inputs.password,
          "Password Confirmation"
        );
      });

      // Step 4: Submit registration form
      logger.info("Step 4: Submitting registration form");
      await this.withRetry(async () => {
        if (!(await this.clickButton("#hide_display2", "Register button"))) {
          throw new Error("Register button not found");
        }
      });

      // Step 5: Check for form validation errors
      logger.info("Step 5: Checking for form validation errors");
      const errorMessages = await this.getErrorMessages(".formError");
      if (errorMessages) {
        logger.error("Form validation errors:" + errorMessages);
        return this.createErrorResponse(errorMessages);
      }

      // Step 6: Select payment method
      logger.info("Step 6: Selecting payment method");
      await this.withRetry(async () => {
        if (this.inputs.payment_method === "クレジットカード") {
          await this.waitForElement("#order_payment_method_id_2", {
            timeout: 5,
          });
          if (
            !(await this.clickButton(
              "#order_payment_method_id_2",
              "Credit Card payment"
            ))
          ) {
            throw new Error("Credit card payment option not found");
          }
        } else {
          await this.waitForElement("#order_payment_method_id_3", {
            timeout: 5,
          });
          if (
            !(await this.clickButton(
              "#order_payment_method_id_3",
              "NP payment"
            ))
          ) {
            throw new Error("NP payment option not found");
          }
        }
      });

      // Step 7: Confirm payment method
      logger.info("Step 7: Confirming payment method");
      await this.withRetry(async () => {
        await this.waitForElement("input#hide_display", { timeout: 5 });
        if (
          !(await this.clickButton(
            "input#hide_display",
            "Payment confirmation button"
          ))
        ) {
          throw new Error("Payment confirmation button not found");
        }
      });

      // Step 8: Handle credit card information if needed
      let creditCardErrorText = "";
      if (this.inputs.payment_method === "クレジットカード") {
        logger.info("Step 8: Processing credit card information");
        await this.withRetry(async () => {
          await this.waitForElement("#new_credit_card_number", {
            timeout: 5,
          });

          // Parse credit card data
          const creditCardData = JSON.parse(
            Buffer.from(this.inputs.credit_card, "base64").toString("utf-8")
          );

          // Format expired month (remove leading zero)
          let expiredMonth = creditCardData.card_expired_month;
          if (expiredMonth.startsWith("0")) {
            expiredMonth = expiredMonth.slice(1);
          }

          const expiredYear = "20" + creditCardData.card_expired_year;

          // Fill credit card form
          await this.fillField(
            "#new_credit_card_number",
            creditCardData.card_number,
            "Card Number"
          );
          await this.fillField(
            "#new_credit_card_name",
            creditCardData.card_name,
            "Card Name"
          );
          await this.fillField(
            "#new_credit_effective_date_2i",
            expiredMonth,
            "Expiry Month"
          );
          await this.fillField(
            "#new_credit_effective_date_1i",
            expiredYear,
            "Expiry Year"
          );
          await this.fillField(
            "#new_credit_security_code",
            creditCardData.card_cvv,
            "CVV"
          );

          await this.waitForElement("#new_credit_card_brand_other", {
            timeout: 1000,
          });
          // 4
          if (
            creditCardData.card_number.startsWith("4") ||
            creditCardData.card_number.startsWith("5")
          ) {
            await this.clickButton("input#new_credit_card_brand_other");
          }
          // 35
          if (creditCardData.card_number.startsWith("35")) {
            await this.clickButton("input#new_credit_card_brand_jcb");
          }
          // 36
          if (creditCardData.card_number.startsWith("36")) {
            await this.clickButton("input#new_credit_card_brand_diners");
          }
          // 37 or 34
          if (
            creditCardData.card_number.startsWith("37") ||
            creditCardData.card_number.startsWith("34")
          ) {
            await this.clickButton("input#new_credit_card_brand_amex");
          }
          // Submit credit card form
          if (
            !(await this.clickButton(
              "input#hide_display",
              "Credit card confirmation"
            ))
          ) {
            throw new Error("Credit card confirmation button not found");
          }

          // Check for credit card errors
          try {
            await this.page.waitForSelector("th.cart01 font", {
              timeout: 5000,
            });
            const cardError = await this.page.$("th.cart01 font");
            if (cardError) {
              creditCardErrorText = await cardError.evaluate(
                (el) => el.textContent
              );
              logger.error("Credit card error:" + creditCardErrorText);
            }
          } catch (error) {
            // No error found, continue
          }
        });
      }

      if (creditCardErrorText) {
        return this.createErrorResponse(creditCardErrorText);
      }

      // Step 9: Final payment confirmation
      logger.info("Step 9: Final payment confirmation");
      await this.withRetry(
        async () => {
          if (
            !(await this.clickButton(
              "input#hide_display1",
              "Final confirmation button"
            ))
          ) {
            throw new Error("Final confirmation button not found");
          }
        },
        10,
        1000
      );

      // Step 10: Extract order number
      logger.info("Step 10: Extracting order number");
      let orderNoText = "";
      await this.withRetry(
        async () => {
          if (!(await this.waitForElement("#order_no", { timeout: 3000 }))) {
            throw new Error("Order number element not found");
          }
          const orderNo = await this.page.$("#order_no");
          orderNoText = await orderNo.evaluate((el) => el.textContent);
          logger.info("Order number extracted:", orderNoText);
        },
        10,
        1000
      );

      // Return success result
      const successMessage = orderNoText
        ? `この度はご注文いただきありがとうございます。\nご注文の詳細をメールにてご送付しておりますので、ご確認くださいませ。\nご注文番号：${orderNoText.trim()}`
        : "Order completed but order number not found";

      logger.info("Genpeiseiyaku submission completed successfully");
      return this.createSuccessResponse(successMessage);
    } catch (error) {
      logger.error(
        "Error occurred during Genpeiseiyaku submission:" + error.message
      );

      return this.createErrorResponse(
        "予期しないエラーが発生しました。後でもう一度お試しください。"
      );
    } finally {
      await this.cleanup();
    }
  }
}

export default GenpeiseiyakuSubmit;
