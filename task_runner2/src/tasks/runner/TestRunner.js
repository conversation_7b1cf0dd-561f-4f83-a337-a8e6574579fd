import { logger } from "../../utils/logger.js";
import BaseSubmit from "./BaseSubmit.js";

class TestRunner extends BaseSubmit {
  constructor(inputs) {
    const defaultInputs = {
      url: { value: "https://news.ycombinator.com" },
    };

    super(defaultInputs);
  }

  async execute() {
    await this.initBrowser();

    const startRun = Date.now();
    const result = await this.run();
    logger.info(`Time run: ${(Date.now() - startRun) / 1000} seconds`);
    return result;
  }

  async run() {
    try {
      logger.info("Starting Hacker News scraping process...");
      logger.info(`Scraping URL: ${this.inputs.url}`);

      // Step 1: Wait for page to load completely
      logger.info("Step 1: Waiting for page to load");
      await this.withRetry(async () => {
        if (!(await this.waitForElement("tr.athing", { timeout: 10000 }))) {
          throw new Error("Hacker News stories not found");
        }
      });

      // Step 2: Extract top 5 stories
      logger.info("Step 2: Extracting top 5 stories");
      let stories = [];
      await this.withRetry(async () => {
        stories = await this.page.evaluate(() => {
          const items = [];
          const storyRows = document.querySelectorAll("tr.athing");

          for (let i = 0; i < Math.min(5, storyRows.length); i++) {
            const row = storyRows[i];
            const titleElement = row.querySelector(".titleline > a");
            const subtext = row.nextElementSibling;
            const scoreElement = subtext?.querySelector(".score");

            items.push({
              title: titleElement?.textContent?.trim() || "",
              link: titleElement?.getAttribute("href") || "",
              points: scoreElement?.textContent?.replace(" points", "") || "0",
            });
          }
          return items;
        });

        if (stories.length === 0) {
          throw new Error("No stories found");
        }
      });

      // Step 3: Format the result message
      logger.info("Step 3: Formatting results");
      let message = "\nTop 5 Hacker News Stories:\n";

      stories.forEach((story, index) => {
        message += `\n${index + 1}. ${story.title}`;
        message += `\n   Link: ${story.link}`;
        message += `\n   Points: ${story.points}`;
        message += "\n";
      });

      logger.info("Scraping completed successfully");
      logger.info(message);

      return this.createSuccessResponse(message);
    } catch (error) {
      logger.error(
        "Error occurred during Hacker News scraping:" + error.message
      );

      return this.createErrorResponse(
        "予期しないエラーが発生しました。後でもう一度お試しください。"
      );
    } finally {
      await this.cleanup();
    }
  }
}

export default TestRunner;
