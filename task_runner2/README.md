# Task Runner 2 - プロジェクトドキュメント

## 概要

Task Runner 2 は、E コマースウェブサイト上で自動化タスク（シナリオ）を実行できるウェブ自動化サービスです。このシステムは Node.js、Express.js で構築され、ブラウザとの対話には Puppeteer を使用しています。

## システムアーキテクチャ

1.  **REST API サーバー**: Express.js で構築され、クライアントからのタスク実行リクエストを受け付けます。
2.  **タスクディスパッチャ**: API からのリクエストを受け取り、対応する`Handler`を検索して初期化する中央コンポーネントです。
3.  **Submit Handlers**: `BaseSubmit.js`を継承する JavaScript クラス（`MiraiSubmit.js`、`GenpeiseiyakuSubmit.js`など）。各ハンドラーは、特定のウェブサイトで特定のシナリオを実行する責任を負います。
4.  **BaseSubmit.js**: Puppeteer の初期化、ブラウザ管理、その他のユーティリティ関数のための共通ロジックを含む基本クラスです。
5.  **Puppeteer**: ヘッドレスまたはフルブラウザを制御し、ユーザー操作を実行するライブラリです。

## セットアップと実行

### 要件

- Node.js（v16+推奨）
- npm または yarn
- Chrome/Chromium ブラウザ（Puppeteer 用）

### インストール手順

1.  リポジトリをクローンします:
    ```bash
    git clone ...
    cd task-runner2
    ```
2.  依存関係をインストールします:
    ```bash
    npm install
    # または
    yarn install
    ```
3.  環境設定:
    `.env.example`ファイルを`.env`にコピーし、必要に応じて値を編集します:
    ```env
    APP_PORT=4000
    APP_ENV=development
    CRAWLER_DEBUGGING=true
    ```

### アプリケーションの実行

- 開発モード（nodemon を使用）:
  ```bash
  npm run dev
  ```
- 本番モード:
  ```bash
  npm start
  ```

## API エンドポイント

### 1. `POST /api/v1/tasks/execute`

自動化シナリオを実行します。

**リクエストボディ:**

```json
{
  "class_name": "ClassNameHandler",
  "inputs": "{\"key1\":\"value1\",\"key2\":\"value2\"}",
  "outputs": {
    "data": {
      "error": "",
      "message": ""
    }
  }
}
```

- `class_name` (string, 必須): 実行するハンドラークラスの名前（例: `MiraiSubmit`, `GenpeiseiyakuSubmit`）。`src/tasks/runner/index.js`で定義されています。
- `inputs` (string, 必須): ハンドラーへの入力パラメータを含む JSON 文字列。ハンドラーがこの文字列を解析します。
- `outputs` (object, 必須): JSON オブジェクト。現在、このフィールドは以下の形式で必須です:
  ```json
  {
    "data": {
      "error": "",
      "message": ""
    }
  }
  ```

**レスポンス (成功時):**

```json
{
  "data": {
    "message": "Automation completed successfully",
    "error": false
  }
}
```

**レスポンス (失敗時):**

```json
{
  "data": {
    "message": "Error message from handler or system",
    "error": true
  }
}
```

### 2. `GET /healthz`

サービスの稼働状況を確認します。

**リクエストボディ:**

なし。

**レスポンス (成功時):**

- ステータスコード: `200 OK`
- ボディ:
  ```json
  {
    "uptime": 5.231299042,
    "message": "OK",
    "timestamp": 1749106533793,
    "environment": "development"
  }
  ```

**レスポンス (失敗時):**

- ステータスコード: `503 Service Unavailable` (または重大な問題がある場合は他のエラーコード)
- ボディ: エラー情報を含む場合があります。

(注意: `/healthz`エンドポイントは標準的なエンドポイントであり、`app.js`または共通のミドルウェアで設定される場合があります。レスポンスの具体的な詳細は実装によって異なる場合があります。)

## 既存のタスク

タスクは`src/tasks/runner/`ディレクトリ内の`BaseSubmit.js`を継承するクラスとして実装されています。

### 1. `GenpeiseiyakuSubmit`

- **説明**: Genpeiseiyaku ウェブサイトでの購入プロセスを自動化します。
- **入力 (JSON 文字列 `inputs` 内):**
  - `sei`, `mei`: 姓、名。
  - `seifuri`, `meifuri`: 姓（フリガナ）、名（フリガナ）。
  - `zipcode`: 郵便番号。
  - `address02`: 詳細住所。
  - `tel`: 電話番号。
  - `mail`: メールアドレス。
  - `payment_method`: 支払い方法 (例: `"クレジットカード（手数料0円）"`)。
  - `credit_card`: カード情報を含む Base64 エンコードされた JSON 文字列 (`{ "card_number": "...", "card_expired_month": "MM", "card_expired_year": "YY" }`)。

### 2. `MiraiSubmit`

- **説明**: Mirai ウェブサイトでの購入プロセスを自動化します。
- **入力**: `GenpeiseiyakuSubmit`と同様ですが、Mirai 特有のフィールドが含まれる場合があります。

## ディレクトリ構造

```
task-runner2/
├── src/
│   ├── app.js                # Expressアプリケーションのエントリーポイント
│   ├── controllers/          # APIリクエストを処理するコントローラー
│   ├── errors/               # カスタムエラークラスの定義
│   ├── middleware/           # Expressミドルウェア (例: requestId, errorHandler)
│   ├── tasks/
│   │   └── runner/           # 自動化シナリオの格納場所
│   │       ├── BaseSubmit.js # シナリオの基本クラス
│   │       ├── MiraiSubmit.js # Mirai用シナリオ
│   │       ├── GenpeiseiyakuSubmit.js # Genpeiseiyaku用シナリオ
│   │       ├── TestRunner.js # シナリオをローカルでテストするためのスクリプト
│   │       └── index.js      # シナリオ名とハンドラークラスのマッピング
│   └── utils/                # ユーティリティ関数 (例: logger)
├── .env.example              # 環境変数のサンプルファイル
├── .env                      # 環境変数ファイル (コミット対象外)
├── package.json
└── README.md
```

## 新しいタスクの追加

1.  `src/tasks/runner/`内に新しい JavaScript ファイルを作成します (例: `NewSiteSubmit.js`)。
2.  `BaseSubmit`を継承する新しいクラスを定義します:

    ```javascript
    import { logger } from "../utils/logger.js";
    import BaseSubmit from "./BaseSubmit.js";

    class NewSiteSubmit extends BaseSubmit {
      constructor(inputs) {
        super(inputs); // inputsはJSON文字列から解析されたオブジェクト
        // 必要であればinputsを検証
      }

      async run() {
        try {
          await this.initBrowser(); // ブラウザを初期化
          // NewSiteの自動化ロジック...
          // await this.page.goto('https://newsite.com');
          // await this.page.type('#username', this.inputs.username);
          logger.info("Running NewSiteSubmit");
          return { success: true, message: "NewSite task completed!" };
        } catch (error) {
          logger.error("Error in NewSiteSubmit" + " " + error);
          // エラーをスローするか、標準エラーオブジェクトを返す
          return {
            success: false,
            error: true,
            message: error.message || "Unknown error in NewSiteSubmit",
          };
        }
      }
    }

    export default NewSiteSubmit;
    ```

3.  新しいハンドラーをエクスポートするために`src/tasks/runner/index.js`ファイルを更新します:

    ```javascript
    // ... 他のインポート
    import NewSiteSubmit from "./NewSiteSubmit.js";

    const TaskRunner = {
      NewSiteSubmit,
    };

    export default TaskRunner;
    ```

## ロギング

システムはログ記録に`winston`ライブラリを使用します。各 API リクエストには一意の`requestId`が割り当てられ、そのリクエストに関連するすべてのログエントリに自動的に追加されます。これにより、追跡とデバッグが容易になります。

- **設定**: `src/utils/logger.js`内。
- **使用方法**: `src/utils/logger.js`から`logger`をインポートし、`logger.info()`、`logger.error()`などのメソッドを使用します。
  ```javascript
  import { logger } from "../utils/logger.js";
  logger.info("Processing user login" + JSON.stringify({ userId: "123" }));
  ```

## エラー処理

- ハンドラー実行中のエラーは`try...catch`で捕捉され、クライアントにエラーレスポンスが返されます。
- グローバルエラー処理ミドルウェア (`errorHandler.js`) は、コントローラー層や他のミドルウェアで処理されなかったエラーを捕捉します。
