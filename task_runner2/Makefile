# ------------ Docker Build & Deploy ----------------------------------------------------
REGISTRY=us-west1-docker.pkg.dev/ec-united-cart/docker/united-cart-chatbot
IMAGE_NAME=runner
COMMIT_SHA=$(shell git rev-parse HEAD)
NAMESPACE=chat-bot
CONTEXT=gke_ec-united-cart_us-west1-c_cart-stg
PROD_CONTEXT=gke_ec-united-cart_us-west1_cart-prod

.PHONY: docker-build docker-push k8s-release-stg release-stg

docker-build:
	docker build --platform=linux/amd64 -f Dockerfile -t $(IMAGE_NAME):latest .
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):latest
	docker tag $(IMAGE_NAME):latest $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

docker-push:
	docker push $(REGISTRY)/$(IMAGE_NAME):latest
	docker push $(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA)

k8s-release-stg:
	kubectl --context $(CONTEXT) set image deployment/bot-runner runner=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE)

release-stg: docker-build docker-push k8s-release-stg


k8s-release-prod:
	kubectl --context $(PROD_CONTEXT) set image deployment/bot-runner runner=$(REGISTRY)/$(IMAGE_NAME):$(COMMIT_SHA) -n $(NAMESPACE)

release-prod: docker-build docker-push k8s-release-prod
