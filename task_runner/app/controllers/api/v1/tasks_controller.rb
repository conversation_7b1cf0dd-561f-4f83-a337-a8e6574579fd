# frozen_string_literal: true

module Api
  module V1
    class TasksController < Api::BaseController
      def execute
        @class_name = params["class_name"]
        inputs = parse_inputs(params["inputs"])
        outputs = parse_inputs(params["outputs"])

        # If using a running server call backend server broadcast result
        # user_id = params["user_id"]
        # ssid = params["ssid"]
        klass = resolve_task_class
        @service = klass.call(inputs: inputs, outputs: outputs)

        render json: handle_service_response
      end

      private

      def resolve_task_class
        "Tasks::Runner::#{@class_name}".constantize
      rescue NameError => e
        render json: { error: "Invalid class name", details: e.message }, status: :bad_request
        nil
      end

      def handle_service_response
        return @service.outputs if @service.success?

        @service.outputs[:data] ||= {}
        @service.outputs[:data][:message] ||= @service.message
        @service.outputs[:data][:error] = true
        @service.outputs
      end

      def parse_inputs(inputs_string)
        JSON.parse(inputs_string)
      rescue JSON::ParserError => e
        render json: { error: "Invalid inputs format", details: e.message }, status: :bad_request
        nil
      end
    end
  end
end
