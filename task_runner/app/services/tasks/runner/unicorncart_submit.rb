module Tasks
  module Runner
    class UnicorncartSubmit < SharedMethods
      include Interactor
      # Function Use
      # vars(keys): This function accepts an array of keys, where each key is the name of the attribute whose value you want to retrieve.
      # handle_response(message, error): This function processes the response with a message, and optionally includes error information if an error occurs.
      # check_box(selector, value): This function interacts with a checkbox (checking or unchecking it).
      # set_select(selector, value): This function interacts with a dropdown to select a value from a <select> element.

      def submit
        Rails.logger.info("Selenium fill_data...")
        unicorncart_submit
        Rails.logger.info("Selenium submit...")

        handle_order_complete
      end

      def unicorncart_submit
        context.session.has_content?("商品")
        set_input("#order_name_family", vars("sei"))
        set_input("#order_name_first", vars("mei"))
        set_input("#order_kana_family", vars("seifuri"))
        set_input("#order_kana_first", vars("meifuri"))
        set_input("#order_address_postcode_0", vars("zipcode").slice(0, 3))
        set_input("#order_address_postcode_1", vars("zipcode").slice(3, 7))
        set_input("#order_address_1", vars("address02"))
        set_input("#order_tel_0", vars("tel1"))
        set_input("#order_tel_1", vars("tel2"))
        set_input("#order_tel_2", vars("tel3"))

        set_input("#order_email", vars("mail"))
        set_input("#order_email_confirm", vars("mail"))

        set_input("#order_password", vars("password"))
        set_input("#order_password_confirm", vars("password"))
        set_select("#order_sex", vars("sex"))

        set_select("#payment_method", "代金引換")
        order_birthday = "#{vars("day")}/#{vars("month")}/#{vars("year")}"
        context.session.find("#order_birthday").set(order_birthday)
        check_box("#agree_check", true)

        sleep(1)
        context.session.find("#submit").click

        if context.session.has_content?("注文確認画面")
          context.session.find("#submit_confirm").click
        end
      end

      def handle_order_complete
        if context.session.has_css?(".invalid-feedback")
          errors = context.session.find_all(".invalid-feedback")
          arr_mes = errors.map { |error| error.text }
          handel_response(arr_mes.join("\n"), true)
          return
        end

        if context.session.has_content?("ご注文完了画面")
          handel_response(context.session.find(".order-number").text, false)
        end
      end
    end
  end
end
