module Tasks
  module Runner
    class SharedMethods
      include Interactor
      def call
        start_time = Time.current

        init_start_time = Time.current
        init_capybara
        init_end_time = Time.current
        Rails.logger.info("Time taken for init_capybara: #{init_end_time - init_start_time} seconds")

        visit_start_time = Time.current
        context.session.visit(vars("url"))
        visit_end_time = Time.current
        Rails.logger.info("Time taken for visit url: #{visit_end_time - visit_start_time} seconds")

        Rails.logger.info("Selenium Submit...")

        submit_start_time = Time.current
        submit
        submit_end_time = Time.current
        Rails.logger.info("Time taken for submit: #{submit_end_time - submit_start_time} seconds")
        
        end_time = Time.current 
        Rails.logger.info("Total time taken for call: #{end_time - start_time} seconds") 

      rescue StandardError => e
        Rails.logger.info("Selenium Submit Failed: #{e.message}")

        context.fail!(message: "予期しないエラーが発生しました。後でもう一度お試しください。")
      ensure
        shut_down
      end

      def init_capybara
        Rails.logger.info("Initing capybara...")
        driver = ENV["CRAWLER_DEBUGGING"] == "true" ? :chrome : :headless_chromium

        if Capybara.drivers[:headless_chromium].nil? && driver == :headless_chromium
          Capybara.register_driver :headless_chromium do |app|
            options = Selenium::WebDriver::Chrome::Options.new
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36")
            options.add_argument("--headless")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--enable-features=NetworkService,NetworkServiceInProcess")
            options.add_argument("--window-size=1920,4000")

            Capybara::Selenium::Driver.new(
              app,
              browser: :chrome,
              options: options,
            )
          end
        end

        Capybara.default_driver = driver
        Capybara.javascript_driver = driver
        Capybara.save_path = "#{Rails.root.join("tmp/screenshots")}"

        context.session = Capybara::Session.new(ENV["CRAWLER_DEBUGGING"] == "true" ? :selenium_chrome : :headless_chromium)
      end

      def shut_down
        Rails.logger.info("Shutting down capybara...")
        if context.session.respond_to?(:quit)
          Capybara.current_session.quit
          context.session.quit
          context.session.driver.quit
        end
        Rails.logger.info("Finsihed: Capybara shut down")
      end

      def check_box(selector, value)
        checkbox = context.session.find(selector)
        return if checkbox.checked? == value

        checkbox.click
      end

      def set_select(selector, value)
        input = context.session.find(selector)
        input.select(value)
      end

      def set_input(selector, value)
        input = context.session.find(selector)
        input.set("")
        input.set(value)
      end

      def vars(keys)
        value = context.inputs[keys]["value"]
        str_base64?(value) ? Base64.decode64(value) : value
      end

      def str_base64?(str)
        return false unless str.is_a?(String)

        decode64 = Base64.decode64(str)
        return false unless decode64.ascii_only?

        Base64.strict_encode64(decode64) == str
      end

      def handel_response(message, error)
        context.outputs["data"]["message"] = message
        context.outputs["data"]["error"] = error
      end
    end
  end
end
