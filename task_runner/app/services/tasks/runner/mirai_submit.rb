module Tasks
  module Runner
    class MiraiSubmit < SharedMethods
      include Interactor
      # Function Use class SharedMethods
      # vars(keys): This function accepts an array of keys, where each key is the name of the attribute whose value you want to retrieve.
      # handle_response(message, error): This function processes the response with a message, and optionally includes error information if an error occurs.
      # check_box(selector, value): This function interacts with a checkbox (checking or unchecking it).
      # set_select(selector, value): This function interacts with a dropdown to select a value from a <select> element.

      def submit
        Rails.logger.info("Checking")

        first_button_start_time = Time.current
        raise "Can't find .p-cta__bodyBtn_a" unless context.session.has_css?(".p-cta__bodyBtn_a", wait: 5)
        context.session.find(".p-cta__bodyBtn_a", match: :first).click
        first_button_end_time = Time.current
        Rails.logger.info("Time taken for Click first Button: #{first_button_end_time - first_button_start_time} seconds")


        checkout_button_start_time = Time.current
        context.session.find(".btn-checkout").click
        checkout_button_end_time = Time.current
        Rails.logger.info("Time taken for Checkout Button: #{checkout_button_end_time - checkout_button_start_time} seconds")


        fill_form_start_time = Time.current

        Rails.logger.info("Fill Form Submit")
        set_input(".name1", vars("sei"))
        set_input(".name2", vars("mei"))
        set_input(".kana1", vars("seifuri"))
        set_input(".kana2", vars("meifuri"))
        set_input("input[name='ORDERER_POSTAL_CODE']", vars("zipcode"))
        set_input("input[name='ORDERER_PHONE_NUMBER']", vars("tel"))
        set_input("input[name='EMAIL']", vars("mail"))
        set_input("input[name='ORDERER_ADDRESS3']", vars("address02"))
        # クレジットカード（手数料0円）
        # 後払い
        context.session.find("label", text: vars("payment_method")).click

        if vars("payment_method") == "クレジットカード（手数料0円）"
          credit = JSON.parse(vars("credit_card"))
          set_input("input[name='zeus_token_card_number']", credit["card_number"])
          set_select("#zeus_token_card_expires_month", credit["card_expired_month"])
          set_select("#zeus_token_card_expires_year", credit["card_expired_year"])
        end
        fill_form_end_time = Time.current
        Rails.logger.info("Time taken for Fill Form: #{fill_form_end_time - fill_form_start_time} seconds")

        Rails.logger.info("Submit...")
        context.session.find(".btn-submit").click


        handle_err_start_time = Time.current
        return process_error_messages(handle_err_start_time) if context.session.has_css?(".nmessage-default", wait: 2)
        return process_error_alert(handle_err_start_time) if context.session.has_css?("#error-msg", wait: 2)



        handle_success_start_time = Time.current
        process_offer_selection
        handle_success_end_time = Time.current
        Rails.logger.info("Time taken for success: #{handle_success_end_time - handle_success_start_time} seconds")
      end

      def process_error_alert(handle_err_start_time)
        Rails.logger.info("Error Alert...")
        errors = context.session.find("#error-msg").text
        handel_response(errors, true)

        handle_err_end_time = Time.current
        Rails.logger.info("Time taken for error: #{handle_err_end_time - handle_err_start_time} seconds")
      end

      def process_error_messages(handle_err_start_time)
        Rails.logger.info("Error...")
        errors = context.session.find_all(".nmessage-default")
        arr_mes = errors.map { |error| error.text }
        handel_response(arr_mes.join("\n"), true)

        handle_err_end_time = Time.current
        Rails.logger.info("Time taken for error: #{handle_err_end_time - handle_err_start_time} seconds")
      end

      def process_offer_selection
        Rails.logger.info("Offer...")

        raise "Can't select item" unless context.session.has_css?(".CV", wait: 5)

        if vars("offer") == "全額返金も付いて30日間も試せるお得な定期ケアコースで始める"
          context.session.find("#selectItem").click
          unless context.session.has_css?(".btn-submit-checkout", wait: 30)
            context.session.save_screenshot("miraiSubmit_75_#{Time.current}.png")
            raise "Can't find #btn-submit-checkout"
          end

          context.session.find(".btn-submit-checkout").click
        else
          context.session.find("#btn1").click
        end

        process_order_completion
      end

      def process_order_completion
        unless context.session.has_css?(".orderCompleteMsg", wait: 30)
          context.session.save_screenshot("miraiSubmit_84_#{Time.current}.png")
          raise "Can't find order complete message"
        end

        handel_response(context.session.find(".orderCompleteMsg").text, false)
      end
    end
  end
end
