{"items": [{"order": 1, "task": {"fileName": "", "className": "TestHuckleberrysubmit", "testcaseName": "test_huckleberrysubmit"}, "inputs": {"product_url": {"value": "https://hb-subscription-sample.myshopify.com/products/02", "type": "string"}, "mail": {"value": "<EMAIL>", "type": "string"}, "sei": {"value": "テスト", "type": "string"}, "mei": {"value": "テスト", "type": "string"}, "zipcode": {"value": "163-6006", "type": "string"}, "pref": {"value": "北海道", "type": "string"}, "city": {"value": "市区町村", "type": "string"}, "st": {"value": "新宿区", "type": "string"}, "bldg": {"value": "住所", "type": "string"}, "tel": {"value": "0359098181", "type": "string"}, "payment_method": {"value": "クレジットカード", "type": "string"}, "credit": {"value": "{\"number\":\"****************\",\"brand\":0,\"expiryMonth\":\"03\",\"expiryYear\":\"30\",\"firstName\":\"TARO\",\"lastName\":\"YAMADA\",\"cvc\":\"737\"}", "type": "string"}}, "outputs": ["status"]}]}