# Generated by Selenium IDE
import pytest
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities

class TestHuckleberrysubmit():
  def setup_method(self, method):
    self.driver = webdriver.Chrome()
    self.vars = {}

  def teardown_method(self, method):
    self.driver.quit()

  def test_huckleberrysubmit(self):
    # self.vars["product_url"] = "https://hb-subscription-sample.myshopify.com/products/02"
    # self.vars["sei"] = "テスト"
    # self.vars["mei"] = "テスト"
    # self.vars["zipcode"] = "1636006"
    # self.vars["mail"] = "<EMAIL>"
    # self.vars["pref"] = "東京都"
    # self.vars["city"] = "テスト"
    # self.vars["st"] = "新宿区"
    # self.vars["bldg"] = "西新宿住友不動産オークタワー6F"
    # self.vars["tel"] = "08077778888"
    # self.vars["payment_method"] = "クレジットカード"
    # self.vars["credit"] = "{\"number\":\"****************\",\"brand\":2,\"expiryMonth\":\"03\",\"expiryYear\":\"27\",\"firstName\":\"YUKI\",\"lastName\":\"KOGAWARA\",\"cvc\":\"555\"}"
    self.driver.get(self.vars["product_url"])
    self.driver.set_window_size(1500, 4000)
    # self.driver.find_element(By.LINK_TEXT, "【単品販売と定期販売が可能な場合】グリーンスムージー250ｍｌ").click()
    time.sleep(2)
    self.driver.find_element(By.CSS_SELECTOR, ".shopify-payment-button__button").click()
    time.sleep(2)
    self.driver.find_element(By.ID, "checkout_email").send_keys(self.vars["mail"])
    self.driver.find_element(By.ID, "checkout_buyer_accepts_marketing").click()
    dropdown = self.driver.find_element(By.ID, "checkout_shipping_address_country")
    dropdown.find_element(By.XPATH, "//option[. = '日本']").click()
    time.sleep(1)
    self.driver.find_element(By.ID, "checkout_shipping_address_last_name").send_keys(self.vars["sei"])
    self.driver.find_element(By.ID, "checkout_shipping_address_first_name").send_keys(self.vars["mei"])
    self.driver.find_element(By.ID, "checkout_shipping_address_zip").send_keys(self.vars["zipcode"])
    time.sleep(3)
    self.driver.find_element(By.XPATH, "//*[@id='checkout_shipping_address_province']/option[. = '{}']".format(self.vars["pref"])).click()
    WebDriverWait(self.driver, 10).until(expected_conditions.visibility_of_element_located((By.ID, "checkout_shipping_address_city")))
    self.driver.find_element(By.ID, "checkout_shipping_address_city").clear()
    self.driver.find_element(By.ID, "checkout_shipping_address_city").send_keys(self.vars["city"])
    self.driver.find_element(By.NAME, "checkout[shipping_address][address1]").clear()
    self.driver.find_element(By.ID, "checkout_shipping_address_address1").send_keys(self.vars["st"])
    self.driver.find_element(By.ID, "checkout_shipping_address_address2").send_keys(self.vars["bldg"])
    self.driver.find_element(By.ID, "checkout_shipping_address_phone").send_keys(self.vars["tel"])

    # 会員登録をして、次へ進む
    self.driver.find_element(By.ID, "continue_button").click()
    time.sleep(2)
    # FormError メッセージを取得する
    form_errors = self.driver.execute_script("return Array.from(document.querySelectorAll(\".field__message.field__message--error\")).map(e => e.innerText).join()")
    if form_errors is not None and len(form_errors) > 0:
      self.vars["form_err"] = form_errors
      raise ValueError(form_errors)

    WebDriverWait(self.driver, 30).until(expected_conditions.url_contains("&step=shipping_method"))
    self.driver.find_element(By.ID, "continue_button").click()
    WebDriverWait(self.driver, 30).until(expected_conditions.url_contains("&step=payment_method"))
    if self.driver.execute_script("return (arguments[0] === \"クレジットカード\")", self.vars["payment_method"]):
      self.vars["credit_number"] = self.driver.execute_script("return JSON.parse(arguments[0]).number", self.vars["credit"])
      self.vars["credit_number_group"] = self.driver.execute_script("regex = /^(\d{0,4})(\d{0,4})(\d{0,4})(\d{0,4})$/g; return regex.exec(arguments[0])", self.vars["credit_number"])
      self.vars["credit_expiry_month"] = self.driver.execute_script("return JSON.parse(arguments[0]).expiryMonth", self.vars["credit"])
      self.vars["credit_expiry_year"] = self.driver.execute_script("return \'20\' + JSON.parse(arguments[0]).expiryYear", self.vars["credit"])
      self.vars["credit_fullname"] = self.driver.execute_script("return JSON.parse(arguments[0]).firstName + \" \" + JSON.parse(arguments[0]).lastName", self.vars["credit"])
      self.vars["credit_cvc"] = self.driver.execute_script("return JSON.parse(arguments[0]).cvc", self.vars["credit"])

      self.driver.switch_to.frame(1)
      self.driver.find_element(By.NAME, "number").send_keys(self.vars["credit_number_group"][1])
      self.driver.find_element(By.NAME, "number").send_keys(self.vars["credit_number_group"][2])
      self.driver.find_element(By.NAME, "number").send_keys(self.vars["credit_number_group"][3])
      self.driver.find_element(By.NAME, "number").send_keys(self.vars["credit_number_group"][4])
      # クレジットカード情報を見えないように
      self.driver.execute_script("document.getElementsByName(\'number\')[0].style.backgroundColor=\'black\'")
      self.driver.execute_script("document.getElementsByName(\'number\')[0].style.color =\'black\'")
      self.driver.switch_to.default_content()
      self.driver.switch_to.frame(2)
      self.driver.find_element(By.ID, "name").send_keys(self.vars["credit_fullname"])
      # クレジットカード情報を見えないように
      self.driver.execute_script("document.getElementsByName(\'name\')[0].style.backgroundColor=\'black\'")
      self.driver.execute_script("document.getElementsByName(\'name\')[0].style.color =\'black\'")
      self.driver.switch_to.default_content()

      self.driver.switch_to.frame(3)
      self.driver.find_element(By.NAME, "expiry").send_keys(self.vars["credit_expiry_month"])
      self.driver.find_element(By.NAME, "expiry").send_keys(self.vars["credit_expiry_year"])
      # クレジットカード情報を見えないように
      self.driver.execute_script("document.getElementsByName(\'expiry\')[0].style.backgroundColor=\'black\'")
      self.driver.execute_script("document.getElementsByName(\'expiry\')[0].style.color =\'black\'")
      self.driver.switch_to.default_content()

      self.driver.switch_to.frame(4)
      self.driver.find_element(By.ID, "verification_value").send_keys(self.vars["credit_cvc"])
      # クレジットカード情報を見えないように
      self.driver.execute_script("document.getElementsByName(\'verification_value\')[0].style.backgroundColor=\'black\'")
      self.driver.execute_script("document.getElementsByName(\'verification_value\')[0].style.color =\'black\'")
      self.driver.switch_to.default_content()

    time.sleep(2)
    self.driver.find_element(By.XPATH, "//*[@id=\"checkout_subscription_agreement\"]").click()
    self.driver.execute_script("window.scrollTo(0,100000);")
    if self.driver.execute_script("return (arguments[0] === \"False\")", self.vars["is_dry"]):
      self.driver.find_element(By.ID, "continue_button").click()
      time.sleep(3)
      # PayError メッセージを取得する
      if self.driver.execute_script("return document.querySelector(\".section.section--payment-method > div.section__content > div:nth-child(2) > div > p\")"):
        pay_errors = self.driver.execute_script("return document.querySelector(\".section.section--payment-method > div.section__content > div:nth-child(2) > div > p\").innerText")
        if pay_errors is not None and len(pay_errors) > 0:
          self.vars["pay_err"] = 'クレジットカード登録が失敗しました。'
          raise ValueError(pay_errors)

      WebDriverWait(self.driver, 30).until(expected_conditions.url_contains("thank_you"))
