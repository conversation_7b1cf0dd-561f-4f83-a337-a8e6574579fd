module Tasks
  module Crawlers
    class CrawlerCheckProductVariantService < Base
      include Interactor

      # Input Crawler: url

      def crawl
        context.input ||= {}
        context.errors = {}
        context.url = context.input.dig("url", "value") || "https://laclulu.xyz/lp?u=upsell_chattest"
        context.product_name = context.input.dig("product", "value") || "テスト_アップセル後"
        context.variant = context.input.dig("variant", "value") || ["S", "Red 12", "M"]

        context.session.visit(context.url)
        sleep(2) # wait product select box to load

        # get all product form
        context.product_container = context.session.find("#product_container")
        context.products = context.product_container.all(".form_group_ec")

        # In case of multiple products, the element has an id attribute
        buy_multiple_product = context.products.first["id"].present?

        buy_multiple_product ? check_variant_when_buy_multiple : check_variant_when_buy_single
      end

      def check_variant_when_buy_multiple
        product_selection = context.product_container.find("p", text: context.product_name)
        product_selection_path = product_selection.path

        context.products.each do |product_element|
          next if product_selection_path.exclude? product_element.path

          context.product_element = product_element
          choose_variant

          break
        end
      end

      def check_variant_when_buy_single
        product_selection = context.product_container.find("#product_id")
        product_selection.select context.product_name # need to use different way to choose the product if using id

        sleep(1) # sleep 1s after each selection to wait this web to load

        context.product_element = context.product_container
        choose_variant
      end

      def choose_variant
        context.input_variants = context.product_element.find(".option_types").all(".input_option_type", visible: :all)

        context.input_variants.each.with_index do |input, index|
          options = input.text.split("\n")

          if options.include? context.variant[index]
            input.select context.variant[index]
          else
            context.errors[:"variant_#{index}"] = ["wrong option type, please select #{options}"]
            break
          end

          sleep(1)
        end
      end
    end
  end
end
# ::Tasks::Crawlers::CrawlerCheckProductVariantService
