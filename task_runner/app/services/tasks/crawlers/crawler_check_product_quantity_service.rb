module Tasks
  module Crawlers
    class CrawlerCheckProductQuantityService < Base
      include Interactor

      def crawl
        context.input ||= {}
        context.errors = {}
        context.url = context.input.dig("url", "value") || "https://laclulu.xyz/lp?u=upsell_chattest"
        context.product_name = context.input.dig("product", "value") || "テスト_アップセル前"
        context.quantity = context.input.dig("quantity", "value") || "1 個"

        context.session.visit(context.url)
        sleep(2) # wait js web load

        # get all product form
        context.product_container = context.session.find("#product_container")
        context.products = context.product_container.all(".form_group_ec")

        # In case of multiple products, the element has an id attribute
        buy_multiple_product = context.products.first["id"].present?

        buy_multiple_product ? check_quantity_when_buy_multiple : check_quantity_when_buy_single
      end

      def check_quantity_when_buy_multiple
        product_selection = context.product_container.find("p", text: context.product_name)
        product_selection_path = product_selection.path

        context.products.each do |product_element|
          next if product_selection_path.exclude? product_element.path

          context.quantity_element = product_element.find(".quantity_select", visible: :all)
          choose_quantity

          break
        end
      end

      def check_quantity_when_buy_single
        product_selection = context.product_container.find("#product_id")
        product_selection.select context.product_name

        sleep(1) # sleep 1s after each selection to wait this web to load

        context.quantity_element = context.product_container.find(".quantity_select", visible: :all)
        choose_quantity
      end

      def choose_quantity
        # when on flag select quantity, <select id="quantity" class="quantity_select">
        # when off flag select quantity, <input type="hidden" class="quantity_select" value="1">

        if context.quantity_element.tag_name == "select"
          options = context.quantity_element.text.split("\n")

          return if options.include? context.quantity

          context.errors[:quantity] = ["wrong quantity, please select #{options}"]
        else
          return if context.quantity_element.value == context.quantity

          context.errors[:quantity] = ["wrong quantity, please select #{context.quantity_element.value}"]
        end
      end
    end
  end
end
# ::Tasks::Crawlers::CrawlerCheckProductQuantityService
