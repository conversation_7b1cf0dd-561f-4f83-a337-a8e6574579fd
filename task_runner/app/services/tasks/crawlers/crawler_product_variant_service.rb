# frozen_string_literal: true

module Tasks
  module Crawlers
    class CrawlerProductVariantService < Base
      include Interactor

      # Input Crawler: url

      after do
        shutdown
      end

      def crawl
        context.url = context.input.dig("url", "value")
        context.product_name = context.input.dig("product", "value")
        # can use product_id too

        context.session.visit(context.url)
        sleep(2) # wait product select box to load

        # choose correct product option
        product_selection = context.session.find("#product_id")
        product_selection.select context.product_name # need to use different way to choose the product if using id

        sleep(1) # sleep 1s after each selection to wait this damn shit web to load

        # basically we only need to check on the first item (test namek)
        first_item = context.session.first(".option_types")
        first_item_options = first_item.all(".option_type", visible: :all)
        # init array of selections here
        context.options = []

        first_item_options.each do |first_item_option|
          label = first_item_option.find("label", visible: :all).text(:all)

          context.options.push(
            {
              label:,
              options: [],
            },
          )
        end
        # Now we have all the options ready
        # We run to check and click all the option inside the option ( test namek / サイズ / ....)
        first_item_options.each.with_index do |first_item_option, index|
          options_of_this_option = first_item_option.all("option", visible: :all)
          # if index = 0, the options are visible from beginning. just need to get all
          context.options[index][:options] = options_of_this_option.map(&:text) if index.zero?

          # Then we started to click on each options, then add the AFTER FETCHED OPTIONS into options[index + 1][:options]
          options_of_this_option.each do |opt|
            first_item_option.select opt.text
            # every time select an option. we wait 0.5 sec
            sleep(0.5)
            if first_item_options[index + 1]
              next_option = first_item_options[index + 1]
              context.options[index + 1][:options].push(next_option.all("option", visible: :all).map(&:text))
            end
          end

          # after that, we unify the option
          context.options[index][:options].flatten!
          context.options[index][:options].uniq!
          context.options[index][:options].reject!(&:empty?)
        end

        context.data = context.options
      end
    end
  end
end
