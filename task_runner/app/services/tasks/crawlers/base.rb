# frozen_string_literal: true

module Tasks
  module Crawlers
    class Base
      include Interactor

      def call
        context.driver = :chrome

        register_driver
        init_session

        crawl

        context.fail! if context.errors.present?
      ensure
        # sleep(20)
        shutdown
      end

      def register_driver
        Capybara.register_driver :chrome do |app|
          ## add proxy:
          # plan 1:
          # proxy = Selenium::WebDriver::Proxy.new(
          #   http: ENV["PROXY_SERVER"],
          #   ssl: ENV["PROXY_SERVER"]
          # )
          # cap = Selenium::WebDriver::Remote::Capabilities.chrome(proxy: proxy)
          # Capybara::Selenium::Driver.new(app, browser: :chrome, capabilities: cap)

          # plan 2:
          options = Selenium::WebDriver::Chrome::Options.new
          options.add_argument("--proxy-server=#{ENV["PROXY_SERVER"]}") if ENV["PROXY_SERVER"].present?
          options.add_argument("--headless=new")

          Capybara::Selenium::Driver.new(app, browser: :chrome, options:)
        end

        Capybara.default_driver = context.driver
        Capybara.javascript_driver = context.driver
      end

      def init_session
        Capybara.default_max_wait_time = 30
        context.session = Capybara::Session.new(context.driver)
      end

      def register_proxy
        return if ENV["PROXY_SERVER"].blank?

        username = ENV["PROXY_USER"]
        password = ENV["PROXY_PASSWORD"]

        browser = context.session.driver.browser
        browser.register(username: username, password: password)
      end

      def shutdown
        Capybara.current_session.quit
        context.session.quit
        context.session.driver.quit
        puts "-------------- SHUTDOWN SUCCESS-----------"
      end
    end
  end
end
