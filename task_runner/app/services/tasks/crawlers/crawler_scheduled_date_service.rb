# frozen_string_literal: true

module Tasks
  module Crawlers
    class CrawlerScheduledDateService < Base
      include Interactor

      # Input Crawler: url

      after do
        shutdown
      end

      def crawl
        context.url = context.input["url"]["value"]

        context.session.visit(context.url)
        sleep(2)

        context.options = []
        scheduled_date = context.session.find("#select_scheduled_to_be_delivered_at")
        scheduled_date_options = scheduled_date.all("option", visible: :all)
        scheduled_date_options.each do |sdo|
          context.options.push(sdo.text)
        end

        context.data = context.options
      end
    end
  end
end
