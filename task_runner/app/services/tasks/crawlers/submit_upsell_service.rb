module Tasks
  module Crawlers
    class SubmitUpsellService < Base
      include Interactor

      def login_user
        context.check_box_login = context.session.find("#show-login-view")

        return if context.check_box_login.blank?

        context.check_box_login.click
        sleep(0.5)

        login_view = context.session.find("#login-view")

        context.session.current_url

        login_view.find(".input_box_email_ec").set("<EMAIL>")
        login_view.find(".input_box_password_ec").set("Test1234")
        sleep(0.5)

        login_view.find(".login_button_ec").click

        sleep(2)
        # Selenium::WebDriver::Wait.new(:timeout => 30).until { old_url != context.session.current_url }
      end

      def crawl
        context.url = "https://laclulu.xyz/lp?u=upsell_chattest"
        context.product_name = "テスト_アップセル後"

        register_proxy
        context.session.visit(context.url)

        sleep(2) # wait product select box to load

        login_user
        context.session.save_and_open_screenshot
        context.check_box_login.blank? ? fill_data_before_login : fill_data_after_login

        upsell_view
      end

      def fill_product_and_variant
        # choose correct product option
        product_selection = context.session.find("#product_id")
        product_selection.select context.product_name

        sleep(1) # sleep 1s after each selection to wait this damn shit web to load

        # choose variant
        context.variants = [["S", "Red 1", "M"]]

        context.input_variants =  context.session.all(".input_option_type", visible: :all)

        context.input_variants.each.with_index do |input, index|
          input.select context.variants[0][index]
          sleep(1)
        end
      end

      def submit
        context.session.find("#optin").click
        sleep(0.5)

        context.session.find("#submit").click
      end

      def fill_data_after_login
        fill_product_and_variant

        submit
      end

      def fill_data_before_login
        fill_product_and_variant

        # fill name and kana
        context.session.find("#order_billing_address_attributes_name1").set("お名前")
        sleep(1)

        context.session.find("#order_billing_address_attributes_name2").set("お名前")
        sleep(1)

        # fill zip code
        context.session.find("#order_billing_address_attributes_zip01").set("150")
        sleep(1)

        context.session.find("#order_billing_address_attributes_zip02").set("0031")
        sleep(1)

        # address
        context.session.find("#order_billing_address_attributes_addr02").set("test 1234")

        # tel
        context.session.find("#form-validation-field-0").set("111")
        context.session.find("#form-validation-field-1").set("1111")
        context.session.find("#form-validation-field-2").set("1111")

        # email, password
        context.session.find("#email").set("<EMAIL>")
        context.session.find("#password").set("Test1234")

        context.session.find("#order_customer_attributes_sex_id").select "Man"

        context.session.find("#order_customer_attributes_job_id").select "公務員"

        submit
      end

      def upsell_view
        old_url = context.session.current_url

        upsell_view = context.session.find("#upsell-view")

        # TODO: Fill data upsell
        sleep(2)
        upsell_view.find("#submit").click

        Selenium::WebDriver::Wait.new(:timeout => 30).until { old_url != context.session.current_url }
        # TODO: Handle Error When Submit Upsell

        context.session.find("#submit").click
      end
    end
  end
end
