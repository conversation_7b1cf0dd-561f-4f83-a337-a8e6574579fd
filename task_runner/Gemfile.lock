GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    bootsnap (1.17.1)
      msgpack (~> 1.2)
    builder (3.2.4)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-selenium (0.0.6)
      capybara
      selenium-webdriver
    coderay (1.1.3)
    concurrent-ruby (1.2.3)
    crass (1.0.6)
    date (3.3.4)
    debug (1.9.1)
      irb (~> 1.10)
      reline (>= 0.3.8)
    dotenv (3.0.2)
    dotenv-rails (3.0.2)
      dotenv (= 3.0.2)
      railties (>= 6.1)
    erubi (1.12.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    interactor (3.1.2)
    io-console (0.7.2)
    irb (1.11.2)
      rdoc
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.7.1)
    language_server-protocol (********)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.0.0)
    mini_mime (1.1.5)
    minitest (5.22.2)
    msgpack (1.7.2)
    net-imap (0.4.10)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.4.0.1)
      net-protocol
    nio4r (2.7.0)
    nokogiri (1.16.2-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.2-arm-linux)
      racc (~> 1.4)
    nokogiri (1.16.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.2-x86-linux)
      racc (~> 1.4)
    nokogiri (1.16.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.2-x86_64-linux)
      racc (~> 1.4)
    parallel (1.24.0)
    parser (3.3.0.5)
      ast (~> 2.4.1)
      racc
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (5.1.2)
      stringio
    public_suffix (5.0.4)
    puma (5.6.8)
      nio4r (~> 2.0)
    racc (1.7.3)
    rack (2.2.8.1)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.1.0)
    rdoc (6.6.2)
      psych (>= 4.0.0)
    regexp_parser (2.9.0)
    reline (0.4.2)
      io-console (~> 0.5)
    rexml (3.2.6)
    rubocop (1.60.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.30.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.30.0)
      parser (>= *******)
    rubocop-capybara (2.20.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.25.1)
      rubocop (~> 1.41)
    rubocop-graphql (1.5.0)
      rubocop (>= 0.90, < 2)
    rubocop-rails (2.23.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.30.0, < 2.0)
    rubocop-rspec (2.26.1)
      rubocop (~> 1.40)
      rubocop-capybara (~> 2.17)
      rubocop-factory_bot (~> 2.22)
    ruby-progressbar (1.13.0)
    rubyzip (2.3.2)
    selenium-devtools (0.122.0)
      selenium-webdriver (~> 4.2)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sqlite3 (1.7.2-aarch64-linux)
    sqlite3 (1.7.2-arm-linux)
    sqlite3 (1.7.2-arm64-darwin)
    sqlite3 (1.7.2-x86-linux)
    sqlite3 (1.7.2-x86_64-darwin)
    sqlite3 (1.7.2-x86_64-linux)
    stimulus-rails (1.3.3)
      railties (>= 6.0.0)
    stringio (3.1.0)
    thor (1.3.0)
    timeout (0.4.1)
    turbo-rails (2.0.4)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.13)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  bootsnap
  capybara
  capybara-selenium
  debug
  dotenv-rails
  importmap-rails
  interactor
  jbuilder
  pry (~> 0.14.2)
  pry-rails
  puma (~> 5.0)
  rack-cors
  rails (~> 7.0.8, >= *******)
  rexml
  rubocop
  rubocop-capybara
  rubocop-graphql
  rubocop-rails
  rubocop-rspec
  selenium-devtools
  selenium-webdriver
  sprockets-rails
  sqlite3 (~> 1.4)
  stimulus-rails
  turbo-rails
  tzinfo-data
  webdrivers (~> 5.3, >= 5.3.1)

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.5.6
