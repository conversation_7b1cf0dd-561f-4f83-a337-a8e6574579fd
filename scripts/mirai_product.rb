require 'uri'
require 'json'
require 'net/http'
require 'faye/websocket'
require 'eventmachine'
require 'openssl'

# Input configuration
# Production environment settings
# graphql_url = 'https://ugchatbot.net/graphql'
# user_id = '5442cb78-95f2-47e4-bf10-0228f98ac768'
# scenario_id = 'd7179000-1b31-47c7-9845-1a449cf40518'
# url = 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw'
# ws_url_base = 'wss://ugchatbot.net/cable'

# Staging environment settings
graphql_url = 'https://chatbot.unicorncart.jp/graphql'
user_id = '20adc23e-f2ea-49b7-9d17-4763fcece4c3'
scenario_id = '01920d62-3f88-4c9e-b9a5-be8ba71b6928'
url = 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw'
ws_url_base = 'wss://chatbot.unicorncart.jp/cable'

# v2
# graphql_url = 'https://chatbot2.behemoth.vn/graphql'
# user_id = '20adc23e-f2ea-49b7-9d17-4763fcece4c3'
# scenario_id = '01920d62-3f88-4c9e-b9a5-be8ba71b6928'
# url = 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw'
# ws_url_base = 'wss://chatbot2.behemoth.vn/cable'

# Initialize HTTP connection for GraphQL request
uri = URI(graphql_url)
https = Net::HTTP.new(uri.host, uri.port)
https.use_ssl = true
https.ssl_version = :TLSv1_2
request = Net::HTTP::Post.new(uri)

# Setup headers
request['accept'] = 'application/json, text/plain, */*'
request['accept-language'] = 'en-US,en;q=0.9,vi;q=0.8'
request['content-type'] = 'application/json'
request['dnt'] = '1'
request['origin'] = 'https://lp.mirai-japan.co.jp'
request['priority'] = 'u=1, i'
request['referer'] = 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw'
request['sec-ch-ua'] = '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
request['sec-ch-ua-mobile'] = '?0'
request['sec-ch-ua-platform'] = '"macOS"'
request['sec-fetch-dest'] = 'empty'
request['sec-fetch-mode'] = 'cors'
request['sec-fetch-site'] = 'cross-site'
request['user-agent'] =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

# Set up body for the GraphQL query
request.body = {
  query: <<~GQL,
    query fetchScenario($url: String!, $userId: ID!, $ssid: String, $scenarioId: String) {
      fetchScenario(userId: $userId, url: $url, ssid: $ssid, scenarioId: $scenarioId) {
        rootNodeUid
        supportUiEnable
        nodes {
          body
          label
          nextNodeUid
          nodeType
          uid
        }
        id
        ssid
        sdata
        shopId
      }
    }
  GQL
  variables: {
    url: url,
    userId: user_id,
    ssid: nil,
    scenarioId: scenario_id
  }
}.to_json

# Send the GraphQL request and handle the response
begin
  response = https.request(request)
  response_body = JSON.parse(response.read_body)
  puts "GraphQL response: #{response_body}"

  ssid = response_body.dig('data', 'fetchScenario', 'ssid')
  if ssid
    puts "Successfully retrieved ssid: #{ssid}"
  else
    puts "Failed to find ssid in response: #{response_body}"
    exit
  end
rescue StandardError => e
  puts "Error sending GraphQL request: #{e.message}"
  exit
end

# Connect to WebSocket and send emit messages
ws_url = "#{ws_url_base}?dataAccount=#{user_id}&ssid=#{ssid}"
number = rand(1_000_000...10_000_000)

emit_subscribe = {
  command: 'subscribe',
  identifier: "{\"channel\":\"SystemMessageChannel\",\"dataAccount\":\"#{user_id}\",\"ssid\":\"#{ssid}\"}"
}

emit_crawler = {
  command: 'message',
  identifier: "{\"channel\":\"SystemMessageChannel\",\"dataAccount\":\"#{user_id}\",\"ssid\":\"#{ssid}\"}",
  data: JSON.dump({
                    inputs: {
                      mei: { type: 'string', value: 'ユナイテッドゲート' },
                      sei: { type: 'string', value: 'テスト' },
                      tel: { type: 'string', value: '***********' },
                      url: { type: 'string', value: url },
                      mail: { type: 'string', value: "test.unitedgate+#{number}@test.test" },
                      offer: { type: 'string', value: '7日間お試しサイズのまま注文する' },
                      meifuri: { type: 'string', value: 'ユナイテッドゲート' },
                      seifuri: { type: 'string', value: 'テスト' },
                      zipcode: { type: 'string', value: '1500031' },
                      address02: { type: 'string', value: '２ーハイフン' },
                      credit_card: { type: 'string',
                                     value: '********************************************************************************************************************************************************************************' },
                      payment_method: { type: 'string', value: '後払い' } #  or クレジットカード（手数料0円）
                    },
                    className: 'MiraiSubmit',
                    outputs: { data: { error: '', message: '' } },
                    userId: user_id,
                    ssid: ssid,
                    action: 'task_runner'
                  })
}

# Sử dụng EventMachine để mở WebSocket
EM.run do
  ws = Faye::WebSocket::Client.new(ws_url, [], {
                                     tls: {
                                       ssl_version: :TLSv1_2,
                                       verify: false
                                     }
                                   })

  ws.on :open do |_event|
    puts "Connected to WebSocket: #{ws_url}"
    ws.send(emit_subscribe.to_json)
    puts "Sent subscribe: #{emit_subscribe}"
    ws.send(emit_crawler.to_json)
    puts "Sent crawler: #{emit_crawler}"
  end

  ws.on :message do |event|
    puts "Message received: #{event.data}"
  end

  ws.on :error do |event|
    puts "WebSocket error: #{event.message}"
    puts event.backtrace.join("\n") if event.respond_to?(:backtrace)
  end

  ws.on :close do |event|
    puts "WebSocket closed: #{event.code}, #{event.reason}"
    EM.stop
  end
rescue StandardError => e
  puts "Error initializing WebSocket: #{e.message}"
  puts e.backtrace.join("\n")
  EM.stop
end
