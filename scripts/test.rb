require 'uri'
require 'json'
require 'net/http'
require 'websocket-client-simple'

# Input
## Local
graphql_url = 'http://localhost:3000/graphql'
user_id = 'a86620d9-6815-4e61-a5f1-855a527dc2a9'
scenario_id = '2afe6121-eb0e-49a8-b765-718ee3e8600f'
url = 'https://pr.orbis.co.jp/cosmetics/udot/413-1/?test=ug'
ws_url_base = 'ws://localhost:9090/cable'

uri = URI(graphql_url)
http = Net::HTTP.new(uri.host, uri.port)
request = Net::HTTP::Post.new(uri)

# Settings headers
request['Accept'] = 'application/json, text/plain, */*'
request['Accept-Language'] = 'en-US,en;q=0.9,vi;q=0.8'
request['Connection'] = 'keep-alive'
request['Content-Type'] = 'application/json'
request['DNT'] = '1'
request['Origin'] = 'https://pr.orbis.co.jp/'
request['Referer'] = 'https://pr.orbis.co.jp/'
request['Sec-Fetch-Dest'] = 'empty'
request['Sec-Fetch-Mode'] = 'cors'
request['Sec-Fetch-Site'] = 'same-site'
request['User-Agent'] =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
request['sec-ch-ua'] = '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
request['sec-ch-ua-mobile'] = '?0'
request['sec-ch-ua-platform'] = '"macOS"'

request.body = {
  query: <<~GQL,
    query fetchScenario($url: String!, $userId: ID!, $ssid: String, $scenarioId: String) {
      fetchScenario(userId: $userId, url: $url, ssid: $ssid, scenarioId: $scenarioId) {
        rootNodeUid
        supportUiEnable
        progressBarEnable
        nodes {
          body
          label
          nextNodeUid
          nodeType
          uid
        }
        id
        ssid
        sdata
        shopId
      }
    }
  GQL
  variables: {
    url: url,
    userId: user_id,
    ssid: nil,
    scenarioId: scenario_id
  }
}.to_json

begin
  response = http.request(request)
  response_body = JSON.parse(response.read_body)

  ssid = response_body.dig('data', 'fetchScenario', 'ssid')
  if ssid
    puts "Lấy được ssid: #{ssid}"
  else
    puts "Không tìm thấy ssid trong response: #{response_body}"
    exit
  end
rescue StandardError => e
  puts "Lỗi khi gửi yêu cầu GraphQL: #{e.message}"
  exit
end

ws_url = "#{ws_url_base}?dataAccount=#{user_id}&ssid=#{ssid}"
number = rand(1_000_000...10_000_000)

emit_subscribe = {
  command: 'subscribe',
  identifier: JSON.dump({ channel: 'SystemMessageChannel', dataAccount: user_id, ssid: ssid })
}

emit_crawler = {
  command: 'message',
  identifier: JSON.dump({ channel: 'SystemMessageChannel', dataAccount: user_id, ssid: ssid }),
  data: JSON.dump({
                    inputs: {
                      mei: { type: 'string', value: 'テスト' },
                      sei: { type: 'string', value: 'テスト' },
                      tel1: { type: 'string', value: '051' },
                      tel2: { type: 'string', value: '123' },
                      tel3: { type: 'string', value: '1234' },
                      url: { type: 'string', value: 'https://sb1.genpeiseiyaku.com/ab/gHLcGxwFwO_PNWlVbqSw' },
                      mail: { type: 'string', value: '<EMAIL>' },
                      meifuri: { type: 'string', value: 'テスト' },
                      seifuri: { type: 'string', value: 'テスト' },
                      zipcode: { type: 'string', value: '1500031' },
                      address02: { type: 'string', value: '1234' },
                      address03: { type: 'string', value: '1234' },
                      password: { type: 'string', value: 'Aaaa1234' },
                      credit_card: { type: 'string',
                                     value: '************************************************************************************************************************************************************************************************' },
                      payment_method: { type: 'string', value: 'NP後払い（コンビニ支払い、銀行振込、郵便振替）' } # 'クレジットカード' } # ＮＰ後払い（コンビニ支払い、銀行振込、郵便振替）
                    },
                    className: 'TestRunner',
                    outputs: { data: { error: '', message: '' } },
                    userId: 'a86620d9-6815-4e61-a5f1-855a527dc2a9',
                    ssid: 'scenario-2afe6121-eb0e-49a8-b765-718ee3e8600f-60a1068751ea275d7759884d8b0149e0',
                    action: 'task_runner'
                  })
}

# Connect WebSocket
begin
  ws = WebSocket::Client::Simple.connect(ws_url)

  ws.on :open do
    puts "Connected to WebSocket: #{ws_url}"
    ws.send(emit_subscribe.to_json)
    puts "Sent emit subscribe: #{emit_subscribe}"

    ws.send(emit_crawler.to_json)
    puts "Sent emit crawler: #{emit_crawler}"
  end

  ws.on :message do |msg|
    puts "Received message from server: #{msg.data}"
  end

  ws.on :error do |e|
    puts "WebSocket error: #{e.message}"
  end

  ws.on :close do
    puts 'WebSocket connection closed'
  end

  sleep
rescue StandardError => e
  puts "Error connecting to WebSocket: #{e.message}"
end
