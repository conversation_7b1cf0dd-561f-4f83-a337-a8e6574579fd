require 'uri'
require 'json'
require 'net/http'
require 'websocket-client-simple'

# Input
graphql_url = 'http://localhost:3000/graphql'
user_id = 'a86620d9-6815-4e61-a5f1-855a527dc2a9'
scenario_id = 'b60732e7-9611-4631-a173-45466b2857aa'
url = 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw'
ws_url_base = 'ws://localhost:9090/cable'

uri = URI(graphql_url)
http = Net::HTTP.new(uri.host, uri.port)
request = Net::HTTP::Post.new(uri)

# Settings headers
request['Accept'] = 'application/json, text/plain, */*'
request['Accept-Language'] = 'en-US,en;q=0.9,vi;q=0.8'
request['Connection'] = 'keep-alive'
request['Content-Type'] = 'application/json'
request['DNT'] = '1'
request['Origin'] = 'https://lp.mirai-japan.co.jp'
request['Referer'] = 'https://lp.mirai-japan.co.jp/'
request['Sec-Fetch-Dest'] = 'empty'
request['Sec-Fetch-Mode'] = 'cors'
request['Sec-Fetch-Site'] = 'same-site'
request['User-Agent'] =
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
request['sec-ch-ua'] = '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
request['sec-ch-ua-mobile'] = '?0'
request['sec-ch-ua-platform'] = '"macOS"'

request.body = {
  query: <<~GQL,
    query fetchScenario($url: String!, $userId: ID!, $ssid: String, $scenarioId: String) {
      fetchScenario(userId: $userId, url: $url, ssid: $ssid, scenarioId: $scenarioId) {
        rootNodeUid
        supportUiEnable
        progressBarEnable
        nodes {
          body
          label
          nextNodeUid
          nodeType
          uid
        }
        id
        ssid
        sdata
        shopId
      }
    }
  GQL
  variables: {
    url: url,
    userId: user_id,
    ssid: nil,
    scenarioId: scenario_id
  }
}.to_json

begin
  response = http.request(request)
  response_body = JSON.parse(response.read_body)

  ssid = response_body.dig('data', 'fetchScenario', 'ssid')
  if ssid
    puts "Lấy được ssid: #{ssid}"
  else
    puts "Không tìm thấy ssid trong response: #{response_body}"
    exit
  end
rescue StandardError => e
  puts "Lỗi khi gửi yêu cầu GraphQL: #{e.message}"
  exit
end

ws_url = "#{ws_url_base}?dataAccount=#{user_id}&ssid=#{ssid}"
number = rand(1_000_000...10_000_000)

emit_subscribe = {
  command: 'subscribe',
  identifier: JSON.dump({ channel: 'SystemMessageChannel', dataAccount: user_id, ssid: ssid })
}

emit_crawler = {
  command: 'message',
  identifier: JSON.dump({ channel: 'SystemMessageChannel', dataAccount: user_id, ssid: ssid }),
  data: JSON.dump({
                    inputs: {
                      mei: { type: 'string', value: 'ユナイテッドゲート' },
                      sei: { type: 'string', value: 'テスト' },
                      tel: { type: 'string', value: '***********' },
                      url: { type: 'string', value: 'https://lp.mirai-japan.co.jp/ab/XsckiNyFTZTBjIgOaBHw' },
                      mail: { type: 'string', value: "test.unitedgate+#{number}@test.test" },
                      offer: { type: 'string', value: '7日間お試しサイズのまま注文する' },
                      meifuri: { type: 'string', value: 'ユナイテッドゲート' },
                      seifuri: { type: 'string', value: 'テスト' },
                      zipcode: { type: 'string', value: '1500031' },
                      address02: { type: 'string', value: '２ーハイフン' },
                      credit_card: { type: 'string',
                                     value: '********************************************************************************************************************************************************************************' },
                      payment_method: { type: 'string', value: '後払い' } #  or クレジットカード（手数料0円）
                    },
                    className: 'MiraiSubmit',
                    outputs: { data: { error: '', message: '' } },
                    userId: user_id,
                    ssid: ssid,
                    action: 'task_runner'
                  })
}

# Connect WebSocket
begin
  ws = WebSocket::Client::Simple.connect(ws_url)

  ws.on :open do
    puts "Connected to WebSocket: #{ws_url}"
    ws.send(emit_subscribe.to_json)
    puts "Sent emit subscribe: #{emit_subscribe}"

    ws.send(emit_crawler.to_json)
    puts "Sent emit crawler: #{emit_crawler}"
  end

  ws.on :message do |msg|
    puts "Received message from server: #{msg.data}"
  end

  ws.on :error do |e|
    puts "WebSocket error: #{e.message}"
  end

  ws.on :close do
    puts 'WebSocket connection closed'
  end

  sleep
rescue StandardError => e
  puts "Error connecting to WebSocket: #{e.message}"
end
