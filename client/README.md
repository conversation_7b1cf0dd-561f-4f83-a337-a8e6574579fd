# README

## Node Version

**_20.11.0_**

## Install Dependencies

```shell
yarn install
```

## Run development environment

```shell
yarn dev
```

## Complie

- Copy Env Sample

```
 SDK_HOST_URL=https://d2w53g1q050m78.cloudfront.net/lacluluxyz/uploads
 API_BASE_URL=https://chatbot.unicorncart.jp
 NODE_ENV="development"
 ID_CHAT_BOT="chatbot-widget"
 URL_CRAWLER=
 WEBSOCKET_HOST=ws://chatbot.unicorncart.jp/cable
 DATA_ACCOUNT=44ded835-cc66-4e31-b1ee-ed049e23c099
 ANALY_P_TOOLS=https://demo.unitedgate.co.jp/analysts/analycore.js

```

- Add the environment variable `SDK_HOST_URL` as the directory link to upload files.In the local environment, the `SDK_HOST_URL` will be the URL running on your machine, for example, `http://localhost:8080`.

- Run Command

```shell
yarn install
```

```shell
yarn build
```

- Afterward, navigate to the 'dist' directory, retrieve the files sdk.min.js and chatbot.js (for the local environment, the chatbot.js file does not need to be uploaded.), and upload them to an HTTP Server or Cloud Storage. Utilize the uploaded URL of the sdk.min.js file for `url_param`.

- Change the `version` with each deployment - When the value of this parameter changes, the browser will treat it as a different URL and reload the file from the server instead of using the cached version. You can get the latest version by using current time to seconds like this
  In browser console:

```
Date.now()
```

- In case `url_param` already includes a version in the URL, there is no need to set the version for the `widgetTag.src` parameter. In this case, `widgetTag.src` will be `${url_param}`.

- `user_id` User ID when created ( user must have Scenario and the url in Scenario is the url corresponding to the url expected to embed the script so that the script can run)

### Sample Script

```
(function () {
if (window !== window.parent) return;

var widgetTag = document.createElement("script");
const version = new Date().getTime()
widgetTag.src   = `${url_param}?v=${version}`;
widgetTag.async = true;
widgetTag.dataset.account = `${user_id}`;
document.body.appendChild(widgetTag);
})();

```

## Use Test

### 下記の手順でファイル準備や実装で確認できると思います。

1. laclulu のどっかのテンプレート設定画面でアクセスをしていただく。https://laclulu.xyz/admin/templates/23/edit
2. こちらの画面でファイルアップロードで compiled されたの chatbot.js ファイルをアップロードして頂く

![Screenshot 2024-05-27 at 17 11 38](https://github.com/kero-chan/united-cart-chatbot/assets/********/742d5f14-70fc-4ff1-b3cc-5e9784d10e6c)

![Screenshot 2024-05-27 at 17 21 46](https://github.com/kero-chan/united-cart-chatbot/assets/********/b3eb09a0-ea90-4cb6-8159-d9687ca7dc96)

3. アップロードされたの chatbot.js ファイルを ファイル閲覧をクリックで URL 取得できます。
4. 上記取得できる URL をコーピーしまして、compile されたの sdk.min.js ファイルの中身ので.../chatbot.js のような URL を上書きしていただいて、そして、また laclulu のどっかのテンプレート設定画面で修正したの sdk.min.js ファイルをアップロードし、URL 取得していただいて
5. 上記取得した URL は widgetTag.src に入れる
6. ブラウザーで確認
