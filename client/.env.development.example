

# local

 SDK_HOST_URL=http://localhost:8080
 API_BASE_URL=http://localhost:3000
 NODE_ENV="development"
 ID_CHAT_BOT="chatbot-widget"
 URL_CRAWLER=https://iqdum.jp/shopping/lp.php?p=coupon_saeko_pgs_dev231120
 WEBSOCKET_HOST=ws://localhost:9090/cable
 DATA_ACCOUNT=56726e71-1806-4281-8f93-36163e49813c
 DATA_SCENARIO=
 ANALY_P_TOOLS=https://dev-api.uganalytics.net/analy/release/analycore.min.js

# local test actual equipment
#  SDK_HOST_URL=http://**************:8080
#  API_BASE_URL=http://**************:3000
#  NODE_ENV="development"
#  ID_CHAT_BOT="chatbot-widget"
#  URL_CRAWLER=https://iqdum.jp/shopping/lp.php?p=coupon_saeko_pgs_dev231120
#  WEBSOCKET_HOST=ws://localhost:9090/cable
#  DATA_ACCOUNT=56726e71-1806-4281-8f93-36163e49813c
#  ANALY_P_TOOLS=https://dev-api.uganalytics.net/analy/release/analycore.min.js