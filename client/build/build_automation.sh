#!/bin/bash
set -e

export $(grep -v '^#' .env | xargs)

BUILD_DIR="src/automation_build"
mkdir -p $BUILD_DIR

# Obfuscate JavaScript files
for file in src/automation/*.js; do
  filename=$(basename "$file" .js)
  outfile="$BUILD_DIR/${filename}.js"

  echo "Obfuscating $file => $outfile"
  npx javascript-obfuscator "$file" \
    --output "$outfile" \
    --compact true \
    --control-flow-flattening true
done

gsutil cp src/automation_build/* gs://unicorncart-chatbot-assets/automation/

echo "Creating cdn.txt with script tags..."
# Remove old cdn.txt if it exists

# Create for each obfuscated file

for file in src/automation/*.js; do
  filename=$(basename "$file" .js)
  sed -e "s|__FILE_NAME__|${filename}|g" \
      -e "s|__SDK_TIMESTAMP__|$(date +%s)|g" src/automation/template.tpl > src/automation_build/${filename}.script.js
done
