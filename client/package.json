{"name": "united-cart-chat-bot", "private": true, "version": "0.0.0", "main": "index.js", "scripts": {"dev": "webpack serve --open --mode=development", "bot.build": "export NODE_ENV=production && webpack build --config webpack.config.js --color", "sdk.build": "export NODE_ENV=production && webpack build --config sdk.webpack.config.js --color", "build": "yarn bot.build && yarn sdk.build", "preview": "vite preview"}, "keywords": ["sdk"], "dependencies": {"@babel/core": "^7.23.9", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-regular-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/vue-fontawesome": "^3.0.5", "@vuepic/vue-datepicker": "^7.4.1", "@vueuse/core": "^10.7.2", "actioncable": "^5.2.8-1", "apexcharts": "^3.45.1", "axios": "^1.4.0", "babel-loader": "^9.1.3", "bootstrap": "^5.3.2", "bootstrap-vue-next": "^0.15.5", "buffer": "^6.0.3", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "dotenv-webpack": "^8.0.1", "element-plus": "^2.5.1", "graphql": "^16.8.0", "graphql-tag": "^2.12.6", "html-webpack-plugin": "^5.6.0", "javascript-obfuscator": "^4.1.0", "jp-postalcode-lookup": "^0.0.2", "jp-prefectures": "^2.0.1", "lodash": "^4.17.21", "pinia": "^2.1.7", "sass": "^1.69.7", "sass-loader": "^14.1.0", "simple-notify": "^0.5.5", "uuid": "^9.0.1", "v-calendar": "^2.4.2", "vanilla-autokana": "^1.3.0", "vue": "^3.3.11", "vue-loader": "^17.4.2", "vue-select": "^4.0.0-beta.6", "vue-style-loader": "^4.1.3", "vue3-apexcharts": "^1.4.4", "wanakana": "^5.3.1", "webpack": "^5.90.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.2", "webpack-obfuscator": "^3.5.1", "yup": "^1.3.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8"}}