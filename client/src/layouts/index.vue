<template>
  <div>
    <Loading v-if="loading"></Loading>

    <AppHeader class="app-header" />

    <Progress />

    <div
      v-if="ready"
      class="px-3 app-content"
      :class="{ active: isActive }"
      ref="contentContainer"
    >
      <div v-for="(node, index) in nodesMapping" :key="index" class="py-3">
        <Module
          :label="node.label"
          :body="node.body"
          :nextNodeUid="node.nextNodeUid"
          :nodeUid="node.uid"
          :nodeType="node.nodeType"
          :index="index"
          :id="node.id"
          @change="scrollEl"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from "vue";
import Module from "Components/Module.vue";
import AppHeader from "Components/layouts/AppHeader.vue";
import Progress from "Components/modules/Progress.vue";
import { useScenarioStore } from "@/stores/scenario";
import { storeToRefs } from "pinia";
import { useCartStore } from "@/stores/cart";
import useScenarioSetting from "@/composables/chatSetting";
import Loading from "Components/layouts/Loading.vue";
import { useGlobalStore } from "@/stores/global";
import { NODE_TYPE_HIDDEN, NODE_TYPE } from "@/ultilities/constants";
import { handleScrollIntoView } from "@/ultilities/helper.js";

//// Define Store
const cartStore = useCartStore();
const { cart } = storeToRefs(cartStore);

const scenarioStore = useScenarioStore();
const { scenario, nodes, container } = storeToRefs(scenarioStore);

const { settingDesign } = useScenarioSetting();

const globalStore = useGlobalStore();
const { loading } = storeToRefs(globalStore);

//// Define Ref
const ready = ref(false);
const isActive = ref(false);
const contentContainer = ref(null);

//// COMPUTED
const nodesMapping = computed(() => {
  return nodes.value.filter(
    (n) => !Object.values(NODE_TYPE_HIDDEN).includes(n.nodeType)
  );
});

// WATCH
watch(
  () => scenario.value,
  async (value) => {
    if (Object.keys(scenario.value).length > 0) {
      if (!nodes.value.length) {
        scenarioStore.nextNode(scenario.value.rootNodeUid);
      } else {
      }
      ready.value = true;
      await nextTick();
      container.value = contentContainer.value;
      setTimeout(() => {
        scrollToBottom();
      }, 500);

      if (value.progressBarEnable) {
        contentContainer.value.style.top = "70px";
      }
    }
  }
);

//  onMounted
onMounted(async () => {
  cart.value.url =
    process.env.NODE_ENV == "development"
      ? process.env.URL_CRAWLER
      : document.location.href;

  if (Object.keys(scenario.value).length > 0) {
    if (!nodes.value.length) {
      scenarioStore.nextNode(scenario.value.rootNodeUid);
      ready.value = true;
    }
  }
});

// FUNCTION
const scrollToBottom = () => {
  const container = contentContainer.value;

  const nodeImages = nodesMapping.value.find(
    (n) => n.body.settings[0].type === "images"
  );

  if (nodeImages) {
    const el = document.getElementById(nodeImages.uid);
    handleScrollIntoView(el, { behavior: "smooth", block: "start" });

    return;
  }

  const lastNode = nodesMapping.value[nodesMapping.value.length - 1];

  if (!scenarioStore.getLastVariable(lastNode.body.settings)) {
    if (lastNode.nodeType === NODE_TYPE.MESSAGE) {
      if (
        nodesMapping.value.length === 1 &&
        lastNode.body.settings[0].type === "images"
      ) {
        return;
      }
      scenarioStore.scrollLastMessage(1500);
    } else {
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  } else {
    if (lastNode.body?.repeatable) {
      if (lastNode.nodeType === NODE_TYPE.MESSAGE) {
        scenarioStore.scrollLastMessage(1500);
      } else {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }
    } else {
      setTimeout(() => {
        scenarioStore.scrollToNode(lastNode);
      }, 100);
    }
  }
};

function containsVariableWithValue(settings, value) {
  if (!settings || !Array.isArray(settings)) {
    return false;
  }

  for (let setting of settings) {
    if (setting.variable === value) {
      return true;
    }

    if (
      setting.settings &&
      containsVariableWithValue(setting.settings, value)
    ) {
      return true;
    }
  }

  return false;
}

function repeatableVariable(value) {
  const nodesWithVariable = nodesMapping.value.filter((node) => {
    return containsVariableWithValue(node.body.settings, value);
  });
  const repeatableNodes = nodesWithVariable.filter((v) => v.body?.repeatable);

  return repeatableNodes;
}

function scrollEl(value) {
  isActive.value = value.active;
  let nodeVariable = [];
  if (value.id) {
    nodeVariable = repeatableVariable(value.id);
  }
  setTimeout(() => {
    if (value.id && !nodeVariable.length) {
      const el = document.getElementById(value.id);
      handleScrollIntoView(el);
    }
  }, 1000);
}
</script>

<style lang="scss" scoped>
.app-header {
  background-color: v-bind("settingDesign.headerBgColor");
  color: v-bind("settingDesign.titleTxtColor");
  position: fixed;
  top: 0;
  z-index: 99999;
}

.app-content {
  position: relative;
  top: 50px;
  scroll-behavior: smooth;
  overflow-y: auto;
  max-height: calc(100vh);
}

.active {
  max-height: calc(100vh - 350px) !important;
  padding: 50px;
}

.form-input {
  background-color: v-bind("settingDesign.formBgColor");
  border-color: v-bind("settingDesign.formBorderColor");
}

.form-input :deep(.input-s) {
  border-color: v-bind("settingDesign.formInputBorderColor");
  background-color: v-bind("settingDesign.chatWindowBgColor");
}

.form-input :deep(.form-btn-submit) {
  width: 100%;
  margin-top: 1rem !important;
  background-color: v-bind("settingDesign.formBtnBgColor");
  color: v-bind("settingDesign.formBtnTxtColor");
  border: none;
  border-radius: 60px;
}

.form-input :deep(.form-btn-submit:hover) {
  opacity: 0.8;
}

.form-input :deep(.chat-bot-select) {
  background-color: v-bind("settingDesign.optionBgColor");
  color: v-bind("settingDesign.optionTxtColor");
}

.form-input :deep(.chat-bot-select:active) {
  background-color: v-bind("settingDesign.optionActiveBgColor");
  color: v-bind("settingDesign.optionActiveTxtColor");
}
</style>
