import axios from "axios";
// ==========UTILITIES=========
import { get } from "lodash";
import Toast from "@/ultilities/toast";

// ==========STORE=========
// import { useAuthStore } from "./stores/auth";
import { useGlobalStore } from "@/stores/global";

import { print } from "graphql";

const BASE_URL = `${process.env.API_BASE_URL}/graphql`;
const BASE_CRAWLER_URL = `${process.env.TASK_RUNNER_URL}/api/v1/tasks`;
const api = axios.create({
  timeout: 30000,
});

function hideLoading(id) {
  const globalStore = useGlobalStore();
  globalStore.removeRequest(id);
}

api.interceptors.request.use(
  function (config) {
    // const globalStore = useGlobalStore();
    // globalStore.validationErrors = {};
    // globalStore.errorMessage = "";

    // const authStore = useAuthStore();
    // const token = authStore.token;

    // if (token) {
    //   config.headers["BhmAIO-Authorization"] = `Bearer ${token}`;
    // }

    // if (config.loading) {
    //   config.id = v4();

    //   globalStore.addRequest(config.id);
    // }

    return config;
  },
  function (error) {
    // Do something with request error
    // if (error.config.loading) hideLoading(error.config.id);

    Toast.error({ title: error.message });

    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  function (response) {
    // if (response.config.loading) hideLoading(response.config.id);

    // Do something with response data
    const errors = response.data.errors;

    if (errors && errors.length > 0) {
      const errorMessage = get(errors[0], "message") || "An error occurred";

      if (response.config.toast) Toast.error({ title: errorMessage });

      const findError = errors[0];

      const globalStore = useGlobalStore();
      globalStore.validationErrors = findError?.errors || {};
    } else if (response.data) {
      const successMessage = get(
        response.data.data,
        `${Object.keys(response.data.data)[0]}.message`
      );

      if (response.config.toast) Toast.success({ title: successMessage });

      return response.data.data;
    }
  },
  function (error) {
    // if (error.config.loading) hideLoading(error.config.id);

    // const errCode = get(error, "response.status");

    const errorMessage =
      get(error, "response.data.errors[0].message") || "An error occurred";

    if (error.config.toast) Toast.error({ title: errorMessage });

    return Promise.reject(error);
  }
);

export default function (
  query,
  variables,
  options = {
    loading: true,
    toast: false,
  }
) {
  switch (options.requestType) {
    case "upload":
      const config = {
        header: {
          "Content-Type": "multiple/form-data",
        },
        baseURL: process.env.API_BASE_URL,
        timeout: 300000,
      };

      return api.post("/upload", variables.data, {
        ...config,
        ...options,
      });
    case "crawler":
      const configCrawler = {
        header: {
          "Content-Type": "application/json; charset=utf-8",
        },
        baseURL: BASE_CRAWLER_URL,
        timeout: 300000,
      };
      return api.post("/crawler_data", variables, {
        ...configCrawler,
        ...options,
      });
    default:
      return api.post(
        BASE_URL,
        {
          query: print(query),
          variables: variables,
        },
        options
      );
  }
}
