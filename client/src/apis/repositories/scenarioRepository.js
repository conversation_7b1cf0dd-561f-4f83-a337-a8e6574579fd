import api from "@/apis/axios";

import { fetchScenarioDetails, fetchScheduleDateSQL } from "@/apis/resolvers";
import { handleResponseResult } from "@/apis/mutations";

export default {
  fetchScenario(
    url,
    userId,
    ssid,
    scenarioId,
    options = { loading: true, toast: false }
  ) {
    return api(
      fetchScenarioDetails,
      { url, userId, ssid, scenarioId },
      options
    );
  },

  activeCrawler(
    crawlerInfo,
    options = { loading: true, toast: false, requestType: "crawler" }
  ) {
    return api(_, crawlerInfo, options);
  },

  handleResponse(input, options = { loading: true, toast: false }) {
    return api(handleResponseResult, { input }, options);
  },

  fetchScheduleDate(
    daysAfterCurrent,
    rangeDays,
    options = { loading: true, toast: false }
  ) {
    return api(fetchScheduleDateSQL, { daysAfterCurrent, rangeDays }, options);
  },
};
