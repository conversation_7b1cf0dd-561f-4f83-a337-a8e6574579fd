import gql from "graphql-tag";

export const fetchScenarioDetails = gql`
  query fetchScenario(
    $url: String!
    $userId: ID!
    $ssid: String
    $scenarioId: String
  ) {
    fetchScenario(
      userId: $userId
      url: $url
      ssid: $ssid
      scenarioId: $scenarioId
    ) {
      rootNodeUid
      supportUiEnable
      progressBarEnable
      nodes {
        body
        label
        nextNodeUid
        nodeType
        uid
      }
      id
      ssid
      sdata
      shopId
    }
  }
`;

export const fetchScheduleDateSQL = gql`
  query scheduledDateOption($daysAfterCurrent: String, $rangeDays: String) {
    scheduledDateOption(
      daysAfterCurrent: $daysAfterCurrent
      rangeDays: $rangeDays
    )
  }
`;
