import gql from "graphql-tag";

export const fetchScenarioSetting = gql`
  query fetchScenarioSetting($userId: ID!, $url: String!, $scenarioId: String) {
    fetchScenarioSetting(userId: $userId, url: $url, scenarioId: $scenarioId) {
      scenarioDesignSetting {
        chatDesignThemeId
        chatWindowBgColor

        cssCustomize
        javascriptCustomize

        customerMsgBodyBgColor
        customerMsgBodyTxtColor

        dateSystemMessageTxtColor

        formBgColor
        formBorderColor
        formBtnBgColor
        formBtnTxtColor
        formInputBorderColor

        headerBgColor
        titleTxtColor

        initiateBtnBgColor
        initiateBtnTxtColor

        messageInputColor

        operatorMsgBodyBgColor
        operatorMsgBodyTxtColor
        operatorNameColor

        optionActiveBgColor
        optionActiveTxtColor
        optionBgColor
        optionTxtColor

        themeColor

        progressBarBgColor
        progressPercentageBgColor
        progressPercentageColor
      }
      scenarioGeneralSetting {
        chatButtonTitle

        chatOperatorImgUrl
        chatOperatorName

        chatWindowPosition
        mobileChatWindowPosition

        chatWindowTitle
        chatWindowSubtitle
        confirmationText

        pcCustomChatWindowHeight
        pcCustomChatWindowWidth

        showButtonClose
        showChatStartButton
        startChatbotImmediately
        showConfirmationCloseModal
      }
    }
  }
`;
