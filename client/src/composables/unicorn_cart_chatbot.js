import { useGlobalStore } from "@/stores/global";
import { useScenarioStore } from "@/stores/scenario";
import { storeToRefs } from "pinia";

function openChatBot() {
  const globalStore = useGlobalStore();

  globalStore.openChatBot();
}

function closeChatBot() {
  const globalStore = useGlobalStore();

  globalStore.closeChatBot();
}

function handleResponse(data) {
  const scenarioStore = useScenarioStore();
  scenarioStore.handleResponseResult(data);
}

export class UnicornCartChatbot {
  constructor(object = {}) {
    this.object = object;
    this.open = openChatBot;
    this.close = closeChatBot;
    this.handleResponse = handleResponse;
  }

  // Getter cho showChatBot và chatBotStartImmediately
  get showChatBot() {
    const globalStore = useGlobalStore();
    const { showChatBot, chatBotStartImmediately } = storeToRefs(globalStore);

    return showChatBot.value || chatBotStartImmediately.value;
  }
  //Setter
  set creditCardInfo(obj = {}) {
    this.object.card_expired_month = obj.card_expired_month; //  get(obj, "card_expired_month");
    this.object.card_expired_year = obj.card_expired_year; // get(obj, "card_expired_year");
    this.object.card_name = obj.card_name; // get(obj, "card_name");
    this.object.card_number = obj.card_number; //  get(obj, "card_number");
    this.object.card_cvv = obj.card_cvv; //  get(obj, "card_cvv");
    this.object.card_brand = obj.card_brand; //  get(obj, "card_brand");
  }

  // Getter
  get creditCardInfo() {
    return {
      card_expired_month: this.object.card_expired_month || "", //  get(this.object, "card_expired_month");
      card_expired_year: this.object.card_expired_year || "", // get(this.object, "card_expired_year");
      card_name: this.object.card_name || "", // get(this.object, "card_name");
      card_number: this.object.card_number || "", // get(this.object, "card_number");
      card_cvv: this.object.card_cvv || "", // get(this.object, "card_cvv");
      card_brand: this.object.card_brand || "", // get(this.object, "card_brand");
    };
    // TODO: destroy creditCardInfo
  }

  get ssId() {
    const scenarioStore = useScenarioStore();
    const { scenario } = storeToRefs(scenarioStore);
    return scenario.value.ssid;
  }

  // next function
}
