import { ref, onMounted, computed, onBeforeMount } from "vue";
import { get, pick } from "lodash";
import * as yup from "yup";
import { useCartStore } from "@/stores/cart";
import { useScenarioStore } from "@/stores/scenario";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import FormLayout from "@/layouts/form.vue";
import {
  INPUT_TYPE,
  LAYOUT,
  ANALY_ACTION,
  NODE_TYPE,
} from "@/ultilities/constants";
import {
  PAYMENT_METHOD,
  CREDIT_CARD_SETTING,
  CREDIT_CARD_SETTING_CVV,
  CREDIT_CARD_SETTING_BRAND,
  CREDIT_CARD_SETTING_NAME,
} from "@/ultilities/paymentMethodConstants";
import { cloneDeep, merge } from "lodash";

export default function useCommon(ctx) {
  //Set validate Locale
  yup.setLocale({
    mixed: {
      required: "必須項目です",
    },
    string: {
      email: "形式が正しくありません",
    },
  });

  //// Define Variables Special
  const schema = yup.object({});

  //// Define Ref
  const props = ref({});
  const ready = ref(false);
  const dataCrawler = ref(null);

  const inputType = ref("");

  // const buttonText = ref("次へ");

  const currentVariables = ref([]);

  //// Define Store
  const cartStore = useCartStore();
  const { cart } = storeToRefs(cartStore);

  const scenarioStore = useScenarioStore();
  const { scenario, nodes, container } = storeToRefs(scenarioStore);

  const globalStore = useGlobalStore();
  const {
    requests,
    socketResult,
    address02OpenSuggestion,
    telOpenSuggestion,
    zipcodeOpenSuggestion,
    reValidationErrors,
  } = storeToRefs(globalStore);

  //// COMPUTED
  const visibleLoading = computed(() => requests.value.includes(uid.value));
  const uid = computed(() => props.value.nodeUid);
  const id = computed(() => props.value.id);
  const nextNodeUid = computed(() => props.value.nextNodeUid);
  const supportUiEnable = computed(() => scenario.value.supportUiEnable);
  const isLastNode = computed(() => nodes.value.at(-1)?.uid == uid.value);
  const buttonText = computed(() => {
    return isLastNode.value ? "次へ" : "更新";
  });

  const bodyType = computed(() => get(props.value.body, "type", ""));
  const bodySettings = computed(() => get(props.value.body, "settings", []));

  const moduleSetting = computed(() => {
    if (bodyType.value == "credit_card") {
      return fetchCardSettings(bodySettings.value[0]);
    }

    if (bodyType.value == "payment_method") {
      const settings = cloneDeep(PAYMENT_METHOD);
      const paymentMethodSettings = bodySettings.value[0];

      settings.forEach((setting) => {
        switch (setting.variable) {
          case "payment_method":
            setting.label = paymentMethodSettings?.payment_method?.label ?? "";
            setting.options =
              paymentMethodSettings?.payment_method?.options ?? [];
            break;
          case "credit_card":
            setting.showOn =
              paymentMethodSettings?.credit_card_form?.show_on || [];
            setting.settings = [];

            if (setting.showOn.length) {
              setting.settings = fetchCardSettings(
                paymentMethodSettings?.credit_card_form
              );
            }

            break;
        }
      });

      return settings;
    }

    return bodySettings.value;
  });

  const fieldLabel = computed(() => (field) => get(field, "label", ""));
  const fieldContent = computed(() => (field) => get(field, "content", ""));
  const fieldType = computed(() => (field) => get(field, "type", ""));
  const fieldVariable = computed(() => (field) => get(field, "variable", ""));
  const fieldOptions = computed(() => (field) => get(field, "options", []));
  const fieldRequired = computed(
    () => (field) => get(field, "required", false)
  );
  const fieldPlaceholder = computed(
    () => (field) => get(field, "placeholder", "")
  );
  const fieldLayout = computed(() => (field) => get(field, "layout", ""));

  // use only RadioButtonGrid
  const fieldEnableEditButton = computed(
    () => (field) => get(field, "enable_edit_button", false)
  );
  //// FUNCTION

  function fetchCardSettings(cardSetting = {}) {
    const settings = cloneDeep(CREDIT_CARD_SETTING);

    settings.forEach((setting) => {
      switch (setting.variable) {
        case "card_number":
          setting.label = cardSetting.number?.label ?? setting.label;
          setting.placeholder =
            cardSetting.number?.placeholder ?? setting.placeholder;
          break;
        case "card_expired":
          setting.label = cardSetting.expired?.label ?? setting.label;
          setting.settings.forEach((item) => {
            if (item.variable == "card_expired_month") {
              item.label = cardSetting.expired?.month?.label ?? item.label;
            } else {
              item.label = cardSetting.expired?.year?.label ?? item.label;
            }
          });
          break;
      }
    });

    if (cardSetting.name?.enabled) {
      const nameSettings = cloneDeep(CREDIT_CARD_SETTING_NAME);
      nameSettings.required = cardSetting.name?.required || false;
      nameSettings.label = cardSetting.name?.label ?? nameSettings.label;
      nameSettings.placeholder =
        cardSetting.name?.placeholder ?? nameSettings.placeholder;

      settings.splice(1, 0, nameSettings);
    }
    if (cardSetting.brand?.enabled) {
      const brandSettings = cloneDeep(CREDIT_CARD_SETTING_BRAND);
      brandSettings.required = cardSetting.brand?.required || false;
      brandSettings.options = cardSetting.brand?.options || [];
      brandSettings.label = cardSetting.brand?.label ?? brandSettings.label;
      settings.unshift(brandSettings);
    }
    if (cardSetting.cvv?.enabled) {
      const cvvSettings = cloneDeep(CREDIT_CARD_SETTING_CVV);
      cvvSettings.required = cardSetting.cvv?.required || false;
      cvvSettings.label = cardSetting.cvv?.label ?? cvvSettings.label;
      cvvSettings.placeholder =
        cardSetting.cvv?.placeholder ?? cvvSettings.placeholder;

      settings.push(cvvSettings);
    }

    return settings;
  }

  function handleFieldClick(field) {
    inputType.value = field;
    if (field === "address02") {
      zipcodeOpenSuggestion.value = false;
      telOpenSuggestion.value = false;
      address02OpenSuggestion.value = !address02OpenSuggestion.value;
      if (ctx)
        ctx.emit("change", {
          active: address02OpenSuggestion.value,
          id: field,
        });
    } else if (field === "zipcode") {
      address02OpenSuggestion.value = false;
      telOpenSuggestion.value = false;
      zipcodeOpenSuggestion.value = !zipcodeOpenSuggestion.value;
      if (ctx)
        ctx.emit("change", { active: zipcodeOpenSuggestion.value, id: field });
    } else if (field === "tel") {
      address02OpenSuggestion.value = false;
      zipcodeOpenSuggestion.value = false;

      telOpenSuggestion.value = !telOpenSuggestion.value;
      if (ctx)
        ctx.emit("change", { active: telOpenSuggestion.value, id: field });
    } else if (["tel1", "tel2", "tel3"].includes(field)) {
      address02OpenSuggestion.value = false;
      zipcodeOpenSuggestion.value = false;

      if (!telOpenSuggestion.value) {
        telOpenSuggestion.value = !telOpenSuggestion.value;
      }
      if (ctx)
        ctx.emit("change", { active: telOpenSuggestion.value, id: field });
    } else {
      address02OpenSuggestion.value = false;
      zipcodeOpenSuggestion.value = false;
      telOpenSuggestion.value = false;
      inputType.value = "";

      if (ctx) ctx.emit("change", { active: false, id: field });
    }
  }

  function setProps(value) {
    props.value = value;
  }

  function getFieldComponent(fieldType) {
    switch (fieldType) {
      case INPUT_TYPE.SELECT:
        return "b-form-select";
      case INPUT_TYPE.INPUT:
        return "b-form-input";
      case INPUT_TYPE.RADIO:
        return "b-form-radio-group";
      case INPUT_TYPE.DATE:
        return "DatePicker";
    }
  }

  function onUpdateNode() {
    scenarioStore.updateNode(id.value);
  }

  async function onClick() {
    validateModuleSettings(moduleSetting.value);
    const currentValues = getCurrentValues();

    if (!schema.isValidSync(currentValues)) {
      handleValidationError();
      await globalStore.setReValidationErrors(uid.value);
      return;
    }
    globalStore.clearReValidationErrors(uid.value);

    ctx.emit("change", { active: false, id: "" });

    globalStore.addRequest(uid.value); // add loading

    const buttonTextValue = buttonText.value;
    if (!isLastNode.value) onUpdateNode();

    await analyPutlogSubmit(buttonTextValue);

    scenarioStore.onNextNode(
      isLastNode,
      uid.value,
      nextNodeUid.value,
      id.value
    );
  }

  function validateModuleSettings(settings) {
    settings.forEach((field) => {
      if (field.settings) {
        validateModuleSettings(field.settings);
      } else {
        validateField(fieldVariable.value(field));
      }
    });
  }

  function createAnalyData() {
    return {
      name: `${scenario.value.shopId}_chatbot`,
      scenarioId: scenario.value.id,
      nodeUid: uid.value,
    };
  }
  function sanitizeCreditCardData() {
    const sanitizedData = cloneDeep(window.parent.creditCard);

    if (sanitizedData) {
      Object.keys(sanitizedData).forEach((key) => {
        if (sanitizedData[key] === "") {
          sanitizedData[key] = null;
        }
      });
      return sanitizedData;
    } else {
      return {};
    }
  }

  function getAllVariables(settings) {
    let variables = [];

    settings.forEach((setting) => {
      if (fieldVariable.value(setting)) {
        variables.push(fieldVariable.value(setting));
      }

      if (setting.settings && Array.isArray(setting.settings)) {
        variables = variables.concat(getAllVariables(setting.settings));
      }
    });

    return variables;
  }

  function getCurrentValues() {
    const creditCardData = sanitizeCreditCardData();
    const value = merge(creditCardData, cart.value);

    return pick(value, currentVariables.value);
  }

  function handleValidationError() {
    const arr = reValidationErrors.value.filter((v) => v === uid.value);
    if (!arr.length) {
      analyPutlogEvent(ANALY_ACTION.VALIDATION_ERROR);
    } else {
      analyPutlogEvent(ANALY_ACTION.RE_VALIDATION_ERROR);
    }
  }

  async function analyPutlogSubmit(buttonTextValue) {
    const firstNodeInput = nodes.value.filter((n) =>
      [NODE_TYPE.BUTTON, NODE_TYPE.INPUT].includes(n.nodeType)
    );
    const node = cloneDeep(
      scenario.value.nodes.find((n) => n.uid === uid.value)
    );
    const nodeType = node?.nodeType;

    if (buttonTextValue === "更新") {
      analyPutlogEvent(ANALY_ACTION.RESUCCESS);
    } else {
      await scenarioStore.analyPutLogEntry(firstNodeInput, uid.value, nodeType);
      analyPutlogEvent(ANALY_ACTION.SUCCESS);
    }
  }

  function analyPutlogEvent(event) {
    const analyData = createAnalyData();
    console.log(event, analyData.nodeUid);
    Analy.P.Input.putChatformLog(
      analyData.name,
      analyData.scenarioId,
      analyData.nodeUid,
      event
    );
  }

  function disabledButton() {
    const currentValues = pick(cart.value, currentVariables.value);

    const requiredKeys = Object.keys(schema.fields).filter((key) => {
      return schema.fields[key].tests.some((test) => {
        return (
          test.OPTIONS.name === "required" ||
          (test.OPTIONS.name === "min" &&
            test.OPTIONS.params &&
            test.OPTIONS.params.min === 1)
        );
      });
    });

    if (requiredKeys.length === 0) {
      return false;
    }

    const requiredSchema = yup.object(
      Object.fromEntries(
        requiredKeys.map((key) => {
          const isArray = schema.fields[key].type === "array";

          if (isArray) {
            return [key, yup.array().min(1, "必須項目です").required()];
          } else {
            return [key, yup.string().required()];
          }
        })
      )
    );

    return !requiredSchema.isValidSync(pick(currentValues, requiredKeys));
  }

  //Css mapping function
  function getWidthChildField(parent, child) {
    if (fieldLayout.value(parent) === LAYOUT.HORIZONTAL) {
      return "calc((100% / " + parent.ratios + " * " + child.ratio + ") - 1%)";
    } else {
      return "100%";
    }
  }

  function mappingFieldLayout(layout) {
    if (layout === LAYOUT.HORIZONTAL) {
      return "layout_horizontal";
    } else if (layout === LAYOUT.VERTICAL) {
      return "layout_vertical";
    }
  }

  function isGlobalValiable(variable) {
    return (
      window.parent.creditCard &&
      typeof window.parent.creditCard === "object" &&
      window.parent.creditCard.hasOwnProperty(variable)
    );
  }
  // Validate
  function validateField(variable) {
    if (!schema.fields[variable]) return;

    if (isGlobalValiable(variable)) {
      validateCreditCard(variable);
    } else {
      validateCart(variable);
    }
  }

  function validateCart(variable) {
    schema
      .validateAt(variable, cart.value)
      .then((_) => {
        globalStore.resetValidationErrors([variable]);
      })
      .catch((errors) => {
        globalStore.setValidationErrors(variable, errors.errors);
      });
  }

  function validateCreditCard(variable) {
    const creditCardData = sanitizeCreditCardData();

    schema
      .validateAt(variable, creditCardData)
      .then((_) => {
        globalStore.resetValidationErrors([variable]);
      })
      .catch((errors) => {
        globalStore.setValidationErrors(variable, errors.errors);
      });
  }

  function setDefaultSchema(item) {
    const variable = fieldVariable.value(item);

    if (!variable) return;

    const japanesePhoneRegex = /^0\d{7,11}$/;
    const furiRegex = /^([ァ-ンｧ-ﾝﾞﾟ]|ー)+$/;
    const re13to16digit = /^\d{13,16}$/;
    const re8to15digit = /^.{8,15}$/;
    const cardNameRegex = /^\S+\s+\S+(\s+\S+)*$/;
    const numberHalfwidth = /^[0-9]+$/;
    const stringHalfwidth = /^[a-zA-Z0-9 　]+$/;

    const zipcodeRegex = /^\d{7}$/;
    const telPartRegex = /^\d{0,4}$/;
    const emailRegex =
      /^[A-Za-z0-9]{1}[A-Za-z0-9_.+\-]*@{1}[A-Za-z0-9_.\-]{1,}\.[A-Za-z0-9]{1,}$/;

    const isRequired = fieldRequired.value(item);
    let validateString = isRequired
      ? yup.string().required()
      : yup.string().nullable();

    let validateArray = isRequired
      ? yup.array().min(1, "必須項目です").required()
      : yup.array().nullable();

    switch (variable) {
      case "seifuri":
      case "meifuri":
        validateString = validateString.matches(
          furiRegex,
          "カタカナで入力して下さい"
        );
        break;
      case "zipcode":
        validateString = validateString
          .matches(numberHalfwidth, "無効な形式")
          .matches(zipcodeRegex, "7桁の数値を入力してください");
        break;
      case "tel1":
      case "tel2":
      case "tel3":
        validateString = validateString
          .matches(numberHalfwidth, "無効な形式")
          .matches(telPartRegex, "最大4桁の数値を入力してください");
        break;

      case "card_cvv":
        validateString = validateString.matches(numberHalfwidth, "無効な形式");
        break;
      case "card_number":
        validateString = validateString.matches(
          re13to16digit,
          "13~16文字で入力してください\n数値を入力してください"
        );

        break;
      case "card_name":
        validateString = validateString
          .matches(cardNameRegex, "姓と名の間にスペースを入れてください")
          .matches(stringHalfwidth, "無効な形式");
        break;
      case "mail":
        validateString = validateString.matches(
          emailRegex,
          "* 正しいメールアドレスを入力してください"
        );
        break;
      case "password":
        validateString = validateString.matches(
          re8to15digit,
          "8~15文字で入力してください"
        );
        break;
      case "tel":
        validateString = validateString
          .matches(japanesePhoneRegex, "形式が正しくありません")
          .matches(numberHalfwidth, "無効な形式");
        break;
    }
    schema.fields[variable] =
      bodyType.value === "radio_buttons_multi_select"
        ? validateArray
        : validateString;
    schema._nodes.push(variable);
  }

  function defaultSetting(settings) {
    settings.forEach((item) => {
      const variable = fieldVariable.value(item);

      if (item.settings) return defaultSetting(item.settings); // case nested settings

      setDefaultSchema(item);
      currentVariables.value.push(variable);
    });
  }

  function getInputType(variable) {
    if (
      [
        "prefectures",
        "card_expired_month",
        "card_expired_year",
        "card_brand",
      ].includes(variable)
    )
      return;

    switch (variable) {
      case "tel":
      case "tel1":
      case "tel2":
      case "tel3":
      case "card_number":
      case "zipcode":
      case "card_cvv":
        return "tel"; // 電話番号用
      case "mail":
        return "email"; // メールアドレス用
      default:
        return "text"; // その他の入力フィールド
    }
  }

  //// OnMounted And Before Mount
  onBeforeMount(() => {
    defaultSetting(moduleSetting.value);
  });

  return {
    buttonText,
    cart,
    FormLayout,
    scenarioStore,
    ready,
    dataCrawler,
    visibleLoading,
    moduleSetting,
    fieldLabel,
    fieldContent,
    fieldType,
    fieldVariable,
    fieldOptions,
    fieldRequired,
    fieldPlaceholder,
    fieldLayout,
    isLastNode,
    schema,
    currentVariables,
    nodes,
    uid,
    socketResult,
    supportUiEnable,
    inputType,
    zipcodeOpenSuggestion,
    address02OpenSuggestion,
    telOpenSuggestion,
    container,
    fieldEnableEditButton,

    getInputType,
    getAllVariables,
    getFieldComponent,
    onClick,
    setProps,
    validateField,
    disabledButton,
    getWidthChildField,
    mappingFieldLayout,
    handleFieldClick,
    setDefaultSchema,
    // validateEmail,
  };
}
