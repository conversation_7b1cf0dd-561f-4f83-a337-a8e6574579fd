import { computed } from "vue";
import { useScenarioSettingStore } from "@/stores/scenarioSetting";
import { useGlobalStore } from "@/stores/global";
import { mobileCheck } from "@/ultilities/helper.js";
import { storeToRefs } from "pinia";
import { get } from "lodash";

export default function useScenarioSetting() {
  //// Define Constants
  const parent = window.parent;
  const chatBotElement = parent.document.getElementById("chatbot");
  const bodyTag = document.body;

  //// Define Store
  const globalStore = useGlobalStore();
  const scenarioSettingStore = useScenarioSettingStore();
  const { scenarioSetting } = storeToRefs(scenarioSettingStore);

  //// Define Computed
  const settingDesign = computed(() =>
    get(scenarioSetting.value, "scenarioDesignSetting", {})
  );
  const settingGeneral = computed(() =>
    get(scenarioSetting.value, "scenarioGeneralSetting", {})
  );
  const isSp = computed(() => mobileCheck());

  //// FUNCTION
  // run when open chat bot
  function initChatBot() {
    if (!globalStore.chatBotStartImmediately && !globalStore.showChatBot)
      return;

    bodyTag.style.backgroundColor = settingDesign.value.chatWindowBgColor;

    if (!chatBotElement) return;

    if (isSp.value) {
      chatBotElement.style.width = `100%`;
      chatBotElement.style.height = `100%`;
    } else {
      chatBotElement.style.width = `${settingGeneral.value.pcCustomChatWindowWidth}px`;
      chatBotElement.style.height = `${settingGeneral.value.pcCustomChatWindowHeight}px`;
    }
  }

  // run when close chat bot
  function initChatStartButton() {
    if (globalStore.showChatBot || globalStore.chatBotStartImmediately) return;

    bodyTag.style.backgroundColor = "transparent";

    if (!chatBotElement) return;
    if (settingGeneral.value.showChatStartButton) {
      chatBotElement.style.width = "400px";
      chatBotElement.style.height = "100px";
    } else {
      chatBotElement.style.width = "0";
      chatBotElement.style.height = "0";
    }

    chatBotElement.style.border = "none";
    chatBotElement.style.position = "fixed";
    chatBotElement.style.padding = "padding";
  }

  function setPosition() {
    if (!chatBotElement) return;

    const position = settingGeneral.value.chatWindowPosition;

    if (position === "bottom_right") {
      if (isSp.value) {
        chatBotElement.style.bottom = "0px";
        chatBotElement.style.right = "0px";
      } else {
        chatBotElement.style.bottom = "10px";
        chatBotElement.style.right = "10px";
      }
    }
  }

  // run when app mount
  function initSettingBot() {
    initChatBot();
    initChatStartButton();
    setPosition();
  }

  return {
    settingDesign,
    settingGeneral,

    initSettingBot,
    initChatBot,
    initChatStartButton,
  };
}
