import { ref, reactive } from "vue";
import { get } from "lodash";

import { UnicornCartChatbot } from "@/composables/unicorn_cart_chatbot";

export default function usePaymentMethod() {
  const creditCard = reactive(new UnicornCartChatbot());
  const creditCardInfo = ref(creditCard.creditCardInfo);

  function deleteCreditCard() {
    delete window?.parent?.creditCard;
  }

  // =========================================================================================
  function getOptions(field) {
    if (field.options) return field.options;

    switch (field.variable) {
      case "card_expired_month":
        return optionsMonth();
      case "card_expired_year":
        return optionsYear();
      default:
        return [];
    }
  }

  function optionsMonth() {
    return Array.from({ length: 12 }, (_, i) => String(i + 1).padStart(2, "0"));
  }

  function optionsYear() {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 11 }, (_, i) =>
      (currentYear - 2000 + i).toString()
    );
  }
  // =========================================================================================

  // =========================================================================================
  async function validateInput(field, value) {
    const fieldType = get(field, "variable", "");

    if (isCardField(fieldType)) {
      const sanitizedValue = sanitizeValue(value);

      if (sanitizedValue !== value) {
        creditCardInfo.value[fieldType] = sanitizedValue;
      }
    }
  }

  function isCardField(fieldType) {
    return fieldType === "card_number" || fieldType === "card_cvv";
  }

  function sanitizeValue(value) {
    return value.replace(/[^0-9]/g, "");
  }
  // =========================================================================================

  return {
    creditCard,
    creditCardInfo,

    getOptions,
    validateInput,
    deleteCreditCard,
  };
}
