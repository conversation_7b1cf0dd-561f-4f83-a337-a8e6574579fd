<template>
  <FormLayout>
    <div v-for="(field, fieldIndex) in moduleSetting" :key="fieldIndex">
      <div
        :class="`${mappingFieldLayout(
          fieldLayout(field)
        )} justify-content-between`"
      >
        <div
          v-if="field.settings"
          v-for="(fieldChild, fieldChildIndex) in field.settings"
          :key="fieldChildIndex"
          :style="{
            width: getWidthChildField(field, fieldChild),
          }"
          class="mt-3"
        >
          <FormValidator
            :label="fieldChildIndex === 0 ? fieldLabel(field) : `　`"
            :required="
              fieldChildIndex === 0 ? fieldRequired(fieldChild) : false
            "
            :name="fieldVariable(fieldChild)"
          >
            <component
              class="input-s"
              :is="getFieldComponent(fieldType(fieldChild))"
              v-model="cart[fieldVariable(fieldChild)]"
              :id="fieldVariable(fieldChild)"
              :type="getInputType(fieldVariable(fieldChild))"
              :placeholder="fieldPlaceholder(fieldChild)"
              @input="checkInput(fieldChild, $event)"
              maxlength="4"
              @click="
                spReadonly ? handleFieldClick(fieldVariable(fieldChild)) : ''
              "
              :readonly="spReadonly"
            />
            <InputSuggestion
              class="app-footer"
              @input="setInput($event)"
              :open-suggestion="telOpenSuggestion"
              :input-type="inputType"
              :value="cart[inputType]"
              @close="inputSuggestionClose"
            />
          </FormValidator>
        </div>
        <div v-else>
          <FormValidator
            v-if="fieldVariable(field) === 'mail'"
            :label="fieldLabel(field)"
            :required="fieldRequired(field)"
            :name="fieldVariable(field)"
            class="mt-3"
          >
            <!-- onsubmit="return false;" -->
            <form action="#" onsubmit="return false;" autocomplete="on">
              <component
                class="input-s"
                :is="getFieldComponent(fieldType(field))"
                v-model="cart[fieldVariable(field)]"
                :id="fieldVariable(field)"
                :type="getInputType(fieldVariable(field))"
                :placeholder="fieldPlaceholder(field)"
                @click="handleFieldClick(fieldVariable(field))"
              />
            </form>
          </FormValidator>

          <FormValidator
            v-else
            :label="fieldLabel(field)"
            :required="fieldRequired(field)"
            :name="fieldVariable(field)"
            class="mt-3"
          >
            <div class="input-container">
              <component
                class="input-s"
                :id="fieldVariable(field)"
                :is="getFieldComponent(fieldType(field))"
                v-model="cart[fieldVariable(field)]"
                :type="hidePass ? 'text' : 'password'"
                :placeholder="fieldPlaceholder(field)"
                @click="handleFieldClick(fieldVariable(field))"
              />
              <font-awesome-icon
                :icon="hidePass ? 'fa-eye' : 'fa-eye-slash'"
                class="hide-icon"
                @click="hidePassword"
              />
            </div>
          </FormValidator>
        </div>
      </div>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeTelEmailPassword()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>
<script setup>
import {
  ref,
  defineProps,
  getCurrentInstance,
  onMounted,
  computed,
  watch,
} from "vue";
import useCommon from "@/composables/common";
import InputSuggestion from "Components/shared/InputSuggestion.vue";
import {
  mobileCheck,
  handleInputInBrowser,
  handleScrollIntoView,
} from "@/ultilities/helper.js";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import { HALF_WIDTH_INPUT_FIELDS } from "@/ultilities/constants";
import { pick } from "lodash";
import { library } from "@fortawesome/fontawesome-svg-core";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
library.add(faEye, faEyeSlash);

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getFieldComponent,
  fieldLayout,
  getWidthChildField,
  disabledButton,
  handleFieldClick,
  mappingFieldLayout,
  inputType,
  getInputType,
  supportUiEnable,
  getAllVariables,
  currentVariables,
} = useCommon(vm);

setProps(props);

//// Define Emit
const emit = defineEmits(["change"]);

//// Define Store
const globalStore = useGlobalStore();
const { telOpenSuggestion } = storeToRefs(globalStore);

//// Define Computed
const isSp = computed(() => mobileCheck());
const spReadonly = computed(() => {
  return isSp.value && supportUiEnable.value;
});

//// FUNCTION
async function submitNodeTelEmailPassword() {
  isTEPModified.value = false;
  const arrKey = getAllVariables(moduleSetting.value);
  arrKey.forEach((item) => {
    if (HALF_WIDTH_INPUT_FIELDS.includes(item)) {
      handleInputInBrowser(item, cart.value);
    }
  });

  await onClick();
}

const isTEPModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isTEPModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isTEPModified.value = true;
    }
  },
  { immediate: true }
);

function setInput(value) {
  if (inputType.value === "tel1") {
    cart.value.tel1 = value;
  }
  if (inputType.value === "tel2") {
    cart.value.tel2 = value;
  }
  if (inputType.value === "tel3") {
    cart.value.tel3 = value;
  }
}

const checkInput = (field, value) => {
  if (["tel1", "tel2", "tel3"].includes(fieldVariable.value(field))) {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value[fieldVariable.value(field)] = value.replace(/[^0-9]/g, "");
    }
  }
};

// Other Variables
const cellPhone = ["090", "080", "070", "050"];
const hidePass = ref(false);

function clickTel2() {
  if (spReadonly.value) {
    // telOpenSuggestion.value = false;
    emit("change", { active: true, id: "tel1" });
    setTimeout(() => {
      handleFieldClick("tel2");
    }, 100);
  } else {
    return;
  }
}

function clickTel3() {
  if (spReadonly.value) {
    // telOpenSuggestion.value = false;
    emit("change", { active: true, id: "tel2" });
    setTimeout(() => {
      handleFieldClick("tel3");
    }, 100);
  } else {
    return;
  }
}
//// Watch
watch(
  () => cart.value.tel1,
  (value) => {
    const tel2 = document.getElementById("tel2");
    if (value.length === 2) {
      if (value === "03" || value === "06") {
        setTimeout(() => {
          tel2.focus();
          clickTel2();
        }, 50);
      }
    } else if (value.length === 3) {
      if (cellPhone.includes(value)) {
        setTimeout(() => {
          tel2.focus();
          clickTel2();
        }, 50);
      }
    } else if (value.length === 4) {
      setTimeout(() => {
        tel2.focus();
        clickTel2();
      }, 50);
    }
  }
);

watch(
  () => cart.value.tel2,
  (value) => {
    if (value) {
      const tel3 = document.getElementById("tel3");
      if (cart.value.tel1 && cart.value.tel1.length === 2) {
        if (value.length === 4) {
          setTimeout(() => {
            tel3.focus();
            clickTel3();
          }, 50);
        }
      } else if (cart.value.tel1 && cart.value.tel1.length === 3) {
        if (value.length === 3) {
          if (!cellPhone.includes(cart.value.tel1 && cart.value.tel1)) {
            setTimeout(() => {
              tel3.focus();
              clickTel3();
            }, 50);
          }
        } else if (value.length === 4) {
          setTimeout(() => {
            tel3.focus();
            clickTel3();
          }, 50);
        }
      } else if (cart.value.tel1 && cart.value.tel1.length === 4) {
        if (value.length === 2) {
          setTimeout(() => {
            tel3.focus();
            clickTel3();
          }, 50);
        }
      }
    }
  }
);

watch(
  () => cart.value.tel3,
  (value) => {
    if (value) {
      const mail = document.getElementById("mail");
      if (value.length === 4) {
        if (spReadonly.value) {
          telOpenSuggestion.value = false;
          emit("change", { active: false, id: "tel3" });
        }

        handleScrollIntoView(mail);

        setTimeout(() => {
          mail.focus();
        }, 100);
      }
    }
  }
);

function hidePassword() {
  hidePass.value = !hidePass.value;
}

function inputSuggestionClose() {
  const ipt = inputType.value;
  inputType.value = "";
  emit("change", { active: false, id: ipt });
}

function handleClickOutside(event) {
  if (
    !event.target.closest(".input-s") &&
    !event.target.closest(".app-footer")
  ) {
    telOpenSuggestion.value = false;
    const ipt = inputType.value;
    inputType.value = "";
    emit("change", { active: false, id: "" });
  }
}

/// ONMOUNTED
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});
</script>
<style scoped>
.input-container {
  position: relative;
}

.input-container .hide-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: black;
}
.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
</style>
