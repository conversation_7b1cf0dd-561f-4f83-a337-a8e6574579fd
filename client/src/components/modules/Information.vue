<template>
  <FormLayout>
    <div v-for="(field, index) in moduleSetting" :key="index">
      <div v-if="fieldVariable(field) == 'full_name'">
        <div v-if="isKanaComponentVisible" class="mt-3">
          <span style="font-weight: bold">お名前</span>

          <FormValidator class="mt-1">
            <component
              class="form-control input-s"
              id="fullName"
              is="input"
              :value="getFullName()"
              @click="handleFieldClick('fullName')"
              @input="setFullName($event.target.value)"
            />
          </FormValidator>

          <div class="note-name mt-2">
            <div class="d-flex">
              <div v-if="converting" class="d-flex align-items-center">
                <span
                  aria-visible="true"
                  class="spinner-border spinner-border-sm"
                  role="status"
                  style="color: #d7eecf"
                ></span>
                <span class="mx-2" style="color: #97e51e">変換中</span>
              </div>

              <div v-if="isNameConverted">
                {{ cart.sei }}
                {{ cart.mei }}
                (
                {{ cart.seifuri }}
                {{ cart.meifuri }})
              </div>
            </div>

            <span class="mt-1 convert-btn" @click="onConvertTxt()">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="#000000"
                height="15px"
                width="15px"
                version="1.1"
                id="Capa_1"
                viewBox="0 0 306.637 306.637"
                xml:space="preserve"
              >
                <g>
                  <g>
                    <path
                      d="M12.809,238.52L0,306.637l68.118-12.809l184.277-184.277l-55.309-55.309L12.809,238.52z M60.79,279.943l-41.992,7.896    l7.896-41.992L197.086,75.455l34.096,34.096L60.79,279.943z"
                    />
                    <path
                      d="M251.329,0l-41.507,41.507l55.308,55.308l41.507-41.507L251.329,0z M231.035,41.507l20.294-20.294l34.095,34.095    L265.13,75.602L231.035,41.507z"
                    />
                  </g>
                </g>
              </svg>
              名前を交換
            </span>
          </div>
        </div>

        <div
          v-if="!isKanaComponentVisible"
          v-for="fieldChild in field.settings"
          :key="fieldChild.id"
        >
          <div v-if="fieldChild.settings" class="mt-3">
            <span style="font-weight: bold">
              {{ fieldLabel(fieldChild) }}
            </span>

            <div class="layout_horizontal justify-content-between">
              <div
                v-for="fieldChildChild in fieldChild.settings"
                :style="{
                  width: fetchWidthChildField(field.settings),
                }"
              >
                <FormValidator
                  :label="fieldLabel(fieldChildChild) || '　'"
                  :name="fieldVariable(fieldChildChild)"
                  :required="fieldRequired(fieldChildChild)"
                >
                  <b-form-input
                    v-model="cart[fieldVariable(fieldChildChild)]"
                    class="input-s"
                    :id="fieldVariable(fieldChildChild)"
                    :placeholder="fieldPlaceholder(fieldChildChild)"
                    @click="handleFieldClick(fieldVariable(fieldChildChild))"
                    @keyup.delete="
                      handleKanaName(fieldVariable(fieldChildChild))
                    "
                  />
                </FormValidator>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="fieldVariable(field) == 'sex'">
        <FormValidator
          class="mt-3"
          :label="fieldLabel(field)"
          :name="fieldVariable(field)"
          :required="fieldRequired(field)"
        >
          <b-form-radio-group
            v-model="cart[fieldVariable(field)]"
            class="w-100 order_sex_radio"
            size="lg"
            stacked
            :id="fieldVariable(field)"
            :options="sexOptions"
            :placeholder="fieldPlaceholder(field)"
            @click="handleFieldClick(fieldVariable(field))"
          />
        </FormValidator>
      </div>

      <div v-if="fieldVariable(field) == 'birthday'" class="mt-3">
        <span style="font-weight: bold">
          {{ fieldLabel(field) }}
        </span>

        <div class="layout_horizontal justify-content-between mb-3">
          <div
            v-for="fieldChild in field.settings"
            :style="{
              width: fetchWidthChildField(field.settings),
            }"
          >
            <FormValidator
              :label="fieldLabel(fieldChild) || '　'"
              :name="fieldVariable(fieldChild)"
              :required="fieldRequired(fieldChild)"
            >
              <b-form-select
                v-model="cart[fieldVariable(fieldChild)]"
                class="input-s"
                :id="fieldVariable(fieldChild)"
                :options="getOptions(fieldChild)"
                :placeholder="fieldPlaceholder(fieldChild)"
                @click="handleFieldClick(fieldVariable(fieldChild))"
              />
            </FormValidator>
          </div>
        </div>
      </div>

      <div v-if="fieldVariable(field) == 'address'">
        <span style="font-weight: bold">
          {{ fieldLabel(field) }}
        </span>

        <div v-for="fieldChild in field.settings">
          <div class="d-flex align-items-center mb-3">
            <div class="col-4">
              <label class="col-form-label">
                {{ fieldLabel(fieldChild) }}
                <span v-if="fieldRequired(fieldChild)" class="text-danger">
                  *
                </span>
              </label>
            </div>

            <div class="col-8">
              <FormValidator
                :label="''"
                :name="fieldVariable(fieldChild)"
                :required="fieldRequired(fieldChild)"
              >
                <component
                  v-model="cart[fieldVariable(fieldChild)]"
                  class="input-s w-100"
                  :id="fieldVariable(fieldChild)"
                  :options="getOptionsPrefectures(fieldVariable(fieldChild))"
                  :placeholder="fieldPlaceholder(fieldChild)"
                  :type="getInputType(fieldVariable(fieldChild))"
                  :is="
                    fieldVariable(fieldChild) === 'prefectures'
                      ? 'b-form-select'
                      : 'b-form-input'
                  "
                  :readonly="
                    spReadonly
                      ? handelReadonly(fieldVariable(fieldChild))
                      : false
                  "
                  @input="validateInput(fieldChild, $event)"
                  @click="
                    spReadonly
                      ? handleFieldClick(fieldVariable(fieldChild))
                      : ''
                  "
                />
              </FormValidator>

              <InputSuggestion
                class="app-footer"
                :input-type="inputType"
                :value="inputType === 'zipcode' ? cart.zipcode : cart.address02"
                :open-suggestion="
                  zipcodeOpenSuggestion || address02OpenSuggestion
                "
                @close="inputSuggestionClose"
                @input="setInput($event)"
              ></InputSuggestion>
            </div>
          </div>
        </div>
      </div>

      <div v-if="fieldVariable(field) == 'tel'">
        <FormValidator
          :label="fieldLabel(field)"
          :name="fieldVariable(field)"
          :required="fieldRequired(field)"
        >
          <b-form-input
            v-model="cart[fieldVariable(field)]"
            class="input-s"
            :id="fieldVariable(field)"
            :placeholder="fieldPlaceholder(field)"
            :type="getInputType(fieldVariable(field))"
            :readonly="
              spReadonly ? handelReadonly(fieldVariable(field)) : false
            "
            @click="spReadonly ? handleFieldClick(fieldVariable(field)) : ''"
            @input="validateInput(field, $event)"
          />
        </FormValidator>

        <InputSuggestion
          class="app-footer"
          :input-type="`tel`"
          :open-suggestion="telOpenSuggestion"
          :value="cart.tel"
          @close="inputSuggestionClose"
          @input="setInput($event, fieldVariable(field))"
        />
      </div>

      <div v-if="fieldVariable(field) == 'mail'">
        <FormValidator
          class="mt-3"
          :label="fieldLabel(field)"
          :name="fieldVariable(field)"
          :required="fieldRequired(field)"
        >
          <form action="#" onsubmit="return false;" autocomplete="on">
            <b-form-input
              class="input-s"
              v-model="cart[fieldVariable(field)]"
              :id="fieldVariable(field)"
              :placeholder="fieldPlaceholder(field)"
              :type="getInputType(fieldVariable(field))"
              @click="handleFieldClick(fieldVariable(field))"
            />
          </form>
        </FormValidator>
      </div>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeInformation()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import {
  computed,
  defineEmits,
  defineProps,
  getCurrentInstance,
  onMounted,
  ref,
  watch,
} from "vue";
import { storeToRefs } from "pinia";
import * as JpPrefecture from "jp-prefectures";
import postalCode from "jp-postalcode-lookup";

import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";

import { useConvertNameStore } from "@/stores/convertName";
import { useGlobalStore } from "@/stores/global";

import {
  convertFullWidthToHalfWidth,
  handleScrollIntoView,
  mobileCheck,
} from "@/ultilities/helper.js";

import InputSuggestion from "Components/shared/InputSuggestion.vue";
import { toKatakana, isKana } from "wanakana";

const emit = defineEmits(["change"]);

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const vm = getCurrentInstance();

const {
  buttonText,
  cart,
  currentVariables,
  disabledButton,
  fieldLabel,
  fieldPlaceholder,
  fieldRequired,
  fieldVariable,
  FormLayout,
  getInputType,
  handleFieldClick,
  inputType,
  moduleSetting,
  onClick,
  setProps,
  supportUiEnable,
  visibleLoading,
} = useCommon(vm);
const { settingDesign } = useScenarioSetting();
const convertNameStore = useConvertNameStore();
const globalStore = useGlobalStore();

setProps(props);

const sexOptions = [
  { text: "男", value: "男" },
  { text: "女", value: "女" },
];

const { address02OpenSuggestion, zipcodeOpenSuggestion, telOpenSuggestion } =
  storeToRefs(globalStore);

const converting = ref(false);
const hideName = ref(false);
const isInformationModified = ref(false);

const isSp = computed(() => mobileCheck());

const spReadonly = computed(() => {
  return isSp.value && supportUiEnable.value;
});

const isButtonUpdate = computed(() => buttonText.value === "更新");

const isKanaComponentVisible = computed(() => {
  return !hideName.value && supportUiEnable.value;
});

const isDisable = computed(() => {
  if (isButtonUpdate.value && isInformationModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

const isNameConverted = computed(() => {
  return (
    cart.value.fullName === `${cart.value.sei}${cart.value.mei}` &&
    cart.value.sei &&
    cart.value.mei &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
});

function isPrefectures(variable) {
  return variable == "prefectures";
}

function getOptionsPrefectures(variable) {
  return isPrefectures(variable) ? JpPrefecture.prefectureNames() : [];
}

const fetchWidthChildField = (settings) => {
  return "calc((100% / " + settings.length + ") - 1%)";
};

async function submitNodeInformation() {
  isInformationModified.value = false;
  await onClick();
}

function getFullName() {
  return cart.value.fullName;
}

function setFullName(value) {
  cart.value.fullName = value;
}

function shouldConvertFullName() {
  return (
    (cart.value.fullName &&
      cart.value.fullName.length &&
      !cart.value.sei &&
      !cart.value.mei &&
      !cart.value.seifuri &&
      !cart.value.meifuri) ||
    (cart.value.fullName &&
      cart.value.fullName.length &&
      cart.value.fullName !== `${cart.value.sei}${cart.value.mei}` &&
      cart.value.sei &&
      cart.value.mei &&
      cart.value.seifuri &&
      cart.value.meifuri)
  );
}

function isFullNameConverted() {
  return (
    cart.value.fullName &&
    cart.value.fullName.length &&
    cart.value.sei &&
    cart.value.mei &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
}

function clearNameFields() {
  cart.value.sei = null;
  cart.value.mei = null;
  cart.value.seifuri = null;
  cart.value.meifuri = null;
}

function startConverting() {
  converting.value = true;
  setTimeout(() => {
    convertNameStore.getKana(cart.value.fullName);
    converting.value = false;
    hideName.value = true;
  }, 500);
}

function onConvertTxt() {
  if (shouldConvertFullName()) {
    startConverting();
  } else if (isFullNameConverted()) {
    hideName.value = true;
  } else if (!cart.value.fullName || !cart.value.fullName.length) {
    clearNameFields();
    hideName.value = true;
  }

  setTimeout(() => {
    const el = document.getElementById("meifuri");
    handleScrollIntoView(el);
  }, 500);
}

const getOptions = (field) => {
  switch (field.variable) {
    case "day":
      return optionsDay();
    case "month":
      return optionsMonth();
    case "year":
      return optionsYear();
  }
};

const optionsMonth = () => {
  return Array.from({ length: 12 }, (_, i) => i + 1);
};

const optionsYear = () => {
  return Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i);
};

const optionsDay = () => {
  const date = new Date();
  const year = cart.value.year || date.getFullYear();
  const month = cart.value.month || date.getMonth() + 1; // January gives 0

  const optionsLength = new Date(year, month, 0).getDate();

  const options = Array.from({ length: optionsLength }, (_, i) => i + 1);

  if (cart.value.day && !options.includes(cart.value.day)) cart.value.day = ``;

  return options;
};

function setInput(value) {
  if (inputType.value === "address02") {
    cart.value.address02 = value;
  }
  if (inputType.value === "zipcode") {
    cart.value.zipcode = value;
  }
  if (inputType.value === "tel") {
    cart.value.tel = value;
  }
}

function inputSuggestionClose() {
  const ipt = inputType.value;
  inputType.value = "";
  emit("change", { active: false, id: ipt });
}

const validateInput = (field, value) => {
  if (fieldVariable.value(field) === "zipcode") {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value.zipcode = value.replace(/[^0-9]/g, "");
    }
  }

  if (fieldVariable.value(field) === "tel") {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value.tel = value.replace(/[^0-9]/g, "");
    }
  }
};

function handelReadonly(field) {
  return field === "address02" || field === "zipcode" || field === "tel";
}

function handleClickOutside(event) {
  if (
    !event.target.closest(".input-s") &&
    !event.target.closest(".app-footer")
  ) {
    zipcodeOpenSuggestion.value = false;
    telOpenSuggestion.value = false;
    address02OpenSuggestion.value = false;
    inputType.value = "";
    emit("change", { active: false, id: "" });
  }
}

function handleKanaName(field) {
  if (field === "sei") {
    if (!cart.value.sei) cart.value.seifuri = "";
  } else if (field === "mei") {
    if (!cart.value.mei) cart.value.meifuri = "";
  } else {
    return;
  }
}

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isInformationModified.value = true;
    }
  },
  { immediate: true }
);

watch(
  () => cart.value.zipcode,
  (value) => {
    if (!value) return;
    console.log(value);

    if (value.length !== 7) return;
    const valueConverted = convertFullWidthToHalfWidth(cart.value["zipcode"]);

    postalCode.get(valueConverted, (results) => {
      if (!results) return;

      cart.value.prefectures = results.prefecture;
      cart.value.address01 = results.city + results.area + results.street;

      if (!cart.value.address02 && spReadonly.value) {
        zipcodeOpenSuggestion.value = false;
        emit("change", { active: false, id: "" });

        setTimeout(() => {
          handleFieldClick("address02");
        }, 500);
      }
    });
  }
);

watch(
  () => cart.value.sei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${value}${cart.value.mei}`);
    if (!isKana(value)) return;

    const seiKana = toKatakana(value);

    cart.value.seifuri = seiKana;
  }
);

watch(
  () => cart.value.mei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${cart.value.sei}${value}`);
    if (!isKana(value)) return;

    const meiKana = toKatakana(value);

    cart.value.meifuri = meiKana;
  }
);

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});
</script>

<style scoped lang="scss">
.input-container {
  position: relative;
}

.input-container .hide-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: black;
}

:deep(.order_sex_radio) {
  display: flex;
  gap: 23px;

  input {
    display: none !important;
  }

  label {
    display: table-cell;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 10px;
    background: #ddd;
    color: #767676;
    font-size: 16px;
    text-align: center;
    line-height: 1;
    transition: 0.2s;
    border-left: 1px solid #fff;
    border-radius: 5px;
  }
}

:deep(.order_sex_radio input[type="radio"]:checked + label) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: #fff;
}

:deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: white;
}

:deep(.form-control-lg) {
  padding: 0px !important;
  min-height: unset;
  width: 50%;
  display: flex;
}

.convert-btn {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  color: #fff;
}

.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
</style>
