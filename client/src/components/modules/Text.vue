<template>
  <FormLayout>
    <div v-for="field in moduleSetting" :key="field.id">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <!-- onsubmit="return false;"  -->
        <form action="#" onsubmit="return false;" autocomplete="on">
          <component
            class="input-s"
            :is="getFieldComponent(fieldType(field))"
            v-model="cart[fieldVariable(field)]"
            :type="getInputType(fieldVariable(field))"
            :id="fieldVariable(field)"
            :placeholder="fieldPlaceholder(field)"
            @input="checkInput(field, $event)"
            @click="spReadonly ? handleFieldClick(fieldVariable(field)) : ''"
            :readonly="
              spReadonly ? handelReadonly(fieldVariable(field)) : false
            "
          />
        </form>
      </FormValidator>
      <InputSuggestion
        class="app-footer"
        @input="setInput($event, fieldVariable(field))"
        :open-suggestion="telOpenSuggestion"
        :input-type="`tel`"
        :value="cart.tel"
        @close="inputSuggestionClose"
      />
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeText()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import InputSuggestion from "Components/shared/InputSuggestion.vue";
import {
  ref,
  watch,
  defineProps,
  onMounted,
  getCurrentInstance,
  computed,
  defineEmits,
} from "vue";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import { mobileCheck, handleInputInBrowser } from "@/ultilities/helper.js";
//// Define Props
const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

//// Define Computed
const isSp = computed(() => mobileCheck());
const spReadonly = computed(() => {
  return isSp.value && supportUiEnable.value;
});

const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getInputType,
  disabledButton,
  getFieldComponent,
  supportUiEnable,
  inputType,
  handleFieldClick,
  getAllVariables,
  currentVariables,
} = useCommon(vm);

setProps(props);

//// Define Emit
const emit = defineEmits(["change"]);

//// Define Store
const globalStore = useGlobalStore();
const { telOpenSuggestion } = storeToRefs(globalStore);

//// FUNCTION

async function submitNodeText() {
  isTextModified.value = false;
  const arrKey = getAllVariables(moduleSetting.value);
  if (arrKey.includes("tel")) {
    handleInputInBrowser("tel", cart.value);
  }
  await onClick();
}

const isTextModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isTextModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isTextModified.value = true;
    }
  },
  { immediate: true }
);

function setInput(value, field) {
  cart.value.tel = value;
}

function handelReadonly(field) {
  return field === "tel";
}

const checkInput = (field, value) => {
  if (fieldVariable.value(field) === "tel") {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value.tel = value.replace(/[^0-9]/g, "");
    }
  }
};

function handleClickOutside(event) {
  if (
    !event.target.closest(".input-s") &&
    !event.target.closest(".app-footer")
  ) {
    telOpenSuggestion.value = false;
    const ipt = inputType.value;
    inputType.value = "";
    emit("change", { active: false, id: "" });
  }
}

function inputSuggestionClose() {
  const ipt = inputType.value;
  inputType.value = "";
  emit("change", { active: false, id: ipt });
}

/// ONMOUNTED
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});
</script>

<style scoped>
.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
</style>
