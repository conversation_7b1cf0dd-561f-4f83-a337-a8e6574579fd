<template>
  <FormLayout>
    <div v-for="(field, index) in moduleSetting" :key="index">
      <FormValidator
        v-if="fieldVariable(field) === 'scheduled_date'"
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <component
          class="input-s"
          :id="fieldVariable(field)"
          :is="getFieldComponent(fieldType(field))"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          :options="fieldScheduleOptions"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
      <FormValidator
        v-else
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <b-form-radio-group
          class="radio"
          :id="fieldVariable(field)"
          v-model="cart[fieldVariable(field)]"
          :options="getOptions(field)"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
    </div>
    <b-button
      class="form-btn-submit"
      @click="submitNode()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import { defineProps, getCurrentInstance, ref, watch, computed } from "vue";
import { useScenarioStore } from "@/stores/scenario";
import { pick } from "lodash";

//// Define Props
const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

//// Define Computed
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  disabledButton,
  getFieldComponent,
  supportUiEnable,
  inputType,
  handleFieldClick,
  currentVariables,
} = useCommon(vm);

setProps(props);
const fieldScheduleOptions = ref(null);
const scenarioStore = useScenarioStore();

//// FUNCTION
function getOptions(field) {
  const options = fieldOptions.value(field);

  return options.map((option) => ({
    text: option.text,
    value: option.text,
  }));
}

async function submitNode() {
  isScheduledModified.value = false;
  await onClick();
}

const isScheduledModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isScheduledModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isScheduledModified.value = true;
    }
  },
  { immediate: true }
);

watch(
  () => moduleSetting.value,
  async (newModuleSetting) => {
    for (const field of newModuleSetting) {
      if (fieldVariable.value(field) === "scheduled_date") {
        const options = await getFielOption(field);
        fieldScheduleOptions.value = options;
      }
    }
  },
  { immediate: true }
);

function getFielOption(field) {
  const daysAfterCurrent = field.days_after_current || "4";
  const rangeDays = field.range_days || "10";
  try {
    const result = scenarioStore.fetchScheduleDate(daysAfterCurrent, rangeDays);
    return result;
  } catch (error) {
    console.error("Error fetching schedule date:", error);
    return [];
  }

  // return dateRange(field.start_date, field.end_date);
}

// function dateRange(start, end, steps = 1) {
//   const dateArray = [];
//   if (!isoDate(start) || !isoDate(end)) {
//     return [];
//   }

//   let currentDate = new Date(isoDate(start));
//   let endDate = new Date(isoDate(end));

//   while (currentDate <= endDate) {
//     const dateValue = new Date(currentDate);
//     dateArray.push(
//       `${dateValue.getFullYear()}-${
//         dateValue.getMonth() + 1
//       }-${dateValue.getDate()}`
//     );
//     currentDate.setDate(currentDate.getDate() + steps);
//   }

//   return dateArray;
// }

// function isoDate(date) {
//   let [year, month, day] = date.split("-");
//   if (!year || !day || !month) {
//     return null;
//   }
//   month = month.padStart(2, "0");
//   day = day.padStart(2, "0");
//   return `${year}-${month}-${day}`;
// }
</script>

<style scoped lang="scss">
.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
.radio {
  display: flex;
  flex-direction: column;
}
</style>
