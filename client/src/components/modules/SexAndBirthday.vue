<template>
  <FormLayout>
    <div v-for="field in moduleSetting" class="mt-3">
      <p style="font-weight: bold" v-if="field.settings">
        {{ fieldLabel(field) }}
      </p>

      <div
        :class="`${mappingFieldLayout(
          fieldLayout(field)
        )} justify-content-between`"
      >
        <div
          v-if="field.settings"
          v-for="fieldChild in field.settings"
          :style="{
            width: getWidthChildField(field, fieldChild),
          }"
        >
          <FormValidator
            :label="fieldLabel(fieldChild)"
            :required="fieldRequired(fieldChild)"
            :name="fieldVariable(fieldChild)"
            class="mt-3"
          >
            <component
              class="input-s"
              :is="getFieldComponent(fieldType(fieldChild))"
              v-model="cart[fieldVariable(fieldChild)]"
              :options="getOptions(fieldChild)"
              :id="fieldVariable(fieldChild)"
              :placeholder="fieldPlaceholder(fieldChild)"
              @click="handleFieldClick(fieldVariable(fieldChild))"
            />
          </FormValidator>
        </div>

        <FormValidator
          v-else
          :label="fieldLabel(field)"
          :required="fieldRequired(field)"
          :name="fieldVariable(field)"
          class="mt-3"
        >
          <component
            :is="getFieldComponent(fieldType(field))"
            v-model="cart[fieldVariable(field)]"
            :id="fieldVariable(field)"
            :placeholder="fieldPlaceholder(field)"
            :options="getOptions(field)"
            @click="handleFieldClick(fieldVariable(field))"
            class="w-100 order_sex_radio"
            size="lg"
            stacked
          />
        </FormValidator>
      </div>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeSB()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref, watch, computed, getCurrentInstance } from "vue";
import { pick } from "lodash";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getFieldComponent,
  fieldLayout,
  getWidthChildField,
  disabledButton,
  mappingFieldLayout,
  handleFieldClick,
  currentVariables,
} = useCommon(vm);

const { settingDesign } = useScenarioSetting();

setProps(props);

async function submitNodeSB() {
  isSABModified.value = false;
  await onClick();
}

const isSABModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isSABModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isSABModified.value = true;
    }
  },
  { immediate: true }
);

function getOptions(field) {
  if (field.options) {
    if (field.type === "radio") {
      return fieldOptions.value(field).map((a) => ({
        text: a.text,
        value: a.text,
      }));
    }
    return field.options;
  }

  switch (field.variable) {
    case "day":
      return optionsDay();
    case "month":
      return optionsMonth();
    case "year":
      return optionsYear();
  }
}

function optionsMonth() {
  return Array.from({ length: 12 }, (_, i) => i + 1);
}

function optionsYear() {
  return Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i);
}

function optionsDay() {
  const date = new Date();
  const year = cart.value.year || date.getFullYear();
  const month = cart.value.month || date.getMonth() + 1; // January gives 0

  const optionsLength = new Date(year, month, 0).getDate();

  const options = Array.from({ length: optionsLength }, (_, i) => i + 1);

  if (!options.includes(cart.value.day)) cart.value.day = ``;

  return options;
}
</script>

<style scoped lang="scss">
:deep(.order_sex_radio) {
  display: flex;
  gap: 23px;
  input {
    display: none !important;
  }
  label {
    display: table-cell;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 10px;
    background: #ddd;
    color: #767676;
    font-size: 16px;
    text-align: center;
    line-height: 1;
    transition: 0.2s;
    border-left: 1px solid #fff;
    border-radius: 5px;
  }
}

:deep(.order_sex_radio input[type="radio"]:checked + label) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: #fff;
}
:deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: white;
}

:deep(.form-control-lg) {
  padding: 0px !important;
  min-height: unset;
  width: 50%;
  display: flex;
}
</style>
