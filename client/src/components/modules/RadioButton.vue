<template>
  <FormLayout>
    <div v-for="field in moduleSetting">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <b-form-radio-group
          class="radio-btn"
          v-model="cart[fieldVariable(field)]"
          :id="fieldVariable(field)"
          :options="getOptions(field)"
          buttons
          stacked
          size="lg"
          @change="onClick()"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
    </div>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  FormLayout,
  cart,
  setProps,
  moduleSetting,
  fieldLabel,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  onClick,
  handleFieldClick,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();

function getOptions(field) {
  const options = fieldOptions.value(field);

  return options.map((option) => ({
    text: option.text,
    value: option.text,
    disabled:
      cart.value[fieldVariable.value(field)] &&
      cart.value[fieldVariable.value(field)] != option.text,
  }));
}
</script>

<style scoped lang="scss">
.radio-btn {
  width: 100%;
  margin: 0 auto;
  /* padding: 15px 45px; */
  color: white;
  display: flex;
}

.radio-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.radio-btn:disabled {
  opacity: 0.5;
}

.radio-btn :deep(label) {
  background-color: white;
  margin: 10px 0 !important;
  color: #b3352d;

  border: 2px solid #b3352d;
  border-bottom-right-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}

.radio-btn :deep(label:hover) {
  background-color: v-bind("settingDesign.optionActiveBgColor");
}

.radio-btn :deep(.btn-check:checked + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.optionActiveBgColor");

  &:hover {
    opacity: 0.85;
  }
}

.radio-btn :deep(.btn-check:disabled + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.optionActiveBgColor");
  opacity: 0.4;
}
</style>
