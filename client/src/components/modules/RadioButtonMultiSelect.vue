<template>
  <FormLayout>
    <div v-for="(field, fieldIndex) in moduleSetting" :key="fieldIndex">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <b-form-checkbox-group
          class="checkbox-group"
          :id="fieldVariable(field)"
          v-model="cart[fieldVariable(field)]"
          buttons
          stacked
          size="lg"
        >
          <b-form-checkbox
            v-for="option in getOptions(field)"
            :key="option.value"
            :value="option.value"
          >
            {{ option.text }}
          </b-form-checkbox>
        </b-form-checkbox-group>
      </FormValidator>
    </div>

    <b-button
      class="form-btn-submit"
      @click="customOnclick()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref, watch, computed, getCurrentInstance } from "vue";
import { pick } from "lodash";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  FormLayout,
  buttonText,
  cart,
  setProps,
  moduleSetting,
  fieldLabel,
  fieldVariable,
  visibleLoading,
  fieldOptions,
  fieldRequired,
  onClick,
  handleFieldClick,
  disabledButton,
  currentVariables,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();
//

function getOptions(field) {
  const options = fieldOptions.value(field);

  return options.map((option) => ({
    text: option.text,
    value: option.text,
  }));
}

async function customOnclick(variable, value) {
  isBTNModified.value = false;
  await onClick();
  await handleFieldClick(variable);
}

const isBTNModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isBTNModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isBTNModified.value = true;
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.checkbox-group {
  width: 100%;
  margin: 0 auto;
  /* padding: 15px 45px; */
  color: white;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.checkbox-group:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.checkbox-group :deep(label) {
  background-color: white;
  margin: 10px 0 !important;
  color: v-bind("settingDesign.initiateBtnBgColor");

  border: 2px solid v-bind("settingDesign.initiateBtnBgColor");
  border-bottom-right-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}

// .checkbox-group :deep(label:hover) {
//   background-color: v-bind("settingDesign.initiateBtnBgColor");
// }

.checkbox-group :deep(.btn-check:checked + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.initiateBtnBgColor");

  &:hover {
    opacity: 0.85;
  }
}
</style>
