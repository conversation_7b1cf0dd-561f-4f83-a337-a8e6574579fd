<template>
  <div v-show="scenario.progressBarEnable" class="fixed-progress">
    <div
      class="progress-bar"
      aria-valuemin="0"
      aria-valuemax="100"
      :aria-valuenow="progressPercentage"
    >
      <div class="progress-percentage" :style="progressStyle">
        あと{{ progressLabel }}問
      </div>
    </div>
  </div>
</template>

<script setup>
import useScenarioSetting from "@/composables/chatSetting";
import { computed, watch, ref } from "vue";
import { storeToRefs } from "pinia";
import { useScenarioStore } from "@/stores/scenario";
import { NODE_TYPE } from "@/ultilities/constants";

const scenarioStore = useScenarioStore();
const { settingDesign } = useScenarioSetting();
const { scenario, nodes } = storeToRefs(scenarioStore);

const longestInput = ref(0);
const longestInputForm = ref(0);


watch(
  nodes,
  () => {
    if (nodes.value.length > 0) {
      const result = calculateLongestInput();
      longestInput.value = result.longestInput;
      longestInputForm.value = result.longestInputForm;
    }
  },
  { deep: true }
);

/**
 * Calculates the longest path of "input" or "button" nodes in a graph.
 *
 * Input: None (uses global `nodes.value`).
 * Output: Object with `longestInputForm` and `longestInput` (both numbers).
 */
const calculateLongestInput = () => {
  // Create a copy of the nodes data to ensure immutability.
  const nodeData = [...nodes.value];

  // Calculate the longest input path starting from the first node.
  const longestInputForm = countInputsFromNode(nodeData[0]);

  // Calculate the longest input path starting from the last node.
  const longestInput = countInputsFromNode(nodeData[nodeData.length - 1]);

  // Return both results.
  return {
    longestInput,
    longestInputForm,
  };
};

// Recursive function to count "input" or "button" nodes along the longest path from a given node.
const countInputsFromNode = (node, visited = [], hasReachedButton = false) => {
  if (!node || visited.includes(node.uid)) {
    return 0;
  }

  // Mark the current node as visited
  visited = [...visited, node.uid];

  // If it's a BUTTON node, stop counting
  if (node.nodeType === NODE_TYPE.BUTTON) {
    return 0;
  }

  // If it's a MESSAGE node after a BUTTON, return 0
  if (node.nodeType === NODE_TYPE.MESSAGE && hasReachedButton) {
    return 0;
  }

  // Check if the current node is an input node
  const isInputNode = node.nodeType === NODE_TYPE.INPUT ? 1 : 0;

  // Ensure `nextNodeUid` is an array before calling `.map()`
  if (!node.nextNodeUid || !Array.isArray(node.nextNodeUid)) {
    return isInputNode;
  }

  // Recursively calculate input count for child nodes
  const childInputs = Math.max(
    ...node.nextNodeUid.map((nextId) => {
      // Ensure `scenario.value.nodes` is valid and contains the target node
      const childNode = scenario.value.nodes?.find((i) => i.uid === nextId);
      return childNode
        ? countInputsFromNode(childNode, visited, node.nodeType === NODE_TYPE.BUTTON || hasReachedButton)
        : 0;
    })
  );

  return isInputNode + childInputs;
};

const progressLabel = computed(() => {
  return isMessageAfterButton()
    ? 0 // Show completion message
    : longestInput.value;
});

// Helper function to check if the current scenario has MESSAGE after BUTTON
const isMessageAfterButton = () => {
  const buttonIndex = nodes.value.findIndex((node) => node.nodeType === NODE_TYPE.BUTTON);
  const messageIndex = nodes.value.findIndex(
    (node, index) => node.nodeType === NODE_TYPE.MESSAGE && index > buttonIndex
  );
  return buttonIndex !== -1 && messageIndex > buttonIndex;
};

const progressPercentage = computed(() => {
  return isMessageAfterButton()
    ? 100 // Full progress completed
    : 100 - Math.round((longestInput.value / longestInputForm.value) * 100);
});

const progressStyle = computed(() => {
  return {
    width: `${progressPercentage.value}%`,
  };
});
</script>

<style scoped lang="scss">
.fixed-progress {
  --progress-bar-bg-color: v-bind("settingDesign.progressBarBgColor");
  --progress-percentage-bg-color: v-bind("settingDesign.progressPercentageBgColor");
  --progress-percentage-color: v-bind("settingDesign.progressPercentageColor");

  width: 100%;
  position: fixed;
  top: 60px;
  z-index: 99999;
  padding: 1px;
  .progress-bar {
    border-radius: 0;
    background-color: var(--progress-bar-bg-color);
    .progress-percentage {
      background-color: var(--progress-percentage-bg-color);
      color: var(--progress-percentage-color);
      font-size: 16px;
      font-weight: bold;
      text-align: left;
      text-indent: 10px;
      height: 25px;
      min-width: 85px;
      transition: 0.5s;
    }
  }
}
</style>
