<template>
  <FormLayout>
    <div v-for="field in moduleSetting">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <component
          class="input-s"
          :is="getFieldComponent(fieldType(field))"
          :id="fieldVariable(field)"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          :options="fieldOptions(field)"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
    </div>

    <b-button
      class="form-btn-submit"
      @click="onClick()"
      :disabled="disabledButton()"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      >
      </span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import { getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getFieldComponent,
  disabledButton,
  handleFieldClick,
} = useCommon(vm);

setProps(props);
</script>
