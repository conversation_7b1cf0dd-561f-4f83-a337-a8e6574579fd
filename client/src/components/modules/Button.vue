<template>
  <FormLayout>
    <div v-for="field in moduleSetting">
      <div v-if="fieldType(field) === BUTTON_TYPE.TEMPLATE">
        <p class="mb-3">
          <b>{{ fieldContent(field).title }}</b>
        </p>
        <pre class="textbox" v-html="mapData(fieldContent(field).body)" />
      </div>

      <div v-if="fieldType(field) === BUTTON_TYPE.POLICY" class="policy">
        <div class="policy-title" @click="policyRead = !policyRead">
          <span>{{ fieldContent(field).title }}</span>
          <span>+</span>
        </div>

        <pre
          class="p-2 policy-content"
          v-if="policyRead"
          v-html="mapData(fieldContent(field).body)"
        />
      </div>

      <div v-if="fieldType(field) === BUTTON_TYPE.BUTTON">
        <form-validator class="mt-4 pt-2 mb-3" name="termAccepted">
          <span>ご購入前に利用規約を必ずお読み下さい。</span>
          <b-form-checkbox name="agree_check" v-model="termAccepted">
            利用規約に同意する
          </b-form-checkbox>
        </form-validator>

        <div class="text-center my-5">
          <b-button
            size="lg"
            variant="success"
            type="submit"
            name="submit"
            id="submit"
            @click="onClick()"
            class="confirm-btn"
            :disabled="!termAccepted"
          >
            {{ fieldContent(field) }}
          </b-button>
        </div>
      </div>
    </div>
  </FormLayout>
</template>

<script setup>
import { BUTTON_TYPE } from "@/ultilities/constants";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref } from "vue";
import { getCurrentInstance } from "vue";

const termAccepted = ref(false);

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  FormLayout,
  setProps,
  moduleSetting,
  fieldContent,
  fieldType,
  cart,
  handleFieldClick,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();

const policyRead = ref(false);

function mapData(data) {
  return data.replace(/\{\{([^{}]+)\}\}/g, (match, key) => {
    if (cart.value.hasOwnProperty(key)) {
      return cart.value[key];
    } else {
      return "";
    }
  });
}
</script>

<style scoped>
.confirm-btn {
  width: 100%;
  background-image: linear-gradient(
    to right,
    v-bind("settingDesign.initiateBtnBgColor") 0%,
    v-bind("settingDesign.chatWindowBgColor") 51%,
    v-bind("settingDesign.initiateBtnBgColor") 100%
  );
  margin: 0 auto;
  padding: 15px 45px;
  text-align: center;
  text-transform: uppercase;
  transition: 0.5s;
  background-size: 200% auto;
  color: white;
  box-shadow: 0 0 20px #eee;
  border: none;
  border-radius: 60px;
  display: block;
}

.confirm-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.confirm-btn:disabled {
  opacity: 0.5;
}
.textbox {
  border: 1px solid v-bind("settingDesign.formInputBorderColor");
  padding: 5px;
  text-wrap: wrap;
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 200px;
}

.policy-title {
  background-color: #e5e7eb;
  padding: 10px 15px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  cursor: pointer;
}

.policy {
  border: 1px solid #e5e7eb;
}

.policy-content {
  text-wrap: wrap;
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 200px;
}
</style>
