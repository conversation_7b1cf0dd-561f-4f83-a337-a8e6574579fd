<template>
  <FormLayout>
    <div v-if="isCrawlerDataNode">
      <b-button
        class="crawler-fetch-button"
        @click="fetchCrawlerData"
        :disabled="isFetching"
      >
        <span
          v-if="isFetching"
          class="spinner-border spinner-border-sm me-2"
          role="status"
          aria-hidden="true"
        >
        </span>
        {{
          isFetching
            ? moduleSetting[0]?.crawler_data?.ui_texts?.loading ||
              "読み込み中..."
            : moduleSetting[0]?.crawler_data?.ui_texts?.button ||
              "データを取得する"
        }}
      </b-button>

      <div v-if="hasFetchError" class="text-danger mt-2">
        {{
          moduleSetting[0]?.crawler_data?.ui_texts?.error ||
          "データの取得に失敗しました。入力内容をご確認ください。"
        }}
      </div>

      <div v-if="isDataLoaded" class="mt-2">
        <span class="text-success">
          ✓
          {{
            moduleSetting[0]?.crawler_data?.crawler_data?.ui_texts?.success ||
            "データが正常に取得されました"
          }}
          ({{ moduleSetting[0]?.options?.length || 0 }}
          {{
            moduleSetting[0]?.crawler_data?.ui_texts?.options_suffix ||
            "オプション"
          }})
        </span>
      </div>
    </div>

    <div v-if="isDataLoaded">
      <div v-for="field in moduleSetting">
        <FormValidator
          :label="fieldLabel(field)"
          :required="fieldRequired(field)"
          :name="fieldVariable(field)"
        >
          <component
            class="input-s"
            :id="fieldVariable(field)"
            :is="getFieldComponent(fieldType(field))"
            v-model="cart[fieldVariable(field)]"
            :placeholder="fieldPlaceholder(field)"
            :options="fieldOptions(field)"
            @click="handleFieldClick(fieldVariable(field))"
          />
        </FormValidator>
      </div>

      <b-button
        class="form-btn-submit"
        @click="submitNodeSelect()"
        :disabled="isDisable || isFetching"
      >
        <span
          v-if="visibleLoading"
          class="spinner-border spinner-border-sm"
          role="status"
          aria-visible="true"
        >
        </span>
        {{ buttonText }}
      </b-button>
    </div>

    <div :id="getAllVariables(moduleSetting)[0]"></div>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import { getCurrentInstance, ref, watch, computed } from "vue";
import useScenarioSetting from "@/composables/chatSetting";
import { useScenarioStore } from "@/stores/scenario";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  getAllVariables,
  onClick,
  buttonText,
  FormLayout,
  cart,
  ready,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getFieldComponent,
  disabledButton,
  handleFieldClick,
  currentVariables,
  socketResult,
} = useCommon(vm);

setProps(props);

const scenarioStore = useScenarioStore();
const { settingDesign } = useScenarioSetting();

const isFetching = ref(false);
const isSelectModified = ref(false);
const hasFetchError = ref(false);

const isCrawlerDataNode = computed(
  () => moduleSetting.value[0]?.crawler_data && moduleSetting.value.length === 1
);
const isDataLoaded = computed(() => {
  return (
    moduleSetting.value[0]?.options &&
    moduleSetting.value[0]?.options.length > 0 &&
    !isFetching.value
  );
});
const isButtonUpdate = computed(() => buttonText.value === "更新");
const isDisable = computed(() => {
  if (isButtonUpdate.value && isSelectModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

async function submitNodeSelect() {
  isSelectModified.value = false;
  await onClick();
}

function fetchCrawlerData() {
  isFetching.value = true;
  scenarioStore.callCrawlerData(moduleSetting.value[0]?.crawler_data);
}

watch(
  () => socketResult.value,
  (value) => {
    if (socketResult.value.data && socketResult.value.data.optionValues) {
      moduleSetting.value[0].options = value.data.optionValues;
      hasFetchError.value = moduleSetting.value[0].options.length === 0;
      isFetching.value = false;
      scenarioStore.scrollToBottom();
      socketResult.value = {};
    }
  },
  { immediate: true }
);

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isSelectModified.value = true;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.crawler-fetch-button {
  width: 100%;
  background-color: v-bind("settingDesign.formBtnBgColor");
  color: v-bind("settingDesign.formBtnTxtColor");
}
</style>
