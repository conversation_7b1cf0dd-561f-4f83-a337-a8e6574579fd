<template>
  <FormLayout>
    <div v-for="field in moduleSetting" class="mt-3">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        class="mt-3"
      >
        <component
          :is="getFieldComponent(fieldType(field))"
          :id="fieldVariable(field)"
          :type="getInputType(fieldVariable(field))"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          :options="getOptionsPrefectures(fieldVariable(field))"
          class="input-s"
          @input="validateInput(field, $event)"
          @click="spReadonly ? handleFieldClick(fieldVariable(field)) : ''"
          :readonly="spReadonly ? handelReadonly(fieldVariable(field)) : false"
        />
      </FormValidator>
      <InputSuggestion
        class="app-footer"
        :open-suggestion="zipcodeOpenSuggestion || address02OpenSuggestion"
        :input-type="inputType"
        :value="inputType === 'zipcode' ? cart.zipcode : cart.address02"
        @input="setInput($event)"
        @close="inputSuggestionClose"
      />
    </div>
    <b-button
      class="form-btn-submit"
      @click="submitNodeAddress()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import * as JpPrefecture from "jp-prefectures";
import postalCode from "jp-postalcode-lookup";
import InputSuggestion from "Components/shared/InputSuggestion.vue";
import {
  ref,
  defineProps,
  onMounted,
  watch,
  getCurrentInstance,
  computed,
  defineEmits,
} from "vue";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import {
  mobileCheck,
  handleInputInBrowser,
  convertFullWidthToHalfWidth,
} from "@/ultilities/helper.js";
import { HALF_WIDTH_INPUT_FIELDS } from "@/ultilities/constants";
import { pick } from "lodash";

//// Define Props
const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
//// Define Emit

const emit = defineEmits(["change"]);

const vm = getCurrentInstance();

//// Define Ref
const isSp = computed(() => mobileCheck());
const spReadonly = computed(() => {
  return isSp.value && supportUiEnable.value;
});

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  getInputType,
  fieldRequired,
  fieldPlaceholder,
  getFieldComponent,
  inputType,
  supportUiEnable,
  handleFieldClick,
  getAllVariables,
  disabledButton,
  currentVariables,
} = useCommon(vm);

setProps(props);

//// Define Store
const globalStore = useGlobalStore();
const { address02OpenSuggestion, zipcodeOpenSuggestion } =
  storeToRefs(globalStore);

//// Function

async function submitNodeAddress() {
  isCartModified.value = false;

  const arrKey = getAllVariables(moduleSetting.value);
  arrKey.forEach((item) => {
    if (HALF_WIDTH_INPUT_FIELDS.includes(item)) {
      handleInputInBrowser(item, cart.value);
    }
  });
  await onClick();
}

function isPrefectures(variable) {
  return variable == "prefectures";
}

function getOptionsPrefectures(variable) {
  return isPrefectures(variable) ? JpPrefecture.prefectureNames() : [];
}

function handelReadonly(field) {
  return field === "address02" || field === "zipcode";
}

function setInput(value) {
  if (inputType.value === "address02") {
    cart.value.address02 = value;
  }
  if (inputType.value === "zipcode") {
    cart.value.zipcode = value;
  }
}

function handleClickOutside(event) {
  if (
    !event.target.closest(".input-s") &&
    !event.target.closest(".app-footer")
  ) {
    zipcodeOpenSuggestion.value = false;
    address02OpenSuggestion.value = false;
    inputType.value = "";
    emit("change", { active: false, id: "" });
  }
}

function inputSuggestionClose() {
  const ipt = inputType.value;
  inputType.value = "";
  emit("change", { active: false, id: ipt });
}

const validateInput = (field, value) => {
  if (fieldVariable.value(field) === "zipcode") {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value.zipcode = value.replace(/[^0-9]/g, "");
    }
  }
};

const isCartModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isCartModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isCartModified.value = true;
    }
  },
  { immediate: true }
);

//// Watch
watch(
  () => cart.value.zipcode,
  (value) => {
    if (!value) return;

    if (value.length !== 7) return;
    const valueConverted = convertFullWidthToHalfWidth(cart.value["zipcode"]);

    postalCode.get(valueConverted, (results) => {
      if (!results) return;

      cart.value.prefectures = results.prefecture;
      cart.value.address01 = results.city + results.area + results.street;

      if (!cart.value.address02 && spReadonly.value) {
        zipcodeOpenSuggestion.value = false;
        emit("change", { active: false, id: "" });

        setTimeout(() => {
          handleFieldClick("address02");
        }, 500);
      }
    });
  }
);

//// ONMOUNTED
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});
</script>
<style scoped>
.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
</style>
