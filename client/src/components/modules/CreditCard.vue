<template>
  <FormLayout>
    <div :id="variable">
      <div v-for="field in moduleSetting" class="mt-3">
        <p
          v-if="field.settings && field.label.length > 0"
          style="font-weight: bold"
        >
          {{ fieldLabel(field) }}
        </p>
        <div
          :class="`${mappingFieldLayout(
            fieldLayout(field)
          )} justify-content-between`"
        >
          <div
            v-if="field.settings"
            v-for="fieldChild in field.settings"
            :style="{
              width: getWidthChildField(field, fieldChild),
            }"
          >
            <FormValidator
              :label="fieldLabel(fieldChild)"
              :required="fieldRequired(fieldChild)"
              :name="fieldVariable(fieldChild)"
              class="mt-2"
            >
              <component
                class="input-s"
                :is="getFieldComponent(fieldType(fieldChild))"
                :options="getOptions(fieldChild)"
                v-model="creditCardInfo[fieldVariable(fieldChild)]"
                :id="fieldVariable(fieldChild)"
                :placeholder="fieldPlaceholder(fieldChild)"
                @click="handleFieldClick(variable)"
              />
            </FormValidator>
          </div>

          <FormValidator
            v-else
            :label="fieldLabel(field)"
            :required="fieldRequired(field)"
            :name="fieldVariable(field)"
            class="mt-3"
          >
            <component
              class="input-s"
              :is="getFieldComponent(fieldType(field))"
              :id="fieldVariable(field)"
              :type="getInputType(fieldVariable(field))"
              v-model="creditCardInfo[fieldVariable(field)]"
              :placeholder="fieldPlaceholder(field)"
              :options="getOptions(field)"
              @input="validateInput(field, $event)"
              @click="handleFieldClick(variable)"
            />
          </FormValidator>
        </div>
      </div>
      <b-button
        class="form-btn-submit"
        @click="submitNodeCardInfo(true)"
        :disabled="disabledButton"
      >
        <span
          v-if="visibleLoading"
          class="spinner-border spinner-border-sm"
          role="status"
          aria-visible="true"
        ></span>
        {{ buttonText }}
      </b-button>
    </div>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import {
  ref,
  reactive,
  watch,
  onMounted,
  computed,
  getCurrentInstance,
} from "vue";
import { UnicornCartChatbot } from "@/composables/unicorn_cart_chatbot";
import { handleInputInBrowser } from "@/ultilities/helper.js";
import { Buffer } from "buffer";
import { HALF_WIDTH_INPUT_FIELDS } from "@/ultilities/constants";

const creditCard = reactive(new UnicornCartChatbot());
const creditCardInfo = ref(creditCard.creditCardInfo);
const isCreditCardModified = ref(false);
const variable = computed(() => props.body.settings[0].variable);
const disabledButton = computed(() => {
  if (
    buttonText.value === "更新" &&
    isCreditCardModified.value &&
    !disableCreditCardButton()
  ) {
    return false;
  }

  if (buttonText.value === "更新") {
    return true;
  }

  return disableCreditCardButton();
});

function disableCreditCardButton() {
  for (let field of moduleSetting.value) {
    if (field.settings) {
      for (let fieldChild of field.settings) {
        if (
          fieldRequired.value(fieldChild) &&
          !creditCardInfo.value[fieldVariable.value(fieldChild)]
        ) {
          return true;
        }
      }
    } else if (
      fieldRequired.value(field) &&
      !creditCardInfo.value[fieldVariable.value(field)]
    ) {
      return true;
    }
  }
  return false;
}

watch(
  creditCardInfo,
  () => {
    window.parent.creditCard = creditCardInfo.value;
    cart.value[variable.value] = Buffer.from(
      JSON.stringify(window.parent.creditCard),
      "utf-8"
    ).toString("base64");

    isCreditCardModified.value = true;
  },
  { deep: true }
);

onMounted(() => {
  if (window.parent && window.parent.creditCard) {
    delete window.parent.creditCard;
  }
});

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const vm = getCurrentInstance();
const {
  onClick,
  buttonText,
  FormLayout,
  setProps,
  visibleLoading,
  cart,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  getFieldComponent,
  fieldLayout,
  getWidthChildField,
  mappingFieldLayout,
  currentVariables,
  schema,
  getInputType,
  getAllVariables,
  handleFieldClick,
} = useCommon(vm);

setProps(props);

async function validateInput(field, value) {
  const fieldType = fieldVariable.value(field);

  if (isCardField(fieldType)) {
    const sanitizedValue = sanitizeValue(value);

    if (sanitizedValue !== value) {
      creditCardInfo.value[fieldType] = sanitizedValue;
    }
  }
}

function isCardField(fieldType) {
  return fieldType === "card_number" || fieldType === "card_cvv";
}

function sanitizeValue(value) {
  return value.replace(/[^0-9]/g, "");
}

async function submitNodeCardInfo() {
  isCreditCardModified.value = false;
  const arrKey = getAllVariables(moduleSetting.value);
  arrKey.forEach((item) => {
    if (HALF_WIDTH_INPUT_FIELDS.includes(item)) {
      handleInputInBrowser(item, creditCardInfo.value);
    }
  });

  window.parent.creditCard = creditCardInfo.value;

  await onClick();
}

function getOptions(field) {
  if (field.options) return field.options;

  switch (field.variable) {
    case "card_expired_month":
      return optionsMonth();
    case "card_expired_year":
      return optionsYear();
    default:
      return [];
  }
}

function optionsMonth() {
  return Array.from({ length: 12 }, (_, i) => String(i + 1).padStart(2, "0"));
}

function optionsYear() {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: 11 }, (_, i) =>
    (currentYear - 2000 + i).toString()
  );
}
</script>

<style scoped></style>
