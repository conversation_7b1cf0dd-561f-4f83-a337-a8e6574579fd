<template>
  <FormLayout>
    <div v-for="(field, fieldIndex) in moduleSetting" :key="fieldIndex">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <b-form-radio-group
          class="radio-btn"
          :id="fieldVariable(field)"
          :value="radioValue[field]"
          buttons
          stacked
          size="lg"
          @update:model-value="radioValueChanged(field, $event)"
        >
          <b-form-radio
            v-for="option in getOptions(field)"
            :key="option.value"
            :value="option.value"
            :checked="isChecked(field, option.value)"
            @click="customOnclick(fieldVariable(field), option.value)"
          >
            {{ option.text }}
          </b-form-radio>
        </b-form-radio-group>
      </FormValidator>
    </div>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  FormLayout,
  cart,
  setProps,
  moduleSetting,
  fieldLabel,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  onClick,
  handleFieldClick,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();

function getOptions(field) {
  const options = fieldOptions.value(field);

  return options.map((option) => ({
    text: option.text,
    value: option.text,
  }));
}
function radioValue(field) {
  cart.value[fieldVariable.value(field)];
}

function radioValueChanged(field, value) {
  cart.value[fieldVariable.value(field)] = value;
}

async function customOnclick(variable, value) {
  cart.value[variable] = value;
  await onClick();
  await handleFieldClick(variable);
}

function isChecked(field, value) {
  return cart.value[fieldVariable.value(field)] === value;
}
</script>

<style scoped lang="scss">
.radio-btn {
  width: 100%;
  margin: 0 auto;
  /* padding: 15px 45px; */
  color: white;
  display: flex;
}

.radio-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.radio-btn :deep(label) {
  background-color: white;
  margin: 10px 0 !important;
  color: v-bind("settingDesign.initiateBtnBgColor");

  border: 2px solid v-bind("settingDesign.initiateBtnBgColor");
  border-bottom-right-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}

.radio-btn :deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
}

.radio-btn :deep(.btn-check:checked + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.initiateBtnBgColor");

  &:hover {
    opacity: 0.85;
  }
}
</style>
