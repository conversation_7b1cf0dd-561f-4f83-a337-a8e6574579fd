<template>
  <FormLayout>
    <div v-for="(field, fieldIndex) in moduleSetting" :key="fieldIndex">
      <div v-if="enableEditButton(field)" class="edit-button">
        <div class="radio-update" @click="canUpdate = true">く 修正する</div>
        <div class="radio-value">{{ cart[fieldVariable(field)] }}</div>
      </div>
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        v-else
      >
        <b-form-radio-group
          class="radio-group"
          :id="fieldVariable(field)"
          :value="radioValue[field]"
          buttons
          stacked
          size="lg"
          @update:model-value="radioValueChanged(field, $event)"
        >
          <b-form-radio
            v-for="option in getOptions(field)"
            :key="option.value"
            :value="option.value"
            :checked="isChecked(field, option.value)"
            @click="customOnclick(fieldVariable(field), option.value)"
          >
            {{ option.text }}
          </b-form-radio>
        </b-form-radio-group>
      </FormValidator>
    </div>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref, getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  FormLayout,
  cart,
  setProps,
  moduleSetting,
  fieldLabel,
  fieldVariable,
  fieldOptions,
  fieldRequired,
  onClick,
  handleFieldClick,
  fieldEnableEditButton,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();
//

const canUpdate = ref(false);

function enableEditButton(field) {
  return (
    cart.value[fieldVariable.value(field)] &&
    !canUpdate.value &&
    fieldEnableEditButton.value(field)
  );
}

function getOptions(field) {
  const options = fieldOptions.value(field);

  return options.map((option) => ({
    text: option.text,
    value: option.text,
  }));
}

function radioValue(field) {
  cart.value[fieldVariable.value(field)];
}

function radioValueChanged(field, value) {
  cart.value[fieldVariable.value(field)] = value;
  canUpdate.value = false;
}

async function customOnclick(variable, value) {
  cart.value[variable] = value;
  canUpdate.value = false;
  await onClick();
  await handleFieldClick(variable);
}

function isChecked(field, value) {
  return cart.value[fieldVariable.value(field)] === value;
}
</script>

<style scoped lang="scss">
.radio-group {
  width: 100%;
  margin: 0 auto;
  /* padding: 15px 45px; */
  color: white;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.radio-group:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.radio-group :deep(label) {
  background-color: white;
  margin: 10px 0 !important;
  color: v-bind("settingDesign.initiateBtnBgColor");

  border: 2px solid v-bind("settingDesign.initiateBtnBgColor");
  border-bottom-right-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
}

.radio-group :deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
}

.radio-group :deep(.btn-check:checked + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.initiateBtnBgColor");

  &:hover {
    opacity: 0.85;
  }
}
.edit-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.radio-value {
  padding: 10px;
  background-color: v-bind("settingDesign.operatorMsgBodyBgColor");
  color: v-bind("settingDesign.operatorMsgBodyTxtColor");
  width: fit-content;
  margin-left: 10px;
  border-radius: 10px 10px 0px 10px;
  font-size: 24px;
}

.radio-update {
  cursor: pointer;
}
</style>
