<template>
  <FormLayout>
    <div v-for="field in moduleSetting">
      <div v-if="fieldType(field) === BUTTON_TYPE.CHECKBOX">
        <form-validator class="mt-4 pt-2 mb-3" :name="fieldVariable(field)">
          <b-form-checkbox
            :name="fieldVariable(field)"
            :id="fieldVariable(field)"
            v-model="cart[fieldVariable(field)]"
          >
            <div v-html="fieldContent(field)"></div>
          </b-form-checkbox>
        </form-validator>
      </div>
      <div v-if="fieldType(field) === BUTTON_TYPE.BUTTON">
        <div class="text-center my-5">
          <b-button
            size="lg"
            variant="success"
            type="submit"
            name="submit"
            id="submit"
            @click="onClick()"
            class="confirm-btn"
            :disabled="!allRequiredChecked"
          >
            {{ fieldContent(field) }}
          </b-button>
        </div>
      </div>
    </div>
  </FormLayout>
</template>

<script setup>
import { BUTTON_TYPE } from "@/ultilities/constants";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref, computed } from "vue";
import { getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  FormLayout,
  setProps,
  moduleSetting,
  fieldContent,
  fieldType,
  cart,
  fieldRequired,
  fieldVariable,
  handleFieldClick,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();

const allRequiredChecked = computed(() => {
  return moduleSetting.value.every((field) => {
    if (
      fieldType.value(field) === BUTTON_TYPE.CHECKBOX &&
      fieldRequired.value(field)
    ) {
      return !!cart.value[fieldVariable.value(field)];
    }
    return true;
  });
});
</script>

<style scoped>
.confirm-btn {
  width: 100%;
  background: v-bind("settingDesign.initiateBtnBgColor");
  margin: 0 auto;
  padding: 15px 45px;
  text-align: center;
  text-transform: uppercase;
  transition: 0.5s;
  background-size: 200% auto;
  color: white;
  box-shadow: 0 0 20px #eee;
  border: none;
  border-radius: 60px;
  display: block;
}

.confirm-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.confirm-btn:disabled {
  opacity: 0.5;
}
.textbox {
  border: 1px solid v-bind("settingDesign.formInputBorderColor");
  padding: 5px;
  text-wrap: wrap;
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 200px;
}
</style>
