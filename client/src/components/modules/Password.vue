<template>
  <FormLayout>
    <div v-for="(field, index) in moduleSetting" :key="index">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
      >
        <div class="input-container">
          <component
            class="input-s"
            :id="fieldVariable(field)"
            :is="getFieldComponent(fieldType(field))"
            v-model="cart[fieldVariable(field)]"
            :type="hidePass ? 'text' : 'password'"
            :placeholder="fieldPlaceholder(field)"
            @click="handleFieldClick(fieldVariable(field))"
          />
          <font-awesome-icon
            :icon="hidePass ? 'fa-eye' : 'fa-eye-slash'"
            class="hide-icon"
            @click="hidePassword"
          />
        </div>
      </FormValidator>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodePassword()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { ref, watch, computed, defineProps, getCurrentInstance } from "vue";
import useCommon from "@/composables/common";
import { library } from "@fortawesome/fontawesome-svg-core";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import { pick } from "lodash";
library.add(faEye, faEyeSlash);

const hidePass = ref(false);

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  getFieldComponent,
  handleFieldClick,
  disabledButton,
  currentVariables,
} = useCommon(vm);

setProps(props);

async function submitNodePassword() {
  isPasswordModified.value = false;
  await onClick();
}

const isPasswordModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isPasswordModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isPasswordModified.value = true;
    }
  },
  { immediate: true }
);

function hidePassword() {
  hidePass.value = !hidePass.value;
}
</script>

<style scoped>
.input-container {
  position: relative;
}

.input-container .hide-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: black;
}
</style>
