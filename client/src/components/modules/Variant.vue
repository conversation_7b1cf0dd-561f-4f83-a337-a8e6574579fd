<template>
  <FormLayout v-if="ready">
    <div v-for="(option, index) in fields" :key="index">
      <FormValidator
        :label="option.label"
        :name="`${moduleSetting[0].variable}_${index}`"
        required
      >
        <component
          class="input-s"
          :is="getFieldComponent(fieldType(field))"
          :id="fieldVariable(field)"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          :options="fieldOptions(field)"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeVariant()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { get } from "lodash";
import { pick } from "lodash";
import useCommon from "@/composables/common";
import { getCurrentInstance } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const fields = computed(() => get(dataCrawler.value, "data", {}));
const vm = getCurrentInstance();

const {
  ready,
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldType,
  fieldPlaceholder,
  dataCrawler,
  isLastNode,
  disabledButton,
  handleFieldClick,
  currentVariables,
} = useCommon(vm);

setProps(props);

async function submitNodeVariant() {
  isVariantModified.value = false;
  await onClick();
}

const isVariantModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isVariantModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isVariantModified.value = true;
    }
  },
  { immediate: true }
);
</script>
