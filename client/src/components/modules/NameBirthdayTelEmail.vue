<template>
  <FormLayout>
    <ConvertName ref="convertNameComponent" />

    <div v-for="field in moduleSetting">
      <div v-if="field.settings && displayField(field)" class="mt-3">
        <span style="font-weight: bold">
          {{ fieldLabel(field) }}
        </span>

        <div class="layout_horizontal justify-content-between">
          <div
            v-for="fieldChild in field.settings"
            :style="{
              width: fetchWidthChildField(field.settings),
            }"
          >
            <FormValidator
              :label="fieldLabel(fieldChild) || '　'"
              :required="fieldRequired(fieldChild)"
              :name="fieldVariable(fieldChild)"
              class="mt-3"
            >
              <component
                class="input-s"
                :is="
                  birthdayVariables.includes(fieldVariable(fieldChild))
                    ? 'b-form-select'
                    : 'b-form-input'
                "
                v-model="cart[fieldVariable(fieldChild)]"
                :id="fieldVariable(fieldChild)"
                :options="getOptions(fieldChild)"
                @keyup.delete="handleKanaName(fieldVariable(fieldChild))"
                :placeholder="fieldPlaceholder(fieldChild)"
                @click="handleFieldClick(fieldVariable(fieldChild))"
              />
            </FormValidator>
          </div>
        </div>
      </div>

      <FormValidator
        v-if="!field.settings"
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        class="mt-3"
      >
        <form action="#" onsubmit="return false;" autocomplete="on">
          <b-form-input
            class="input-s"
            v-model="cart[fieldVariable(field)]"
            :type="getInputType(fieldVariable(field))"
            :id="fieldVariable(field)"
            :placeholder="fieldPlaceholder(field)"
            @input="checkInput(field, $event)"
            @click="spReadonly ? handleFieldClick(fieldVariable(field)) : ''"
            :readonly="
              spReadonly ? handelReadonly(fieldVariable(field)) : false
            "
          />
        </form>
      </FormValidator>

      <InputSuggestion
        class="app-footer"
        @input="setInput($event, fieldVariable(field))"
        :open-suggestion="telOpenSuggestion"
        :input-type="`tel`"
        :value="cart.tel"
        @close="inputSuggestionClose"
      />
    </div>
    <b-button
      v-if="displayButtonSubmit"
      class="form-btn-submit"
      @click="submitNodeText()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { computed, onMounted, watch, ref, getCurrentInstance } from "vue";
import { toKatakana, isKana } from "wanakana";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { mobileCheck, handleInputInBrowser } from "@/ultilities/helper.js";
import ConvertName from "@/components/supportUi/ConvertName.vue";
import InputSuggestion from "Components/shared/InputSuggestion.vue";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";
import { pick } from "lodash";

// ==========Icon===========
import { library } from "@fortawesome/fontawesome-svg-core";
import { faPen } from "@fortawesome/free-solid-svg-icons";
library.add(faPen);
// =========================

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

//// Define Emit
const emit = defineEmits(["change"]);

const isSp = computed(() => mobileCheck());
const spReadonly = computed(() => {
  return isSp.value && supportUiEnable.value;
});

const globalStore = useGlobalStore();
const { telOpenSuggestion } = storeToRefs(globalStore);
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  disabledButton,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  handleFieldClick,
  supportUiEnable,
  getInputType,
  inputType,
  getAllVariables,
  currentVariables,
} = useCommon(vm);

const { settingDesign } = useScenarioSetting();
const birthdayVariables = ["day", "month", "year"];
setProps(props);

watch(
  () => cart.value.sei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${value}${cart.value.mei}`);
    if (!isKana(value)) return;

    const seiKana = toKatakana(value);

    cart.value.seifuri = seiKana;
  }
);

watch(
  () => cart.value.mei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${cart.value.sei}${value}`);
    if (!isKana(value)) return;

    const meiKana = toKatakana(value);

    cart.value.meifuri = meiKana;
  }
);

// Functions

function setFullName(value) {
  cart.value.fullName = value;
}

function handleKanaName(field) {
  if (field === "sei") {
    if (!cart.value.sei) cart.value.seifuri = "";
  } else if (field === "mei") {
    if (!cart.value.mei) cart.value.meifuri = "";
  } else {
    return;
  }
}

async function submitNodeText() {
  isNameModified.value = false;

  const arrKey = getAllVariables(moduleSetting.value);
  if (arrKey.includes("tel")) {
    handleInputInBrowser("tel", cart.value);
  }
  await onClick();
}

const isNameModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isNameModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isNameModified.value = true;
    }
  },
  { immediate: true }
);

const fetchWidthChildField = (settings) => {
  return "calc((100% / " + settings.length + ") - 1%)";
};

const getOptions = (field) => {
  switch (field.variable) {
    case "day":
      return optionsDay();
    case "month":
      return optionsMonth();
    case "year":
      return optionsYear();
  }
};

const optionsMonth = () => {
  return Array.from({ length: 12 }, (_, i) => i + 1);
};

const optionsYear = () => {
  return Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i);
};

const optionsDay = () => {
  const date = new Date();
  const year = cart.value.year || date.getFullYear();
  const month = cart.value.month || date.getMonth() + 1; // January gives 0

  const optionsLength = new Date(year, month, 0).getDate();

  const options = Array.from({ length: optionsLength }, (_, i) => i + 1);

  if (cart.value.day && !options.includes(cart.value.day)) cart.value.day = ``;

  return options;
};

// ==========Support UI And Convert Name=================================
const convertNameComponent = ref(null);

const displayButtonSubmit = computed(
  () => !supportUiEnable.value || convertNameComponent.value?.convertCompleted
);

const displayField = (field) => {
  if (!supportUiEnable.value) return true;

  const fieldBirthday = field.settings.some((fieldChild) =>
    birthdayVariables.includes(fieldVariable.value(fieldChild))
  );

  return fieldBirthday || convertNameComponent.value?.convertCompleted;
};

const checkInput = (field, value) => {
  if (fieldVariable.value(field) === "tel") {
    const isValid = /^[0-9]*$/.test(value);

    if (!isValid) {
      cart.value.tel = value.replace(/[^0-9]/g, "");
    }
  }
};

// ==========Support InputSuggestion=================================
function handelReadonly(field) {
  return field === "tel";
}

function handleClickOutside(event) {
  if (
    !event.target.closest(".input-s") &&
    !event.target.closest(".app-footer")
  ) {
    telOpenSuggestion.value = false;
    const ipt = inputType.value;
    inputType.value = "";
    emit("change", { active: false, id: "" });
  }
}
function setInput(value, field) {
  cart.value.tel = value;
}

function inputSuggestionClose() {
  const ipt = inputType.value;
  inputType.value = "";
  emit("change", { active: false, id: ipt });
}

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});
</script>

<style scoped lang="scss">
:deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: white;
}

:deep(.form-control-lg) {
  padding: 0px !important;
  min-height: unset;
  width: 50%;
  display: flex;
}

.convert-btn {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  color: #fff;
}
.app-footer {
  background-color: #ffffff;
  color: red;
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 0px;
}
</style>
