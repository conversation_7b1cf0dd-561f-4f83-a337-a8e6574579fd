<template>
  <FormLayout>
    <div v-if="isKanaComponentVisible" class="mt-3">
      <p style="font-weight: bold">お名前</p>

      <FormValidator class="mt-1">
        <component
          class="form-control input-s"
          is="input"
          id="fullName"
          :value="getFullName()"
          @input="setFullName($event.target.value)"
          @click="handleFieldClick('fullName')"
        />
      </FormValidator>
      <div class="note-name mt-2">
        <div class="d-flex">
          <div v-if="converting" class="d-flex align-items-center">
            <span
              style="color: #d7eecf"
              class="spinner-border spinner-border-sm"
              role="status"
              aria-visible="true"
            ></span>
            <span class="mx-2" style="color: #97e51e">変換中</span>
          </div>
          <div v-if="isNameConverted">
            {{ cart.sei }}
            {{ cart.mei }}
            (
            {{ cart.seifuri }}
            {{ cart.meifuri }})
          </div>
        </div>

        <div>
          <span class="mt-1 convert-btn" @click="onConvertTxt()">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              fill="#000000"
              height="15px"
              width="15px"
              version="1.1"
              id="Capa_1"
              viewBox="0 0 306.637 306.637"
              xml:space="preserve"
            >
              <g>
                <g>
                  <path
                    d="M12.809,238.52L0,306.637l68.118-12.809l184.277-184.277l-55.309-55.309L12.809,238.52z M60.79,279.943l-41.992,7.896    l7.896-41.992L197.086,75.455l34.096,34.096L60.79,279.943z"
                  />
                  <path
                    d="M251.329,0l-41.507,41.507l55.308,55.308l41.507-41.507L251.329,0z M231.035,41.507l20.294-20.294l34.095,34.095    L265.13,75.602L231.035,41.507z"
                  />
                </g>
              </g>
            </svg>
            名前を交換
          </span>
        </div>
      </div>
    </div>
    <div
      v-if="!isKanaComponentVisible"
      v-for="field in moduleSetting"
      class="mt-3"
    >
      <p style="font-weight: bold">{{ fieldLabel(field) }}</p>
      <div
        :class="`${mappingFieldLayout(
          fieldLayout(field)
        )} justify-content-between`"
      >
        <div
          v-if="field.settings"
          v-for="fieldChild in field.settings"
          :style="{
            width: getWidthChildField(field, fieldChild),
          }"
        >
          <FormValidator
            :label="fieldLabel(fieldChild)"
            :required="fieldRequired(fieldChild)"
            :name="fieldVariable(fieldChild)"
            class="mt-1"
          >
            <component
              class="input-s"
              :id="fieldVariable(fieldChild)"
              :is="getFieldComponent(fieldType(fieldChild))"
              v-model="cart[fieldVariable(fieldChild)]"
              :placeholder="fieldPlaceholder(fieldChild)"
              @keyup.delete="handleKanaName(fieldVariable(fieldChild))"
              @click="handleFieldClick(fieldVariable(fieldChild))"
            />
          </FormValidator>
        </div>
      </div>
    </div>

    <b-button
      v-if="!isKanaComponentVisible"
      class="form-btn-submit"
      @click="submitNodeName()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { ref, watch, computed, getCurrentInstance } from "vue";
import useCommon from "@/composables/common";
import { toKatakana, isKana } from "wanakana";
import { useConvertNameStore } from "@/stores/convertName";
import useScenarioSetting from "@/composables/chatSetting";
import { handleScrollIntoView } from "@/ultilities/helper.js";
import { pick } from "lodash";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  disabledButton,
  getFieldComponent,
  fieldLayout,
  getWidthChildField,
  mappingFieldLayout,
  supportUiEnable,
  container,
  handleFieldClick,
  currentVariables,
} = useCommon(vm);

setProps(props);

async function submitNodeName() {
  isCartModified.value = false;
  await onClick();
}

const isCartModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isCartModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isCartModified.value = true;
    }
  },
  { immediate: true }
);

watch(
  () => cart.value.sei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${value}${cart.value.mei}`);
    if (!isKana(value)) return;

    const seiKana = toKatakana(value);

    cart.value.seifuri = seiKana;
  }
);

watch(
  () => cart.value.mei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${cart.value.sei}${value}`);
    if (!isKana(value)) return;

    const meiKana = toKatakana(value);

    cart.value.meifuri = meiKana;
  }
);

function handleKanaName(field) {
  if (field === "sei") {
    if (!cart.value.sei) cart.value.seifuri = "";
  } else if (field === "mei") {
    if (!cart.value.mei) cart.value.meifuri = "";
  } else {
    return;
  }
}
const { settingDesign } = useScenarioSetting();
const convertNameStore = useConvertNameStore();

const hideName = ref(false);
const converting = ref(false);

const isKanaComponentVisible = computed(() => {
  return !hideName.value && supportUiEnable.value;
});

const isNameConverted = computed(() => {
  return (
    cart.value.fullName === `${cart.value.sei}${cart.value.mei}` &&
    cart.value.sei &&
    cart.value.mei &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
});

function getFullName() {
  return cart.value.fullName;
}

function setFullName(value) {
  cart.value.fullName = value;
}

function onConvertTxt() {
  if (shouldConvertFullName()) {
    startConverting();
  } else if (isFullNameConverted()) {
    hideName.value = true;
  } else if (!cart.value.fullName || !cart.value.fullName.length) {
    clearNameFields();
    hideName.value = true;
  }

  setTimeout(() => {
    const el = document.getElementById("meifuri");
    handleScrollIntoView(el);
  }, 500);
}

function shouldConvertFullName() {
  return (
    (cart.value.fullName &&
      cart.value.fullName.length &&
      !cart.value.sei &&
      !cart.value.mei &&
      !cart.value.seifuri &&
      !cart.value.meifuri) ||
    (cart.value.fullName &&
      cart.value.fullName.length &&
      cart.value.fullName !== `${cart.value.sei}${cart.value.mei}` &&
      cart.value.sei &&
      cart.value.mei &&
      cart.value.seifuri &&
      cart.value.meifuri)
  );
}

function isFullNameConverted() {
  return (
    cart.value.fullName &&
    cart.value.fullName.length &&
    cart.value.sei &&
    cart.value.mei &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
}

function startConverting() {
  converting.value = true;
  setTimeout(() => {
    convertNameStore.getKana(cart.value.fullName);
    converting.value = false;
    hideName.value = true;
  }, 500);
}

function clearNameFields() {
  cart.value.sei = null;
  cart.value.mei = null;
  cart.value.seifuri = null;
  cart.value.meifuri = null;
}
</script>

<style>
.convert-btn {
  cursor: pointer;
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  padding: 5px;
  cursor: pointer;
  color: #fff;
  font-size: 10px;
}
.note-name {
  font-size: 10px;

  display: flex;
  justify-content: space-between;
}
</style>
