<template>
  <div class="d-flex my-2 align-items-end">
    <div class="bot-avatar">
      <img :src="settingGeneral.chatOperatorImgUrl" class="avatar" />
    </div>

    <div>
      <p class="bot-name">{{ settingGeneral.chatOperatorName }}</p>
      <div class="bot-message" :id="uid">
        <div class="typing" v-if="isTyping">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>

        <ul
          v-for="field in moduleSetting"
          class="flex-direction-column my-4"
          :style="{ display: isTyping ? `none` : `flex` }"
        >
          <li v-if="fieldType(field) === MESSAGE_TYPE.TEXT">
            <span
              class="bot-typing"
              v-html="convertMessageContent(messageContent)"
            ></span>
          </li>

          <li>
            <img
              class="w-100"
              v-if="fieldType(field) === MESSAGE_TYPE.IMAGE"
              :src="fieldContent(field)"
            />
          </li>

          <li>
            <img
              class="w-100"
              v-if="fieldType(field) === MESSAGE_TYPE.IMAGES"
              :src="fieldContent(field)"
            />
          </li>

          <li>
            <video
              v-if="fieldType(field) === MESSAGE_TYPE.VIDEO"
              width="320"
              height="240"
              controls
            >
              <source :src="fieldContent(field)" type="video/mp4" />
            </video>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MESSAGE_TYPE } from "@/ultilities/constants";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { ref, watch } from "vue";
import { useScenarioStore } from "@/stores/scenario";
import { storeToRefs } from "pinia";
import { ANALY_ACTION } from "@/ultilities/constants";

const scenarioStore = useScenarioStore();
const { scenario } = storeToRefs(scenarioStore);

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const isTyping = ref(true);
const timeOut = ref(1000);
const messageContent = ref(props.body.settings[0].content);
const {
  setProps,
  moduleSetting,
  fieldContent,
  fieldType,
  isLastNode,
  uid,
  socketResult,
  cart,
  nodes,
  handleFieldClick,
} = useCommon();

setProps(props);

const { settingGeneral, settingDesign } = useScenarioSetting();

function nextNode() {
  if (nodes.value.at(-1)?.id == props.id) {
    scenarioStore.onNextNode(
      isLastNode.value,
      uid.value,
      props.nextNodeUid,
      props.id
    );
  }
  isTyping.value = false;

  stopWatch();

  socketResult.value = {};
}

const stopWatch = watch(
  socketResult,
  (value) => {
    handleTypingResult();
  },
  { immediate: true }
);

function handleTypingResult() {
  if (!isLastNode.value) timeOut.value = 0;

  if (props.body.settings[0].variable) {
    if (!socketResult.value.data) {
      const content = props.body.settings[0].content;
      if (!content) return;

      setTimeout(() => (isTyping.value = false), timeOut.value);
    } else {
      const isError = socketResult.value.data.error;

      if (isError) {
        cart.value[props.body.settings[0].variable] =
          props.body.settings[0].fallback_error ||
          socketResult.value.data.message;

        messageContent.value =
          props.body.settings[0].fallback_error ||
          socketResult.value.data.message;

        props.body.settings[0].content =
          props.body.settings[0].fallback_error ||
          socketResult.value.data.message;
      } else {
        cart.value[props.body.settings[0].variable] =
          socketResult.value.data.message;

        messageContent.value = socketResult.value.data.message;

        props.body.settings[0].content = socketResult.value.data.message;
      }

      cart.value["error"] = isError;
      props.body.settings[0].isError = isError;

      setTimeout(() => (isTyping.value = false), timeOut.value);
      if (props.body.settings[0].content) {
        scenarioStore.scrollLastMessage(1500);
      }
      socketResult.value = {};

      if (isError) return;

      console.log("conversion", props.nodeUid);
      Analy.P.Input.putChatformLog(
        `${scenario.value.shopId}_chatbot`,
        scenario.value.id,
        props.nodeUid,
        ANALY_ACTION.CONVERSION
      );
    }
  }

  setTimeout(nextNode, timeOut.value);
}

function convertMessageContent(message) {
  return message.replace(/\n/g, "<br>");
}

function botTypeWritting(message) {
  const currentIndex = ref(0); // Reactive reference to track the current index
  const textMessage = ref(""); // Reactive reference for the typed message

  const typeNextCharacter = () => {
    if (currentIndex.value < message.length) {
      // Add one character at a time
      textMessage.value += message.charAt(currentIndex.value);
      currentIndex.value++;

      // Schedule the next character to be typed after a delay
      setTimeout(typeNextCharacter, 50); // Adjust typing speed as needed
    }
  };

  // Start typing characters
  typeNextCharacter();

  return textMessage; // Return the reactive reference for the typed message
}
</script>

<style scoped lang="scss">
.bot-avatar {
  width: 50px;
  border-radius: 50%;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px,
    rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  .avatar {
    width: 50px;
    border-radius: 50%;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 10px 36px 0px,
      rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
  }
}

.bot-name {
  font-size: 16px;
  color: v-bind("settingDesign.operatorNameColor");
  margin-bottom: 0;
}

.bot-message {
  padding: 10px;
  background-color: v-bind("settingDesign.operatorMsgBodyBgColor");
  color: v-bind("settingDesign.operatorMsgBodyTxtColor");
  width: fit-content;
  margin-left: 10px;
  border-radius: 10px 10px 10px 0;
  font-size: 24px;
}

.typing {
  align-items: center;
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  padding: 5px 1rem;
}

.dot {
  border-radius: 9999px;
  height: 0.5rem;
  width: 0.5rem;

  background: rgba(148 163 184 / 1);
  animation: wave 1s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0.3333s;
}
.dot:nth-child(2) {
  animation-delay: 0.6666s;
}
.dot:nth-child(3) {
  animation-delay: 0.9999s;
}

@keyframes wave {
  0% {
    transform: translateY(0px);
    background: rgba(148 163 184 / 0);
  }
  50% {
    transform: translateY(-0.5rem);
    background: rgba(148 163 184 / 0.8);
  }
  100% {
    transform: translateY(0px);
    background: rgba(148 163 184 / 0);
  }
}

.textbox {
  text-wrap: wrap !important;
}
</style>
