<template>
  <FormLayout>
    <ConvertName ref="convertNameComponent" />

    <div v-for="field in moduleSetting">
      <div v-if="field.settings && displayField(field)" class="mt-3">
        <span style="font-weight: bold">
          {{ fieldLabel(field) }}
        </span>

        <div class="layout_horizontal justify-content-between">
          <div
            v-for="fieldChild in field.settings"
            :style="{
              width: fetchWidthChildField(field.settings),
            }"
          >
            <FormValidator
              :label="fieldLabel(fieldChild) || '　'"
              :required="fieldRequired(fieldChild)"
              :name="fieldVariable(fieldChild)"
              class="mt-3"
            >
              <component
                class="input-s"
                :is="
                  birthdayVariables.includes(fieldVariable(fieldChild))
                    ? 'b-form-select'
                    : 'b-form-input'
                "
                v-model="cart[fieldVariable(fieldChild)]"
                :id="fieldVariable(fieldChild)"
                :options="getOptions(fieldChild)"
                @keyup.delete="handleKanaName(fieldVariable(fieldChild))"
                :placeholder="fieldPlaceholder(fieldChild)"
                @click="handleFieldClick(fieldVariable(fieldChild))"
              />
            </FormValidator>
          </div>
        </div>
      </div>

      <FormValidator
        v-if="!field.settings"
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        class="mt-3"
      >
        <b-form-radio-group
          v-model="cart[fieldVariable(field)]"
          :id="fieldVariable(field)"
          :placeholder="fieldPlaceholder(field)"
          :options="sexOptions"
          @click="handleFieldClick(fieldVariable(field))"
          class="w-100 order_sex_radio"
          size="lg"
          stacked
        />
      </FormValidator>
    </div>

    <b-button
      v-if="displayButtonSubmit"
      class="form-btn-submit"
      @click="submitNodeName()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { computed, watch, ref, getCurrentInstance } from "vue";
import { toKatakana, isKana } from "wanakana";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { pick } from "lodash";
import ConvertName from "@/components/supportUi/ConvertName.vue";

// ==========Icon===========
import { library } from "@fortawesome/fontawesome-svg-core";
import { faPen } from "@fortawesome/free-solid-svg-icons";
library.add(faPen);
// =========================

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  disabledButton,
  handleFieldClick,
  supportUiEnable,
  currentVariables,
} = useCommon(vm);

const { settingDesign } = useScenarioSetting();

const sexOptions = [
  { text: "男", value: "男" },
  { text: "女", value: "女" },
];

const birthdayVariables = ["day", "month", "year"];

setProps(props);

async function submitNodeName() {
  isNSBModified.value = false;
  await onClick();
}

const isNSBModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isNSBModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isNSBModified.value = true;
    }
  },
  { immediate: true }
);

watch(
  () => cart.value.sei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${value}${cart.value.mei}`);
    if (!isKana(value)) return;

    const seiKana = toKatakana(value);

    cart.value.seifuri = seiKana;
  }
);

watch(
  () => cart.value.mei,
  (value) => {
    if (cart.value.mei && value) setFullName(`${cart.value.sei}${value}`);
    if (!isKana(value)) return;

    const meiKana = toKatakana(value);

    cart.value.meifuri = meiKana;
  }
);

function setFullName(value) {
  cart.value.fullName = value;
}

function handleKanaName(field) {
  if (field === "sei") {
    if (!cart.value.sei) cart.value.seifuri = "";
  } else if (field === "mei") {
    if (!cart.value.mei) cart.value.meifuri = "";
  } else {
    return;
  }
}

// Functions
const fetchWidthChildField = (settings) => {
  return "calc((100% / " + settings.length + ") - 1%)";
};

const getOptions = (field) => {
  switch (field.variable) {
    case "day":
      return optionsDay();
    case "month":
      return optionsMonth();
    case "year":
      return optionsYear();
  }
};

const optionsMonth = () => {
  return Array.from({ length: 12 }, (_, i) => i + 1);
};

const optionsYear = () => {
  return Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i);
};

const optionsDay = () => {
  const date = new Date();
  const year = cart.value.year || date.getFullYear();
  const month = cart.value.month || date.getMonth() + 1; // January gives 0

  const optionsLength = new Date(year, month, 0).getDate();

  const options = Array.from({ length: optionsLength }, (_, i) => i + 1);

  if (cart.value.day && !options.includes(cart.value.day)) cart.value.day = ``;

  return options;
};

// ==========Support UI And Convert Name=================================
const convertNameComponent = ref(null);

const displayButtonSubmit = computed(
  () => !supportUiEnable.value || convertNameComponent.value?.convertCompleted
);

const displayField = (field) => {
  if (!supportUiEnable.value) return true;

  const fieldBirthday = field.settings.some((fieldChild) =>
    birthdayVariables.includes(fieldVariable.value(fieldChild))
  );

  return fieldBirthday || convertNameComponent.value?.convertCompleted;
};
</script>

<style scoped lang="scss">
:deep(.order_sex_radio) {
  display: flex;
  gap: 23px;
  input {
    display: none !important;
  }
  label {
    display: table-cell;
    cursor: pointer;
    width: 100%;
    margin: 0;
    padding: 10px;
    background: #ddd;
    color: #767676;
    font-size: 16px;
    text-align: center;
    line-height: 1;
    transition: 0.2s;
    border-left: 1px solid #fff;
    border-radius: 5px;
  }
}

:deep(.order_sex_radio input[type="radio"]:checked + label) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: #fff;
}
:deep(label:hover) {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: white;
}

:deep(.form-control-lg) {
  padding: 0px !important;
  min-height: unset;
  width: 50%;
  display: flex;
}

.convert-btn {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  color: #fff;
}
</style>
