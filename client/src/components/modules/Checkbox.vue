<template>
  <FormLayout>
    <div v-for="field in moduleSetting">
      <FormValidator
        v-if="fieldType(field) == 'checkbox'"
        class="my-3"
        :name="fieldVariable(field)"
        :id="fieldVariable(field)"
      >
        <b-form-checkbox v-model="cart[fieldVariable(field)]">
          <span v-html="fieldLabel(field)"></span>
          <span v-if="fieldRequired(field)" class="text-danger">*</span>
        </b-form-checkbox>
      </FormValidator>
    </div>
    <b-button
      size="lg"
      variant="success"
      type="submit"
      name="submit"
      id="submit"
      :disabled="!canNextNode"
      @click="onClick()"
      class="confirm-btn"
    >
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";
import { computed, getCurrentInstance, watch } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  FormLayout,
  fieldRequired,
  setProps,
  moduleSetting,
  fieldVariable,
  fieldType,
  cart,
  fieldLabel,
  buttonText,
} = useCommon(vm);

setProps(props);

const { settingDesign } = useScenarioSetting();

const canNextNode = computed(() => {
  const filteredItems = moduleSetting.value.filter((item) => {
    if (item.type == "checkbox" && item.required) {
      const isInCart = cart.value[item.variable];
      return !isInCart || isInCart === false;
    }
  });
  return filteredItems.length === 0;
});
</script>

<style scoped>
.confirm-btn {
  width: 100%;
  background-image: linear-gradient(
    to right,
    v-bind("settingDesign.initiateBtnBgColor") 0%,
    v-bind("settingDesign.chatWindowBgColor") 51%,
    v-bind("settingDesign.initiateBtnBgColor") 100%
  );
  margin: 0 auto;
  padding: 15px 45px;
  text-align: center;
  text-transform: uppercase;
  transition: 0.5s;
  background-size: 200% auto;
  color: white;
  box-shadow: 0 0 20px #eee;
  border: none;
  border-radius: 60px;
  display: block;
}

.confirm-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.confirm-btn:disabled {
  opacity: 0.5;
}
.textbox {
  border: 1px solid v-bind("settingDesign.formInputBorderColor");
  padding: 5px;
  text-wrap: wrap;
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 200px;
}
</style>
