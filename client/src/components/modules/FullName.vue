<template>
  <FormLayout>
    <div v-for="field in moduleSetting" class="mt-2">
      <FormValidator
        v-if="displayField(field)"
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        class="mt-1"
      >
        <template v-if="isFullName(field)" #append-label>
          <div class="append-label">
            姓と名の間にスペースを入れてご入力ください。
          </div>
        </template>

        <component
          class="input-s"
          :id="fieldVariable(field)"
          :is="getFieldComponent(fieldType(field))"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>

      <div
        v-if="displayButtonConvert(field)"
        class="d-flex justify-content-end mt-2"
      >
        <b-button
          class="btn-sm convert-btn text-center"
          :disabled="converting"
          @click="startConverting"
        >
          <span
            v-if="converting"
            class="spinner-border spinner-border-sm"
            aria-hidden="true"
          />
          <font-awesome-icon v-else icon="fa-solid fa-pen" />
          <span class="ms-1">名前を交換</span>
        </b-button>
      </div>
    </div>

    <b-button
      v-if="displayButtonSubmit"
      class="form-btn-submit"
      @click="submitNodeFullName()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      />
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import { watch, getCurrentInstance, onMounted, ref, computed } from "vue";
import { useConvertNameStore } from "@/stores/convertName";
import { pick } from "lodash";
import useCommon from "@/composables/common";
import useScenarioSetting from "@/composables/chatSetting";

import * as AutoKana from "vanilla-autokana";

// ==========Icon===========
import { library } from "@fortawesome/fontawesome-svg-core";
import { faPen } from "@fortawesome/free-solid-svg-icons";
library.add(faPen);
// =========================

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const vm = getCurrentInstance();
const convertNameStore = useConvertNameStore();

const { settingDesign } = useScenarioSetting();
const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  supportUiEnable,
  getFieldComponent,
  handleFieldClick,
  disabledButton,
  currentVariables,
} = useCommon(vm);

setProps(props);

const autokana = ref(null);
const converting = ref(false);
const displayFurigana = ref(false);

async function submitNodeFullName() {
  isCartModified.value = false;
  await onClick();
}

const isCartModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isCartModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isCartModified.value = true;
    }
  },

  { immediate: true }
);

onMounted(() => {
  if (supportUiEnable.value) return;

  autokana.value = AutoKana.bind("#full_name", "#full_name_kana", {
    katakana: true,
  });
});

const displayButtonSubmit = computed(() => {
  if (!supportUiEnable.value) return true;

  return displayFurigana.value;
});

const isFullName = (field) => {
  return fieldVariable.value(field) == "full_name";
};

const displayField = (field) => {
  if (!supportUiEnable.value) return true;
  if (isFullName(field)) return true;

  return displayFurigana.value;
};

const displayButtonConvert = (field) => {
  if (!supportUiEnable.value) return false;
  if (!isFullName(field)) return false;

  return !displayFurigana.value;
};

const startConverting = async () => {
  if (!cart.value.full_name) {
    displayFurigana.value = true;
    return;
  }

  converting.value = true;

  try {
    const result = await convertNameStore.fetchFullNameKana(
      cart.value.full_name
    );

    if (result.getKanaName.lastKana && result.getKanaName.firstKana) {
      cart.value.full_name_kana =
        result.getKanaName.lastKana + "　" + result.getKanaName.firstKana;
    } else {
      cart.value.full_name_kana =
        result.getKanaName.lastKana || result.getKanaName.firstKana;
    }
  } catch (error) {
    console.error(error);
  } finally {
    converting.value = false;
    displayFurigana.value = true;
  }
};

watch(
  () => cart.value.full_name,
  () => {
    if (supportUiEnable.value) return;

    if (!cart.value.full_name) return (cart.value.full_name_kana = "");
    if (!autokana.value) return;

    cart.value.full_name_kana = autokana.value.getFurigana();
  }
);
</script>

<style lang="css" scoped>
.append-label {
  font-size: 14px;
  flex-grow: 8;
  text-align: right;
  color: blue;
  opacity: 0.4;
}

.convert-btn {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  color: #fff;
}
</style>
