<template>
  <FormLayout>
    <div v-for="field in moduleSetting" class="mt-3">
      <FormValidator
        :label="fieldLabel(field)"
        :required="fieldRequired(field)"
        :name="fieldVariable(field)"
        class="mt-3"
      >
        <component
          class="input-s"
          :is="getFieldComponent(fieldType(field))"
          :id="fieldVariable(field)"
          v-model="cart[fieldVariable(field)]"
          :placeholder="fieldPlaceholder(field)"
          :type="getTypeInput(fieldVariable(field))"
          @click="handleFieldClick(fieldVariable(field))"
        />
      </FormValidator>
    </div>

    <b-button
      class="form-btn-submit"
      @click="submitNodeEmailAndPassword()"
      :disabled="isDisable"
    >
      <span
        v-if="visibleLoading"
        class="spinner-border spinner-border-sm"
        role="status"
        aria-visible="true"
      ></span>
      {{ buttonText }}
    </b-button>
  </FormLayout>
</template>

<script setup>
import useCommon from "@/composables/common";
import { ref, watch, getCurrentInstance, computed } from "vue";
import { pick } from "lodash";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});
const vm = getCurrentInstance();

const {
  onClick,
  buttonText,
  FormLayout,
  cart,
  setProps,
  visibleLoading,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  isLastNode,
  disabledButton,
  getFieldComponent,
  handleFieldClick,
  currentVariables,
} = useCommon(vm);

setProps(props);

const isCartModified = ref(false);
const isButtonUpdate = computed(() => buttonText.value === "更新");

const isDisable = computed(() => {
  if (isButtonUpdate.value && isCartModified.value) {
    return false;
  }
  if (isButtonUpdate.value) {
    return true;
  }

  return disabledButton();
});

watch(
  () => currentVariables.value.map((variable) => cart.value[variable]),
  (value, oldvalue) => {
    if (value.length && oldvalue.length) {
      isCartModified.value = true;
    }
  },
  { immediate: true }
);

async function submitNodeEmailAndPassword() {
  isCartModified.value = false;

  await onClick();
}

function isPassword(variable) {
  return variable == "password";
}

function getTypeInput(variable) {
  return isPassword(variable) ? "password" : "text";
}
</script>
