<template>
  <div id="modal-mask">
    <div class="modal-box">
      <button class="close-button" @click="closeModal">X</button>
      <ul v-for="field in moduleSetting" class="d-flex flex-direction-column">
        <li>{{ fieldContent(field) }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import useCommon from "@/composables/common";
import { onMounted } from "vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const { setProps, moduleSetting, fieldContent, nodes, socketResult } =
  useCommon();

setProps(props);

const closeModal = () => {
  socketResult.value = {};
  nodes.value.pop();
  var s = document.getElementById("submitScript");
  s.remove();
};

onMounted(() => {
  if (socketResult.value.data && props.body.settings[0].variable) {
    props.body.settings[0].content = socketResult.value.data.message;
  }
});
</script>

<style scoped>
.modal-box {
  background-color: white;
  width: 60vw;
  height: 30vh;
  position: relative;
  padding: 20px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #000;
  background-color: transparent;
}

.close-button:hover {
  color: #999;
}

#modal-mask {
  position: fixed;
  z-index: 9999999999999999999999999999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: table;
  transition: opacity 0.3s ease;
}
</style>
