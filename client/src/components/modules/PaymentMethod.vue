<template>
  <FormLayout>
    <div :id="fieldVariable(moduleSetting[0])">
      <FormValidator
        :label="fieldLabel(moduleSetting[0])"
        :required="fieldRequired(moduleSetting[0])"
        :name="fieldVariable(moduleSetting[0])"
      >
        <b-form-radio-group
          class="radio-btn"
          v-model="cart[fieldVariable(moduleSetting[0])]"
          :id="fieldVariable(moduleSetting[0])"
          size="lg"
          stacked
        >
          <b-form-radio
            v-for="option in getOptions(moduleSetting[0])"
            :key="option"
            :value="option.text"
          >
            {{ option.text }}
          </b-form-radio>
        </b-form-radio-group>
      </FormValidator>

      <div
        v-if="displayFormInput"
        v-for="field in moduleSetting[1].settings"
        :key="field"
        class="mt-3"
      >
        <p
          v-if="field.settings && field.label.length > 0"
          style="font-weight: bold"
        >
          {{ fieldLabel(field) }}
        </p>

        <div
          :class="`${mappingFieldLayout(
            fieldLayout(field)
          )} justify-content-between`"
        >
          <div
            v-if="field.settings"
            v-for="fieldChild in field.settings"
            :style="{ width: getWidthChildField(field, fieldChild) }"
          >
            <FormValidator
              :label="fieldLabel(fieldChild)"
              :required="fieldRequired(fieldChild)"
              :name="fieldVariable(fieldChild)"
              class="mt-2"
            >
              <component
                class="input-s"
                :is="getFieldComponent(fieldType(fieldChild))"
                :options="getOptions(fieldChild)"
                v-model="creditCardInfo[fieldVariable(fieldChild)]"
                :id="fieldVariable(fieldChild)"
                :placeholder="fieldPlaceholder(fieldChild)"
                @click="handleFieldClick(fieldVariable(field))"
              />
            </FormValidator>
          </div>

          <FormValidator
            v-else
            :label="fieldLabel(field)"
            :required="fieldRequired(field)"
            :name="fieldVariable(field)"
            class="mt-3"
          >
            <component
              class="input-s"
              :is="getFieldComponent(fieldType(field))"
              :id="fieldVariable(field)"
              :type="getInputType(fieldVariable(field))"
              v-model="creditCardInfo[fieldVariable(field)]"
              :placeholder="fieldPlaceholder(field)"
              :options="getOptions(field)"
              @input="validateInput(field, $event)"
              @click="handleFieldClick(fieldVariable(field))"
            />
          </FormValidator>
        </div>
      </div>

      <b-button
        class="form-btn-submit"
        @click="submitNodeCardInfo"
        :disabled="disabledButton"
      >
        <span
          v-if="visibleLoading"
          class="spinner-border spinner-border-sm"
          role="status"
          aria-visible="true"
        />
        {{ buttonText }}
      </b-button>
    </div>
  </FormLayout>
</template>

<script setup>
import { ref, watch, onMounted, computed, getCurrentInstance } from "vue";

import useCommon from "@/composables/common";
import usePaymentMethod from "@/composables/paymentMethod";
import useScenarioSetting from "@/composables/chatSetting";

import { handleInputInBrowser } from "@/ultilities/helper.js";
import { Buffer } from "buffer";
import { HALF_WIDTH_INPUT_FIELDS } from "@/ultilities/constants";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  id: {
    type: String,
    default: "",
  },
});

const vm = getCurrentInstance();
const {
  onClick,
  buttonText,
  FormLayout,
  setProps,
  visibleLoading,
  cart,
  schema,
  moduleSetting,
  fieldLabel,
  fieldType,
  fieldVariable,
  fieldRequired,
  fieldPlaceholder,
  getFieldComponent,
  fieldLayout,
  getWidthChildField,
  mappingFieldLayout,
  getInputType,
  getAllVariables,
  handleFieldClick,
  setDefaultSchema,
} = useCommon(vm);

setProps(props);

const { creditCardInfo, deleteCreditCard, validateInput, getOptions } =
  usePaymentMethod();
const { settingDesign } = useScenarioSetting();

const displayFormInput = computed(() => {
  return moduleSetting.value?.[1]?.showOn?.includes(
    cart.value["payment_method"]
  );
});

const disabledButton = computed(() => {
  if (!cart.value["payment_method"]) return true;

  if (!displayFormInput.value) return false;

  for (let field of moduleSetting.value?.[1]?.settings) {
    if (field.settings) {
      for (let fieldChild of field.settings) {
        if (
          fieldRequired.value(fieldChild) &&
          !creditCardInfo.value[fieldVariable.value(fieldChild)]
        ) {
          return true;
        }
      }
    } else if (
      fieldRequired.value(field) &&
      !creditCardInfo.value[fieldVariable.value(field)]
    ) {
      return true;
    }
  }

  return false;
});

const submitNodeCardInfo = async () => {
  const arrKey = getAllVariables(moduleSetting.value);

  arrKey.forEach((item) => {
    if (HALF_WIDTH_INPUT_FIELDS.includes(item)) {
      handleInputInBrowser(item, creditCardInfo.value);
    }
  });

  window.parent.creditCard = creditCardInfo.value;
  cart.value.creditCard = Buffer.from(
    JSON.stringify(window.parent.creditCard),
    "utf-8"
  ).toString("base64");

  await onClick();
};

const addToSchema = (settings) => {
  settings.forEach((field) => {
    if (field.settings) return addToSchema(field.settings);

    setDefaultSchema(field);
  });
};

const clearSchema = (settings) => {
  settings.forEach((field) => {
    if (field.settings) return clearSchema(field.settings);

    delete schema.fields[field.variable];
  });
};

watch(
  () => cart.value["payment_method"],
  () => {
    if (displayFormInput.value) {
      addToSchema(moduleSetting.value?.[1]?.settings);
    } else {
      clearSchema(moduleSetting.value?.[1]?.settings);
    }
  },
  { immediate: true }
);

onMounted(() => {
  deleteCreditCard();
});
</script>

<style scoped lang="scss">
.radio-btn {
  margin: 0 auto;
  color: white;
}

.radio-btn:hover {
  background-position: right center;
  color: #fff;
  text-decoration: none;
}

.radio-btn:disabled {
  opacity: 0.5;
}

.radio-btn :deep(label) {
  background-color: white;
  color: v-bind("settingDesign.initiateBtnBgColor");
}

.radio-btn :deep(.btn-check:checked + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.optionActiveBgColor");

  &:hover {
    opacity: 0.85;
  }
}

.radio-btn :deep(.btn-check:disabled + .btn) {
  color: v-bind("settingDesign.optionActiveTxtColor");
  background-color: v-bind("settingDesign.optionActiveBgColor");
  opacity: 0.4;
}
</style>
