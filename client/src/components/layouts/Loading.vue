<template>
  <div class="loading">
    <div class="spinner-item">
      <span
        class="spinner-border text-secondary"
        role="status"
        aria-visible="true"
      ></span>
    </div>
  </div>
</template>

<script></script>

<style scoped>
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999999999999999999999;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.4);
}
.spinner-item {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 50%;
  left: 50%;
  z-index: 9999999999999999999999999999;
}
</style>
