<template>
  <div class="chat-bot-header">
    <ul class="text-box text-left">
      <li>
        <b>{{ settingGeneral.chatWindowTitle }}</b>
      </li>

      <li>{{ settingGeneral.chatWindowSubtitle }}</li>
    </ul>

    <!-- <div id="countdown">{{ formattedTime }}</div> -->

    <b-button
      v-if="settingGeneral.showButtonClose"
      class="close-btn"
      @click="
        settingGeneral.showConfirmationCloseModal ? showModal() : closeChatBot()
      "
    >
      <font-awesome-icon icon="fa-solid fa-x" class="close-icon" />
    </b-button>

    <b-modal
      ref="modalConfirmClose"
      title="BootstrapVue"
      centered
      hide-footer
      hide-header
    >
      <p class="my-4">{{ settingGeneral.confirmationText }}</p>

      <div class="d-flex justify-content-between">
        <b-button class="btn-ok" @click="onClose"> 閉じる </b-button>
        <b-button class="btn-cancel" @click="hideModal">
          チャットに戻る
        </b-button>
      </div>
    </b-modal>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { library } from "@fortawesome/fontawesome-svg-core";
import { faX } from "@fortawesome/free-solid-svg-icons";
library.add(faX);

import { useGlobalStore } from "@/stores/global";

import useScenarioSetting from "@/composables/chatSetting";

const modalConfirmClose = ref(null);
const globalStore = useGlobalStore();

function hideModal() {
  modalConfirmClose.value.hide();
}

function showModal() {
  modalConfirmClose.value.show();
}

function onClose() {
  hideModal();
  globalStore.closeChatBot();
}

// let initialCountdownTime = (13 * 60 * 60 + 3 * 60 + 10) * 1000 + 31;

// const formattedTime = ref(getFormattedTime(initialCountdownTime));

// function getFormattedTime(time) {
//   const hours = Math.floor(time / (1000 * 60 * 60));
//   const minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
//   const seconds = Math.floor((time % (1000 * 60)) / 1000);
//   return `あと${hours} 時間 ${minutes} 分 ${seconds} 秒`;
// }

// const timer = setInterval(() => {
//   initialCountdownTime -= 1000;

//   formattedTime.value = getFormattedTime(initialCountdownTime);

//   if (initialCountdownTime <= 0) {
//     clearInterval(timer);
//     formattedTime.value = "Countdown expired!";
//   }
// }, 1000);

const { settingGeneral } = useScenarioSetting();
</script>

<style scoped>
.close-btn {
  background-color: transparent;
  float: right;
  border: none;
}

.chat-bot-header {
  width: 100%;
  height: 60px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.close-icon {
  color: white;
}

.text-box {
  margin: 1px 1px 1px 3px;
  padding: 3px 8px;
}

.btn-ok {
  background-color: #f0f0f0;
  color: #333333;
  font-size: 15px;
}

.btn-cancel {
  color: #ffffff;
  font-size: 15px;
}

#countdown {
  font-size: 21px;
  font-weight: bold;
  color: white;
}
</style>
