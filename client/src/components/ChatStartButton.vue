<template>
  <div class="start-chat-button" @click="openChatBot">
    {{ settingGeneral.chatButtonTitle }}
    <font-awesome-icon icon="fa-solid fa-comment" class="close-icon" />
  </div>
</template>

<script setup>
import useScenarioSetting from "@/composables/chatSetting";

import { useGlobalStore } from "@/stores/global";

import { library } from "@fortawesome/fontawesome-svg-core";
import { faComment } from "@fortawesome/free-solid-svg-icons";
library.add(faComment);

const { settingGeneral, settingDesign } = useScenarioSetting();

const globalStore = useGlobalStore();

async function openChatBot() {
  await globalStore.openChatBot();
}
</script>

<style scoped>
.start-chat-button {
  display: block;
  position: fixed;
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  color: v-bind("settingDesign.initiateBtnTxtColor");

  bottom: 10px;
  right: 10px;
  padding: 10px 20px;
  width: 300px;
  height: 50px;

  border-radius: 15px;
  cursor: pointer;
  box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;

  text-align: center;
  z-index: 999999;
}

.start-chat-button:hover {
  opacity: 0.8;
}
</style>
