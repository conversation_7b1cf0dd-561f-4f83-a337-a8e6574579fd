<template>
  <div v-if="openSuggestion" class="input-suggestion">
    <div class="keyPadTitle text-gray-700 text-xs">{{ titleInput }}</div>
    <span class="keyInput p-1">
      {{ inputValue }}
      <i class="ping-box border-r-2 border-blue ml-1 animate-ping mt-1"></i>
    </span>
    <div class="d-flex w-full gap-1 p-0 mt-2">
      <div class="verticalBox">
        <div
          v-if="column1"
          class="pad assist blue"
          :class="(mapperColor, { mini: inputType !== 'tel' })"
          @click="addInput(column1)"
        >
          {{ column1 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number1)">
          {{ number1 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number4)">
          {{ number4 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number7)">
          {{ number7 }}
        </div>
      </div>
      <div class="verticalBox">
        <div
          v-if="column1"
          class="pad assist blue"
          :class="(mapperColor, { mini: inputType !== 'tel' })"
          @click="addInput(column2)"
        >
          {{ column2 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number2)">
          {{ number2 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number5)">
          {{ number5 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number8)">
          {{ number8 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number0)">
          {{ number0 }}
        </div>
      </div>
      <div class="verticalBox">
        <div
          v-if="column1"
          class="pad assist blue"
          :class="(mapperColor, { mini: inputType !== 'tel' })"
          @click="addInput(column3)"
        >
          {{ column3 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number3)">
          {{ number3 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number6)">
          {{ number6 }}
        </div>
        <div class="pad" :class="mapperColor" @click="addInput(number9)">
          {{ number9 }}
        </div>
      </div>
      <div class="verticalBox">
        <span v-if="!column4 && column3" class="pad assist empty"></span>
        <div
          v-if="column4"
          class="pad assist blue"
          :class="(mapperColor, { mini: inputType !== 'tel' })"
          @click="addInput('ー')"
        >
          {{ column4 }}
        </div>

        <div
          class="pad d-flex justify-content-center align-items-center"
          @click="clearLastNumber()"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 9.75L14.25 12m0 0l2.25 2.25M14.25 12l2.25-2.25M14.25 12L12 14.25m-2.58 4.92l-6.375-6.375a1.125 1.125 0 010-1.59L9.42 4.83c.211-.211.498-.33.796-.33H19.5a2.25 2.25 0 012.25 2.25v10.5a2.25 2.25 0 01-2.25 2.25h-9.284c-.298 0-.585-.119-.796-.33z"
            ></path>
          </svg>
        </div>
        <div class="pad mini" @click="clearData()">消去</div>
        <div class="pad double mini" @click="closeSuggestion()">完了</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, computed, defineEmits, onMounted } from "vue";
import { useGlobalStore } from "@/stores/global";
import { storeToRefs } from "pinia";

//// Define Props
const props = defineProps({
  openSuggestion: {
    type: Boolean,
    default: false,
  },
  inputType: {
    type: String,
    default: "address02", //zipcode tel address02
  },
  value: {
    type: String,
    default: "", //zipcode tel address02
  },
});

//// Define Emit
const emit = defineEmits(["input", "close"]);

//// Define Ref
const number0 = ref("0");
const number1 = ref("1");
const number2 = ref("2");
const number3 = ref("3");
const number4 = ref("4");
const number5 = ref("5");
const number6 = ref("6");
const number7 = ref("7");
const number8 = ref("8");
const number9 = ref("9");
const inputValue = ref("");
const tellValue = ["tel", "tel1", "tel2", "tel3"];
//// Define Computed
const titleInput = computed(() => {
  if (props.inputType === "zipcode") {
    return "〒郵便番号";
  } else if (tellValue.includes(props.inputType)) {
    return "電話番号";
  } else {
    return "丁目-番地-号";
  }
});
const mapperColor = computed(() => {
  if (props.inputType === "zipcode") {
    return "red";
  } else {
    return "blue";
  }
});

const column1 = computed(() => {
  if (props.inputType === "zipcode") {
    return null;
  } else if (tellValue.includes(props.inputType)) {
    return "090";
  } else {
    return "丁目";
  }
});
const column2 = computed(() => {
  if (props.inputType === "zipcode") {
    return null;
  } else if (tellValue.includes(props.inputType)) {
    return "080";
  } else {
    return "番";
  }
});
const column3 = computed(() => {
  if (props.inputType === "zipcode") {
    return null;
  } else if (tellValue.includes(props.inputType)) {
    return "070";
  } else {
    return "号";
  }
});
const column4 = computed(() => {
  if (props.inputType === "zipcode") {
    return null;
  } else if (tellValue.includes(props.inputType)) {
    return null;
  } else {
    return "ーハイフン"; // ー
  }
});

//// Define Store
const globalStore = useGlobalStore();
const { address02OpenSuggestion, telOpenSuggestion, zipcodeOpenSuggestion } =
  storeToRefs(globalStore);

//// FUNCTION
function addInput(number) {
  const data = inputValue.value + number.toString();

  if (
    props.inputType === "tel" &&
    (inputValue.value.length >= 13 || data.length > 13)
  ) {
    return;
  }
  if (
    ["tel1", "tel2", "tel3"].includes(props.inputType) &&
    (inputValue.value.length > 4 || data.length > 4)
  ) {
    return;
  }

  if (
    props.inputType === "zipcode" &&
    (inputValue.value.length >= 7 || data.length > 7)
  ) {
    return;
  }
  inputValue.value = data;
  emit("input", inputValue.value);
}

function clearLastNumber() {
  if (typeof inputValue.value === "string") {
    inputValue.value = inputValue.value.slice(0, -1);
    emit("input", inputValue.value);
  }
}

function clearData() {
  inputValue.value = "";
  emit("input", inputValue.value);
}

function closeSuggestion() {
  address02OpenSuggestion.value = false;
  zipcodeOpenSuggestion.value = false;
  telOpenSuggestion.value = false;
  emit("close");
}

watch(
  () => props.value,
  (value) => {
    inputValue.value = value;
  }
);

watch(
  () => props.inputType,
  (value) => {
    if (value === "address02") {
      number0.value = `０`;
      number1.value = `１`;
      number2.value = `２`;
      number3.value = `３`;
      number4.value = `４`;
      number5.value = `５`;
      number6.value = `６`;
      number7.value = `７`;
      number8.value = `８`;
      number9.value = `９`;
    } else {
      number0.value = `0`;
      number1.value = `1`;
      number2.value = `2`;
      number3.value = `3`;
      number4.value = `4`;
      number5.value = `5`;
      number6.value = `6`;
      number7.value = `7`;
      number8.value = `8`;
      number9.value = `9`;
    }
  }
);

onMounted(() => {
  if (props.inputType === "address02") {
    number0.value = `０`;
    number1.value = `１`;
    number2.value = `２`;
    number3.value = `３`;
    number4.value = `４`;
    number5.value = `５`;
    number6.value = `６`;
    number7.value = `７`;
    number8.value = `８`;
    number9.value = `９`;
  }
  inputValue.value = props.value;
});
</script>

<style lang="scss" scoped>
.input-suggestion {
  width: 100%;
  min-height: 320px;
  z-index: 9999999;
}
.keypadContainer {
  display: inline-block;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 4px;
  background-color: #fcfcfc;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.16) !important;
  transform: translateY(500px);
  visibility: hidden;
  transition: all 0.5s;
  text-align: center;
}
.keyPadTitle {
  font-size: 0.8rem;
  border-bottom: 1px solid #efefef;
  color: grey;
  padding: 0 0 4px;
  text-align: center;
}
.keyInput {
  color: v-bind("mapperColor");
  font-size: 1.125rem;
  line-height: 1.75rem;
  display: block;
  text-align: center;
  overflow-wrap: anywhere;
  --tw-text-opacity: 1;
}
.verticalBox {
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  display: flex;
  margin: 1px;
}
.double {
  height: 104px;
  line-height: 104px;
}
.pad {
  background-color: #cbd5e1;
  cursor: pointer;
  // width: 100%;
  text-align: center;
  border-radius: 5px;
  color: #0f172a;
  height: 50px;
  line-height: 50px;
  border: none;
  border-bottom: 1px solid #adadad;
  font-size: 1.4rem;
  font-weight: 500;
  font-family: "Helvetica Neue", Arial, "Hiragino Kaku Gothic ProN",
    "Hiragino Sans", Meiryo, sans-serif;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin: 2px;
}
.mini {
  font-size: 0.975rem !important;
}
.double {
  height: 104px;
  line-height: 104px;
}

.assist {
  height: 32px;
  line-height: 32px;
  border-radius: 50px;
  margin-bottom: 4px;
}

.empty {
  background-color: transparent;
  border-bottom: none;
}

.empty:hover {
  border-bottom: none;
  background-color: transparent !important;
}

.border-blue {
  border-color: rgba(37, 99, 235, 0.9);
}

.border-r-2 {
  border-right-width: 2px;
}

@keyframes ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(1, 1, 1, 1) infinite;
}

.ml-1 {
  margin-left: 0.1rem;
}

.ping-box {
  width: 1.5px;
  height: 15px;
  display: inline-block;
  background-color: rgba(37, 99, 235, 0.9);
  vertical-align: middle;
}

.text-gray-700 {
  color: #4a5568; /* Adjusted color to match */
}

.w-4 {
  width: 1.5rem; /* Adjusted width */
}

.h-4 {
  height: 1.5rem; /* Adjusted height */
}

.blue {
  background-color: #dbeafe;
  color: #1e40af;
  border-bottom: 1px solid #c4c1c1;
}
.blue:hover {
  background-color: #93c5fd;
}

.red {
  background-color: #fef2f2;
  color: #b91c1c;
  border-bottom: 1px solid #c4c1c1;
}

.red:hover {
  background-color: #fecaca;
}

.pad:hover {
  background-color: #94a3b8;
}
</style>
