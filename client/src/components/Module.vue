<template>
  <component
    :is="checkModule()"
    :label="label"
    :body="body"
    :nextNodeUid="nextNodeUid"
    :nodeUid="nodeUid"
    :id="id"
  />
</template>

<script setup>
import { NODE_TYPE, MODULE_TYPE } from "@/ultilities/constants";

import Message from "Components/modules/Message.vue";
import Quantity from "Components/modules/Quantity.vue";
import Variant from "Components/modules/Variant.vue";
import Name from "Components/modules/Name.vue";
import FullName from "Components/modules/FullName.vue";
import EmailAndPassword from "Components/modules/EmailAndPassword.vue";
import Address from "Components/modules/Address.vue";
import SexAndBirthday from "Components/modules/SexAndBirthday.vue";
import NameSexBirthday from "Components/modules/NameSexBirthday.vue";
import NameBirthdayTelEmail from "Components/modules/NameBirthdayTelEmail.vue";
import CreditCard from "Components/modules/CreditCard.vue";
import PaymentMethod from "Components/modules/PaymentMethod.vue";
import Button from "Components/modules/Button.vue";
import ButtonV2 from "Components/modules/ButtonV2.vue";
import Text from "Components/modules/Text.vue";
import Select from "Components/modules/Select.vue";
import Password from "Components/modules/Password.vue";
import Modal from "Components/modules/Modal.vue";
import RadioButton from "Components/modules/RadioButton.vue";
import RadioButtonReselectable from "Components/modules/RadioButtonReselectable.vue";
import RadioButtonGrid from "Components/modules/RadioButtonGrid.vue";
import RadioButtonMultiSelect from "Components/modules/RadioButtonMultiSelect.vue";
import TelEmailPassword from "Components/modules/TelEmailPassword.vue";
import ScheduledDelivery from "Components/modules/ScheduledDelivery.vue";
import Checkbox from "Components/modules/Checkbox.vue";

const props = defineProps({
  label: {
    type: String,
    default: "",
  },
  body: {
    type: Object,
    default: {},
  },
  nextNodeUid: {
    type: Array,
    default: [],
  },
  nodeUid: {
    type: String,
    default: "",
  },
  nodeType: {
    type: String,
    default: "",
  },
  index: {
    type: Number,
    default: 0,
  },
  id: {
    type: String,
    default: "",
  },
});

const checkModule = () => {
  if (props.nodeType == NODE_TYPE.MESSAGE) {
    return Message;
  }

  if (props.nodeType == NODE_TYPE.MODAL) {
    return Modal;
  }

  if (props.nodeType == NODE_TYPE.BUTTON) {
    return Button;
  }
  if (props.nodeType == NODE_TYPE.BUTTON_V2) {
    return ButtonV2;
  }

  if (props.nodeType == NODE_TYPE.INPUT) {
    switch (props.body.type) {
      case MODULE_TYPE.QUANTITY:
        return Quantity;
      case MODULE_TYPE.VARIANT:
        return Variant;
      case MODULE_TYPE.NAME:
        return Name;
      case MODULE_TYPE.FULL_NAME:
        return FullName;
      case MODULE_TYPE.PASSWORD:
        return Password;
      case MODULE_TYPE.TEXT:
        return Text;
      case MODULE_TYPE.SELECT:
        return Select;
      case MODULE_TYPE.EMAIL_AND_PASSWORD:
        return EmailAndPassword;
      case MODULE_TYPE.ADDRESS:
        return Address;
      case MODULE_TYPE.SEX_AND_BIRTHDAY:
        return SexAndBirthday;
      case MODULE_TYPE.CREDIT_CARD:
        return CreditCard;
      case MODULE_TYPE.PAYMENT_METHOD:
        return PaymentMethod;
      case MODULE_TYPE.RADIO_BUTTON:
        return RadioButton;
      case MODULE_TYPE.RADIO_BUTTON_RESELECTABLE:
        return RadioButtonReselectable;
      case MODULE_TYPE.TEL_EMAIL_PASSWORD:
        return TelEmailPassword;
      case MODULE_TYPE.SCHEDULED_DELIVERY:
        return ScheduledDelivery;
      case MODULE_TYPE.CHECKBOX:
        return Checkbox;
      case MODULE_TYPE.NAME_SEX_BIRTHDAY:
        return NameSexBirthday;
      case MODULE_TYPE.RADIO_BUTTON_GRID:
        return RadioButtonGrid;
      case MODULE_TYPE.RADIO_BUTTONS_MULTI_SELECT:
        return RadioButtonMultiSelect;
      case MODULE_TYPE.NAME_BIRTHDAY_TEL_EMAIL:
        return NameBirthdayTelEmail;
    }
  }
};
</script>
