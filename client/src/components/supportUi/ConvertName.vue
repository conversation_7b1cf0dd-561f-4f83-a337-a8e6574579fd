<template>
  <div v-if="displayConvertName">
    <p style="font-weight: bold">お名前</p>

    <FormValidator class="mt-1">
      <b-form-input
        class="input-s"
        id="fullName"
        v-model="cart.fullName"
        @click="handleFieldClick('fullName')"
      />
    </FormValidator>

    <div class="note-name mt-2">
      <div class="d-flex">
        <div v-if="converting" class="d-flex align-items-center">
          <span
            style="color: #d7eecf"
            class="spinner-border spinner-border-sm"
            role="status"
            aria-visible="true"
          ></span>
          <span class="mx-2" style="color: #97e51e">変換中</span>
        </div>
        <div v-if="isNameConverted">
          {{ cart.sei }}
          {{ cart.mei }}
          (
          {{ cart.seifuri }}
          {{ cart.meifuri }})
        </div>
      </div>

      <div class="d-flex justify-content-end mt-2">
        <b-button
          class="btn-sm convert-btn text-center"
          :disabled="converting"
          @click="startConverting"
        >
          <span
            v-if="converting"
            class="spinner-border spinner-border-sm"
            aria-hidden="true"
          />
          <font-awesome-icon v-else icon="fa-solid fa-pen" />
          <span class="ms-1">名前を交換</span>
        </b-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, getCurrentInstance } from "vue";

import { storeToRefs } from "pinia";

import useScenarioSetting from "@/composables/chatSetting";
import { useConvertNameStore } from "@/stores/convertName";

import useCommon from "@/composables/common";
import { handleScrollIntoView } from "@/ultilities/helper.js";

// ==========Icon===========
import { library } from "@fortawesome/fontawesome-svg-core";
import { faPen } from "@fortawesome/free-solid-svg-icons";
library.add(faPen);
// =========================

const vm = getCurrentInstance();
const { cart, handleFieldClick, supportUiEnable } = useCommon(vm);

const scenarioSettingStore = useScenarioSetting();
const { settingDesign } = storeToRefs(scenarioSettingStore);

const convertNameStore = useConvertNameStore();

const converting = ref(false);
const convertCompleted = ref(false);

const displayConvertName = computed(
  () => supportUiEnable.value && !convertCompleted.value
);

const isNameConverted = computed(() => {
  return (
    cart.value.sei &&
    cart.value.mei &&
    cart.value.fullName === `${cart.value.sei}${cart.value.mei}` &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
});

const startConverting = async () => {
  if (!cart.value.fullName) {
    clearNameFields();
    convertCompleted.value = true;
    scrollNode();
    return;
  }

  if (isFullNameConverted()) {
    convertCompleted.value = true;
    scrollNode();
    return;
  }

  converting.value = true;

  setTimeout(() => {
    try {
      convertNameStore.getKana(cart.value.fullName);
    } catch (error) {
      console.error(error);
    } finally {
      converting.value = false;
      convertCompleted.value = true;
    }
  }, 200);
  scrollNode();
};

function scrollNode() {
  setTimeout(() => {
    const el = document.getElementById("meifuri");
    handleScrollIntoView(el);
  }, 500);
}

function clearNameFields() {
  cart.value.sei = null;
  cart.value.mei = null;
  cart.value.seifuri = null;
  cart.value.meifuri = null;
}

function isFullNameConverted() {
  return (
    cart.value.fullName &&
    cart.value.fullName.length &&
    cart.value.sei &&
    cart.value.mei &&
    cart.value.seifuri &&
    cart.value.meifuri
  );
}

defineExpose({
  convertCompleted,
});
</script>

<style scoped lang="scss">
.convert-btn {
  background-color: v-bind("settingDesign.initiateBtnBgColor");
  border-radius: 10px 10px;
  color: #fff;
}
</style>
