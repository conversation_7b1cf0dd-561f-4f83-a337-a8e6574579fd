<template>
  <Index v-show="onShowChatBot" />
  <ChatStartButton v-show="showChatStartButton" />
</template>

<script setup>
import { computed, onMounted, watch } from "vue";
import Index from "@/layouts/index.vue";
import ChatStartButton from "@/components/ChatStartButton.vue";
import useScenarioSetting from "@/composables/chatSetting";
import { UnicornCartChatbot } from "@/composables/unicorn_cart_chatbot";
import { useGlobalStore } from "@/stores/global";
import { useScenarioStore } from "@/stores/scenario";
import { storeToRefs } from "pinia";
import webSocket from "@/ultilities/webSocket";

// Define Store
const globalStore = useGlobalStore();
const { showChatBot, chatBotStartImmediately } = storeToRefs(globalStore);
const { settingGeneral, initSettingBot } = useScenarioSetting();

const scenarioStore = useScenarioStore();
const { scenario } = storeToRefs(scenarioStore);

// Define Computed
const showChatStartButton = computed(() => {
  return (
    !showChatBot.value &&
    settingGeneral.value.showChatStartButton &&
    !chatBotStartImmediately.value
  );
});

const onShowChatBot = computed(() => {
  return chatBotStartImmediately.value || showChatBot.value;
});

//  Watch
watch(
  () => onShowChatBot.value,
  async (value) => {
    if (value) {
      await scenarioStore.fetchScenarioDetail();
      await webSocket().tryConnectWebSocket();
      await analyAccessPutlog();
    }
  }
);

//  Function
function analyAccessPutlog() {
  console.log("putLog", `${scenario.value.shopId}_chatbot`);
  Analy.Access.putLog(`${scenario.value.shopId}_chatbot`);
}

//  Init
initSettingBot();
window.parent.UnicornCartChatbot = new UnicornCartChatbot();

//  onMounted
onMounted(async () => {
  if (onShowChatBot.value) {
    await scenarioStore.fetchScenarioDetail();
    await webSocket().tryConnectWebSocket();
    await analyAccessPutlog();
  }
});
</script>
