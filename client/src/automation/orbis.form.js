(function () {
  const initializeForm = function () {
    const currentPath = window.location.pathname;
    const cookieStore = parseCookies();
    const sessionId = cookieStore["ug_ssId"];
    switch (currentPath) {
      case "/order/personal_input/":
        if (sessionId) {
          displayLoadingOverlay();

          const fetchOptions = {
            method: "GET",
            redirect: "follow",
          };

          fetch(
            `https://chatbot.unicorncart.jp/api/v1/form_prefills/${sessionId}`,
            fetchOptions
          )
            .then((response) => response.text())
            .then((result) => {
              const checkErr = () => {
                const err = document.querySelector(
                  '.enclosure--error:not([style*="display: none"])'
                )?.innerText;
                if (err) {
                  hideLoadingOverlay();
                } else {
                  populateForm(JSON.parse(result));
                }
              };
              setTimeout(checkErr, 0);
            })
            .catch((error) => console.error("Fetch error:", error));
        } else {
          console.error("Session ID not found in cookies");
        }
        break;

      case "/order/payment/":
        if (sessionId) {
          displayLoadingOverlay();
          const fetchOptions = {
            method: "DELETE",
            redirect: "follow",
          };

          fetch(
            `https://chatbot.unicorncart.jp/api/v1/form_prefills/${sessionId}`,
            fetchOptions
          )
            .then(() => {
              console.log("Session deleted successfully");
              document.cookie = `ug_ssId=; domain=.orbis.co.jp; path=/`;
              hideLoadingOverlay();
            })
            .catch((error) => {
              hideLoadingOverlay();
              console.error("Fetch error:", error);
            });
        }

        break;

      default:
        console.log("No action required for this path");
    }
  };

  setTimeout(initializeForm, 0);

  const parseCookies = () => {
    return document.cookie.split(";").reduce((cookies, cookie) => {
      const [key, value] = cookie.split("=");
      cookies[key.trim()] = value;
      return cookies;
    }, {});
  };

  const setInputValue = (selector, value) => {
    return new Promise((resolve, reject) => {
      const inputEvent = new InputEvent("input", {
        bubbles: true,
        cancelable: false,
      });
      const changeEvent = new InputEvent("change", {
        bubbles: true,
        cancelable: false,
      });
      const inputElement = document.querySelector(selector);

      if (inputElement) {
        inputElement.value = value;
        inputElement.dispatchEvent(inputEvent);
        inputElement.dispatchEvent(changeEvent);
        resolve(`Input '${selector}' filled with value '${value}'`);
      } else {
        reject(new Error(`Input element '${selector}' not found`));
      }
    });
  };

  const selectOption = (
    selector,
    value,
    options = { useValue: true, useText: false }
  ) => {
    return new Promise((resolve, reject) => {
      let targetOption;

      if (options.useText) {
        targetOption = Array.from(
          document.querySelectorAll(`${selector} option`)
        ).find((opt) => opt.innerText.trim() === value);
      } else if (options.useValue) {
        targetOption = document.querySelector(
          `${selector} option[value='${value}']`
        );
      } else {
        targetOption = document.querySelector(
          `${selector} option[label='${value}']`
        );
      }

      const selectElement = document.querySelector(selector);

      if (targetOption && selectElement) {
        targetOption.selected = true;
        const changeEvent = new Event("change", {
          bubbles: true,
          composed: true,
        });
        selectElement.dispatchEvent(changeEvent);
        resolve(
          `Select '${selector}' set to ${
            options.useText ? "text" : "value"
          } '${value}'`
        );
      } else {
        reject(
          new Error(
            `Select '${selector}' with ${
              options.useText ? "text" : "value"
            } '${value}' not found`
          )
        );
      }
    });
  };

  const selectRadio = (selector, value) => {
    return new Promise((resolve, reject) => {
      const radioElement = document.querySelector(
        `${selector}[value='${value}']`
      );

      if (radioElement) {
        radioElement.checked = true;
        const changeEvent = new Event("change", {
          bubbles: true,
          composed: true,
        });
        radioElement.dispatchEvent(changeEvent);
        resolve(`Radio '${selector}' with value '${value}' selected`);
      } else {
        reject(
          new Error(`Radio '${selector}' with value '${value}' not found`)
        );
      }
    });
  };

  const triggerMouseEvents = (element) => {
    const mouseEvents = ["mousedown", "mouseup", "click"];
    mouseEvents.forEach((eventType) => {
      const event = new MouseEvent(eventType, {
        view: window,
        bubbles: true,
        cancelable: true,
      });
      element.dispatchEvent(event);
    });
  };

  const waitForElements = (selectors, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const interval = 100;
      let elapsed = 0;

      const checkElements = () => {
        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            resolve(element);
            return;
          }
        }
        elapsed += interval;
        if (elapsed >= timeout) {
          reject(
            new Error(
              `Elements '${selectors.join(", ")}' not found within ${timeout}ms`
            )
          );
        } else {
          setTimeout(checkElements, interval);
        }
      };

      checkElements();
    });
  };

  const toggleCheckboxState = (selector, isChecked) => {
    return new Promise((resolve, reject) => {
      const checkbox = document.querySelector(selector);

      if (checkbox) {
        checkbox.checked = isChecked;
        const changeEvent = new Event("change", {
          bubbles: true,
          composed: true,
        });
        checkbox.dispatchEvent(changeEvent);
        resolve(`Checkbox '${selector}' set to ${isChecked}`);
      } else {
        reject(new Error(`Checkbox '${selector}' not found`));
      }
    });
  };

  async function populateForm(responseData) {
    try {
      // console.log("Populating form with data:", responseData);
      const formData = responseData.data;

      console.log("form data", formData);
      await new Promise((resolve) => setTimeout(resolve, 2000));

      await setInputValue('[name="name1"]', formData.sei);
      await new Promise((resolve) => setTimeout(resolve, 500));
      await setInputValue('[name="name2"]', formData.mei);
      await new Promise((resolve) => setTimeout(resolve, 500));

      const changeEvent = new InputEvent("change", {
        bubbles: true,
        cancelable: false,
      });
      await setInputValue('[name="kana1"]', formData.seifuri);
      document.querySelector('[name="name1"]').dispatchEvent(changeEvent);
      await new Promise((resolve) => setTimeout(resolve, 500));
      await setInputValue('[name="kana2"]', formData.meifuri);
      document.querySelector('[name="name2"]').dispatchEvent(changeEvent);
      await new Promise((resolve) => setTimeout(resolve, 500));

      switch (formData.sex) {
        case "女性":
          await selectRadio('[name="gender"]', "2");
          break;
        case "男性":
          await selectRadio('[name="gender"]', "1");
          break;
        default:
          await selectRadio('[name="gender"]', "9");
          break;
      }

      await setInputValue('[name="zip1"]', formData.zipcode);

      document.querySelector(".formUnitInline__searchBtn").click();
      await new Promise((resolve) => setTimeout(resolve, 1500));

      document.querySelector(".modalAddress__submitBtn").click();
      await new Promise((resolve) => setTimeout(resolve, 500));

      await setInputValue('[name="address2"]', formData.address02);
      // await setInputValue('[name="address3"]', formData.address03);

      await setInputValue('[name="tel1"]', formData.tel1);
      await setInputValue('[name="tel2"]', formData.tel2);
      await setInputValue('[name="tel3"]', formData.tel3);

      await setInputValue('[name="email"]', formData.mail);
      await setInputValue('[name="password"]', formData.password);
      if (document.querySelector('[name="password_confirm"]')) {
        await setInputValue('[name="password_confirm"]', formData.password);
      }

      if (
        formData.year &&
        formData.year !== "Undefined" &&
        formData.month &&
        formData.month !== "Undefined" &&
        formData.day &&
        formData.day !== "Undefined"
      ) {
        await selectOption('[name="birth_year"]', formData.year);
        await selectOption('[name="birth_month"]', formData.month);
        await selectOption('[name="birth_day"]', formData.day);
      }

      await toggleCheckboxState('[name="delivery_format"]', true);

      await toggleCheckboxState('[name="termCheck"]', true);

      // window.scrollTo(0, document.body.scrollHeight);
      // await new Promise((resolve) => setTimeout(resolve, 1000));
      const submitButton = document.querySelector(".js-formValidationSubmit");
      const hasSubmitted = localStorage.getItem("hasSubmitted");
      if (hasSubmitted !== "true") {
        console.log("submitButton");
        localStorage.setItem("hasSubmitted", true);
        await submitButton.click();
        setTimeout(hideLoadingOverlay, 5000);
      } else {
        setTimeout(hideLoadingOverlay, 2000);
      }
    } catch (error) {
      hideLoadingOverlay();
      console.error("Populate form error:", error);
      return;
    }
  }

  function displayLoadingOverlay() {
    const overlay = document.createElement("div");
    overlay.id = "loadingOverlay";
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.width = "100%";
    overlay.style.height = "100%";
    overlay.style.backgroundColor = "rgba(255, 255, 255, 0.9)";
    overlay.style.display = "flex";
    overlay.style.justifyContent = "center";
    overlay.style.alignItems = "center";
    overlay.style.flexDirection = "column";
    overlay.style.color = "#333";
    overlay.style.zIndex = "9999999999";
    overlay.style.backdropFilter = "blur(10px)";
    overlay.style.webkitBackdropFilter = "blur(10px)";

    const spinner = document.createElement("div");
    spinner.style.border = "16px solid #f3f3f3";
    spinner.style.borderTop = "16px solid rgb(100, 161, 108)";
    spinner.style.borderRadius = "50%";
    spinner.style.width = "120px";
    spinner.style.height = "120px";
    spinner.style.animation = "spin 2s linear infinite";

    overlay.appendChild(spinner);

    const loadingMessage = document.createElement("p");
    loadingMessage.textContent =
      "システムが処理を行っています。しばらくお待ちください。";
    overlay.appendChild(loadingMessage);

    document.body.appendChild(overlay);

    const styleSheet = document.createElement("style");
    styleSheet.innerHTML = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
      `;
    document.head.appendChild(styleSheet);
  }

  function hideLoadingOverlay() {
    const overlay = document.getElementById("loadingOverlay");
    if (overlay) {
      overlay.style.display = "none";
    }
  }
})();
