import { defineStore } from "pinia";
import { ref, watch } from "vue";
import { useLocalStorage } from "@vueuse/core";

import useScenarioSetting from "@/composables/chatSetting";
import { get } from "lodash";
import { useScenarioSettingStore } from "@/stores/scenarioSetting";
import { storeToRefs } from "pinia";
import { ANALY_ACTION } from "@/ultilities/constants";

// import { getActivePinia } from "pinia";

// import GlobalsRepository from "@/apis/repositories/globalsRepository";
// import axios from "@/apis/axios";

export const useGlobalStore = defineStore("global", () => {
  const theme = ref(useLocalStorage("theme", "light"));

  // Define Store
  const scenarioSettingStore = useScenarioSettingStore();
  const { scenarioSetting } = storeToRefs(scenarioSettingStore);

  // Define Ref
  const chatBotStartImmediately = ref(false);

  const validationErrors = ref({});
  const reValidationErrors = ref([]);
  const errorMessage = ref("");
  const requests = ref([]);
  const showChatBot = ref(window.parent == window);
  const loading = ref(false);
  const socketResult = ref({});
  const address02OpenSuggestion = ref(false);
  const zipcodeOpenSuggestion = ref(false);
  const telOpenSuggestion = ref(false);

  const { initChatStartButton, initChatBot } = useScenarioSetting();

  //  WATCH
  watch(
    () => scenarioSetting.value.scenarioGeneralSetting,
    (value) => {
      chatBotStartImmediately.value = value.startChatbotImmediately;
    }
  );

  function isIphone() {
    const ua = navigator.userAgent.toLowerCase();
    return /iphone|ipod/.test(ua);
  }

  function openChatBot() {
    showChatBot.value = true;

    initChatBot();
  }
  // Function

  function closeChatBot() {
    // const pinia = getActivePinia();

    // const store = pinia._s.get("scenario");
    // store.clearScenarioData();

    showChatBot.value = false;
    chatBotStartImmediately.value = false;

    if (isIphone()) {
      window.parent.document.body.style.overflow = "unset";
      window.parent.document.documentElement.style.overflow = "unset";
      window.parent.document.body.style.maxHeight = "none";
    }
    initChatStartButton();
  }

  function getErrors(name) {
    return get(validationErrors.value, name, []);
  }

  function hasErrorOn(field) {
    const fieldError = validationErrors.value[field];

    if (fieldError) {
      return fieldError.length > 0;
    } else {
      return false;
    }
  }

  function haveErrorOnFields(fields) {
    for (const field of fields) {
      if (hasErrorOn(field)) {
        return true;
      }
    }

    return false;
  }

  function setErrorMessage(message) {
    errorMessage.value = message;
  }

  function setValidationErrors(key, errors) {
    validationErrors.value[key] = errors;
  }

  function setReValidationErrors(key) {
    reValidationErrors.value.push(key);
  }

  function clearReValidationErrors(key) {
    reValidationErrors.value = reValidationErrors.value.filter(
      (item) => item !== key
    );
  }

  function resetValidationErrors(keys = []) {
    if (!keys?.length) {
      validationErrors.value = null;
    }
    keys.forEach((key) => {
      delete validationErrors.value[key];
    });
  }

  function addRequest(id) {
    requests.value.push(id);
  }

  function removeRequest(id) {
    setTimeout(
      () => (requests.value = requests.value.filter((item) => item !== id)),
      300
    );
  }

  return {
    theme,
    requests,
    showChatBot,
    loading,
    socketResult,
    chatBotStartImmediately,

    address02OpenSuggestion,
    telOpenSuggestion,
    zipcodeOpenSuggestion,

    // Errors Msgs
    errorMessage,
    validationErrors,
    reValidationErrors,

    // methods
    setReValidationErrors,
    clearReValidationErrors,
    setValidationErrors,
    resetValidationErrors,
    getErrors,
    setErrorMessage,
    haveErrorOnFields,
    addRequest,
    removeRequest,
    openChatBot,
    closeChatBot,
  };
});
