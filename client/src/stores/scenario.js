import { defineStore } from "pinia";
import { ref, watch } from "vue";
import ScenarioRepository from "@/apis/repositories/scenarioRepository";
import { storeToRefs } from "pinia";
import { useCartStore } from "@/stores/cart";
import { useGlobalStore } from "@/stores/global";
import {
  NODE_TYPE_HIDDEN,
  ANALY_ACTION,
  NODE_TYPE,
} from "@/ultilities/constants";
import webSocket from "@/ultilities/webSocket";
import { v4 } from "uuid";
import { cloneDeep } from "lodash";

import {
  getLocalStorateSessionData,
  setLocalStoragetSessionData,
  getLastUsedScenarioByUrl,
  setLastUsedScenarioByUrl,
  clearAllExpiredLocalStorageKeys,
  handleScrollIntoView,
} from "@/ultilities/helper.js";

export const useScenarioStore = defineStore("scenario", () => {
  //// Define Ref
  const scenario = ref({});
  const nodes = ref([]);
  const container = ref(null);

  //// Define Store
  const cartStore = useCartStore();
  const { cart } = storeToRefs(cartStore);

  const globalStore = useGlobalStore();
  const { socketResult } = storeToRefs(globalStore);

  // TODO initialize payload
  const originUrl =
    process.env.NODE_ENV == "development"
      ? process.env.URL_CRAWLER
      : document.location.href;

  const dataScenario =
    window.parent.document
      .querySelector("script[data-account]")
      ?.getAttribute("data-scenario") ||
    process.env.DATA_SCENARIO ||
    null;
  const dataAccount =
    window.parent.document
      .querySelector("script[data-account]")
      ?.getAttribute("data-account") ||
    process.env.DATA_ACCOUNT ||
    null;

  const lastUsedScenarioId = ref(null);
  const sessionDataId = ref(null);
  const ssid = ref(null);

  async function removeAllExpiredKeys() {
    clearAllExpiredLocalStorageKeys();
  }

  // try to fetch from local storage base on dataScenario
  //// FUNCTION
  async function fetchLastUsedScenario() {
    lastUsedScenarioId.value = await getLastUsedScenarioByUrl(originUrl);

    sessionDataId.value = getLocalStorateSessionData(lastUsedScenarioId.value);
  }

  function clearScenarioData() {
    scenario.value = {};
    nodes.value = [];
  }

  async function fetchScenarioDetail() {
    const result = await ScenarioRepository.fetchScenario(
      originUrl,
      dataAccount,
      sessionDataId.value || ssid.value,
      dataScenario
    );

    scenario.value = result.fetchScenario;

    if (JSON.parse(result.fetchScenario.sdata)) {
      const ssidData = JSON.parse(result.fetchScenario.sdata);
      if (checkObjectKeys(ssidData)) {
        cart.value = ssidData.cart;
        nodes.value = ssidData.nodes;
      }
    }
    ssid.value = result.fetchScenario.ssid;
    await setLastUsedScenarioByUrl(originUrl, scenario.value.id); // lastused.urlEncrypted
    setLocalStoragetSessionData(scenario.value.id, ssid.value);
  }

  function scrollToBottom() {
    const lastNode = nodes.value[nodes.value.length - 1];

    if (!getLastVariable(lastNode.body.settings)) {
      if (lastNode.nodeType === NODE_TYPE.MESSAGE) {
        if (
          nodes.value.length === 1 &&
          lastNode.body.settings[0].type === "images"
        ) {
          return;
        }
        scrollLastMessage(1500);
      } else {
        if (container.value) {
          setTimeout(() => {
            container.value.scrollTop = container.value.scrollHeight;
          }, 100);
        }
      }
    } else {
      if (lastNode.body?.repeatable) {
        if (lastNode.nodeType === NODE_TYPE.MESSAGE) {
          scrollLastMessage(1500);
        } else {
          if (container.value) {
            setTimeout(() => {
              container.value.scrollTop = container.value.scrollHeight;
            }, 100);
          }
        }
      } else {
        setTimeout(() => {
          scrollToNode(lastNode);
        }, 100);
      }
    }
  }

  function nextNode(nodeUid, id) {
    const node = cloneDeep(scenario.value.nodes.find((n) => n.uid === nodeUid));

    if (
      !node ||
      (!node?.body?.repeatable && nodes.value.some((n) => n.uid === node.uid))
    ) {
      return;
    }

    node.id = v4();

    const nodeType = node.nodeType;
    const firstNodeInput = nodes.value.filter((n) =>
      [NODE_TYPE.BUTTON, NODE_TYPE.INPUT].includes(n.nodeType)
    );

    analyPutLogImpression(firstNodeInput, nodeUid, nodeType);

    if (!Object.values(NODE_TYPE_HIDDEN).includes(nodeType)) {
      console.log("display", nodeUid);
      Analy.P.Input.putChatformLog(
        `${scenario.value.shopId}_chatbot`,
        scenario.value.id,
        nodeUid,
        ANALY_ACTION.DISPLAY
      );
      
      if (nodeType === NODE_TYPE.BUTTON) {
        addNodeWithoutContent(node);
      } else {
        nodes.value.push(node);
      }

      const nodeLast = nodes.value[nodes.value.length - 1];
      //  TODO Refactor Code
      if (checkScroll()) {
        if (!getLastVariable(nodeLast.body.settings)) {
          if (nodeType === NODE_TYPE.MESSAGE) {
            scrollLastMessage(100);
            scrollLastMessage(1500);
          } else {
            if (container.value) {
              setTimeout(() => {
                container.value.scrollTop = container.value.scrollHeight;
              }, 100);
            }
          }
        } else {
          if (nodeLast.body?.repeatable) {
            if (nodeType === NODE_TYPE.MESSAGE) {
              scrollLastMessage(1500);
            } else {
              if (container.value) {
                setTimeout(() => {
                  container.value.scrollTop = container.value.scrollHeight;
                }, 100);
              }
            }
          } else {
            setTimeout(() => {
              scrollToNode(nodeLast);
            }, 100);
          }
        }
      }
      return;
    }

    if (nodes.value.at(-1)?.id == id) {
      const nodeType = node.nodeType;
      if (!Object.values(NODE_TYPE_HIDDEN).includes(nodeType))
        return nodes.value.push(node);
      switch (nodeType) {
        case NODE_TYPE_HIDDEN.CONDITION:
          return handleNextNodeCondition(node);
        case NODE_TYPE_HIDDEN.SET_VALUE:
          return handleNextNodeSetValue(node);
        case NODE_TYPE_HIDDEN.HTML_TASK:
        case NODE_TYPE_HIDDEN.HEADLESS_TASK:
          return handleNextNodeTrigger(node);
      }
    }
  }

  function analyPutLogImpression(firstNodeInput, nodeUid, nodeType) {
    if (
      !firstNodeInput.length &&
      [NODE_TYPE.BUTTON, NODE_TYPE.INPUT].includes(nodeType)
    ) {
      console.log("impression", nodeUid);
      Analy.P.Input.putChatformLog(
        `${scenario.value.shopId}_chatbot`,
        scenario.value.id,
        nodeUid,
        ANALY_ACTION.IMPRESSION
      );
    }
  }

  function analyPutLogEntry(firstNodeInput, nodeUid, nodeType) {
    const nodeIsInput = [NODE_TYPE.BUTTON, NODE_TYPE.INPUT].includes(nodeType);
    //  putChatformLog  ENTRY first node type button input
    if (firstNodeInput.length === 1 && nodeIsInput) {
      console.log("entry", nodeUid);
      Analy.P.Input.putChatformLog(
        `${scenario.value.shopId}_chatbot`,
        scenario.value.id,
        nodeUid,
        ANALY_ACTION.ENTRY
      );
    }
  }
  function checkScroll() {
    const lastIndex = Number(nodes.value.length - 1);
    const sce = lastIndex - 1;

    let isBool = true;
    for (let i = sce; i >= 0; i--) {
      let node = nodes.value[i];
      if (node.nodeType !== "message") {
        isBool = true;
        break;
      } else {
        if (node.body.settings[0].type === "images") {
          isBool = false;
          break;
        } else {
          if (i === 0) {
            isBool = true;
          } else {
            continue;
          }
        }
      }
    }
    return isBool;
  }

  function getLastVariable(settings) {
    let lastVariable = null;

    function findLastVariable(arr) {
      arr.forEach((item) => {
        if (item.variable) {
          lastVariable = item.variable;
        }
        if (item.settings) {
          findLastVariable(item.settings);
        }
      });
    }

    findLastVariable(settings);
    return lastVariable;
  }

  function scrollLastMessage(timeout) {
    setTimeout(() => {
      const el =
        document.getElementsByClassName("bot-message")[
          document.getElementsByClassName("bot-message").length - 1
        ];

      handleScrollIntoView(el, { behavior: "smooth", block: "start" });
    }, timeout);
  }

  function scrollToNode(node) {
    const variable = getLastVariable(node.body.settings);
    const el = document.getElementById(variable);
    if (el) {
      handleScrollIntoView(el);
    } else {
      if (variable === "meifuri") {
        const elFullName = document.getElementById("fullName");
        handleScrollIntoView(elFullName);
      }
    }
  }

  function handleNextNodeSetValue(node) {
    cart.value[node.body.settings[0].variable] = node.body.settings[0].value;

    node.nextNodeUid.forEach((uid) => nextNode(uid, node.id)); // case node condition has many next node uid
  }

  function handleNextNodeCondition(node) {
    const setting = node.body.settings[0];
    const { variable, value: expectedValue } = setting;
    const currentValue = cart.value[variable];

    if (!isConditionMet(currentValue, expectedValue)) return;

    nodes.value.push(node);
    node.nextNodeUid.forEach((uid) => nextNode(uid, node.id)); // case node condition has many next node uid
  }

  function isConditionMet(currentValue, expectedValue) {
    if (typeof currentValue === "boolean") {
      return currentValue === expectedValue;
    } else {
      return currentValue.includes(expectedValue);
    }
  }

  function handleNextNodeTrigger(node) {
    // #complelte
    console.log("complete", node.uid);
    Analy.P.Input.putChatformLog(
      `${scenario.value.shopId}_chatbot`,
      scenario.value.id,
      node.uid,
      ANALY_ACTION.COMPLETE
    );

    if (node.nodeType == NODE_TYPE_HIDDEN.HTML_TASK) {
      handelHtmlTasks(node);
    }

    if (node.nodeType == NODE_TYPE_HIDDEN.HEADLESS_TASK) {
      node.body.settings.forEach((taskSetting) => {
        const taskData = {
          inputs: mapCartDataToTaskInputs(taskSetting.inputs),
          className: taskSetting.task.class_name,
          outputs: taskSetting.outputs,
          userId: dataAccount,
          ssid: ssid.value,
        };

        webSocket().sendDataTaskRunnerToSystemMessageChannel(taskData);
      });
    }

    addNodeWithoutContent(node);
    node?.nextNodeUid?.forEach((uid) => nextNode(uid, node.id)); // case node condition has many next node uid
  }

  function mapCartDataToTaskInputs(inputs) {
    const keys = Object.keys(cart.value).filter((key) => key !== "url");
    keys.forEach((key) => {
      if (inputs[key]) {
        inputs[key].value = cart.value[key];
      }
    });
    return inputs;
  }

  function callCrawlerData(crawlerData) {
    const taskData = {
      inputs: mapCartDataToTaskInputs(crawlerData.inputs),
      className: crawlerData.task.class_name,
      outputs: crawlerData.outputs,
      userId: dataAccount,
      ssid: ssid.value,
    };

    webSocket().sendDataTaskCrawlerToSystemMessageChannel(taskData);
  }

  function handelHtmlTasks(node) {
    node.body.settings.forEach((item) => {
      try {
        const parentDocument = window.parent.document;

        const script = parentDocument.createElement("script");
        script.id = node.uid;
        script.type = "text/javascript";

        const code = mapDataToContent(item.task.content, cart.value);
        script.appendChild(parentDocument.createTextNode(code));

        parentDocument.body.appendChild(script);
      } catch (e) {
        console.log(e);
      }
    });
  }

  function addNodeWithoutContent(node) {
    if (
      [NODE_TYPE.BUTTON, NODE_TYPE_HIDDEN.HTML_TASK].includes(node.nodeType)
    ) {
      node.body.settings.forEach((item) => {
        if (item.task) {
          item.task.content = "";
        } else {
          item.content = "";
        }
      });

      nodes.value.push(node);
    }
  }

  function mapDataToContent(content, data) {
    return content?.replace(/v\.(\w+)/g, (match, key) => {
      if (data.hasOwnProperty(key)) {
        return data[key];
      } else {
        return ``;
      }
    });
  }

  async function getDataFromCrawler(input) {
    const result = await ScenarioRepository.activeCrawler(input);

    return result;
  }

  function updateNode(uid) {
    const indexNode = nodes.value.findLastIndex((item) => item.id == uid);
    nodes.value = nodes.value.slice(0, indexNode + 1);
  }

  function handleResponseResult(data) {
    const input = {
      userId: dataAccount,
      data: data,
      ssid: ssid.value,
      // ssid: JSON.parse(
      //   localStorage.getItem(`chatbot.${process.env.NODE_ENV}_sessionDataId`)
      // ).data,
    };

    ScenarioRepository.handleResponse(input);
  }

  function setDataToRedis() {
    const cartClone = cloneDeep(cart.value);
    delete cartClone.credit_card;
    delete cartClone.password;

    const dataString = { cart: cartClone, nodes: nodes.value };
    const data = JSON.stringify(dataString);
    webSocket().sendPostToSystemMessageChannel({ data: data });
  }

  function onNextNode(isLastNode, uid, nextNodeUid, id, timeout = 500) {
    if (!Array.isArray(nextNodeUid)) return;

    // case last node

    if (!nextNodeUid.length && checkObjectKeys(cart.value) > 1) {
      setDataToRedis();
    }

    nextNodeUid.forEach((nn) => {
      setTimeout(() => {
        nextNode(nn, id);

        // pass params isLastNode as computed
        // use computed isLastNode to get real-time data
        if (isLastNode.value) return;

        globalStore.removeRequest(uid); // remove loading
      }, timeout);
    });
  }

  async function fetchScheduleDate(daysAfterCurrent, rangeDays) {
    const result = await ScenarioRepository.fetchScheduleDate(
      daysAfterCurrent,
      rangeDays
    );

    return result.scheduledDateOption.scheduled_date_option;
  }

  function checkObjectKeys(value) {
    return Object.keys(value).length;
  }

  watch(socketResult, () => {
    if (socketResult.value.data) {
      if (window.parent.creditCard) {
        delete window.parent.creditCard;
      }
    }
  });

  watch(
    nodes,
    () => {
      if (checkObjectKeys(cart.value) > 1) {
        setDataToRedis();
      }
    },
    { deep: true }
  );

  return {
    // data
    scenario,
    nodes,
    container,
    // function
    analyPutLogEntry,
    fetchLastUsedScenario,
    fetchScenarioDetail,
    getLastVariable,
    nextNode,
    updateNode,
    scrollLastMessage,
    getDataFromCrawler,
    callCrawlerData,
    onNextNode,
    handleResponseResult,
    scrollToBottom,
    scrollToNode,
    fetchScheduleDate,
    removeAllExpiredKeys,
    clearScenarioData,
  };
});
