import { defineStore } from "pinia";
import { ref } from "vue";
import ScenarioSettingRepository from "@/apis/repositories/scenarioSettingRepository";

export const useScenarioSettingStore = defineStore("scenarioSetting", () => {
  const scenarioSetting = ref({});

  const originUrl =
    process.env.NODE_ENV == "development"
      ? process.env.URL_CRAWLER
      : document.location.href;

  const dataScenario =
    window.parent.document
      .querySelector("script[data-account]")
      ?.getAttribute("data-scenario") ||
    process.env.DATA_SCENARIO ||
    null;
  const dataAccount =
    window.parent.document
      .querySelector("script[data-account]")
      ?.getAttribute("data-account") ||
    process.env.DATA_ACCOUNT ||
    null;

  async function fetchScenarioSetting() {
    const result = await ScenarioSettingRepository.fetchScenarioSetting(
      dataAccount,
      originUrl,
      dataScenario
    );

    scenarioSetting.value = result.fetchScenarioSetting;
  }

  return {
    // data
    scenarioSetting,

    // function
    fetchScenarioSetting,
  };
});
