import { defineStore } from "pinia";

import convertNameRepository from "../apis/repositories/convertNameRepository";
import { useCartStore } from "@/stores/cart";
import { storeToRefs } from "pinia";

export const useConvertNameStore = defineStore("convertName", () => {
  const cartStore = useCartStore();
  const { cart } = storeToRefs(cartStore);

  async function fetchFullNameKana(fullName) {
    const result = await convertNameRepository.getKana(fullName);

    return result;
  }

  async function getKana(fullName) {
    const result = await fetchFullNameKana(fullName);
    console.log("result", result.getKanaName);

    cart.value.sei = result.getKanaName.last;
    cart.value.mei = result.getKanaName.first;
    cart.value.seifuri = result.getKanaName.lastKana;
    cart.value.meifuri = result.getKanaName.firstKana;
    cart.value.fullName = `${cart.value.sei}${cart.value.mei}`;

    return result;
  }

  return {
    // data
    getKana,
    fetchFullNameKana,
  };
});
