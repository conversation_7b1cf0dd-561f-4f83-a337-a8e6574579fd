@import "bootstrap/dist/css/bootstrap.min.css";
@import "bootstrap-vue-next/dist/bootstrap-vue-next.css";
@import "vue-select/dist/vue-select.css";

html, body { 
  -ms-overflow-style: none; 
  scrollbar-width: none;  
}

html::-webkit-scrollbar { 
  display: none;
}

body {
  overflow-y: scroll; 
}

ul {
  list-style-type: none; 
  padding: 0; 
  margin: 0; 
}

.form-control::placeholder {
  color: #c3ccd5; 
}

.layout_vertical {
 display: flex;
 flex-direction: column;
}

.layout_horizontal {
  display: flex;
  flex-direction: row;
}

