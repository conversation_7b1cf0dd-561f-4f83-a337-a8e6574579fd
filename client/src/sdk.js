console.log("init iframe", new Date().getTime());

function mobileCheck() {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  return /android|iphone|ipod|iemobile|blackberry|kindle|windows phone|opera mini|nokia/i.test(
    userAgent
  );
}

function isIphone() {
  const ua = navigator.userAgent.toLowerCase();
  return /iphone|ipod/.test(ua);
}

const rootDiv = document.createElement("div");
const iframe = document.createElement("iframe");
const url = process.env.SDK_HOST_URL;
const idChatBot = process.env.ID_CHAT_BOT;
const timeStamp = Date.now();

rootDiv.id = "chatbot";
rootDiv.style.cssText = `
  position: fixed;
  z-index: 99999999999;
  border: none;
  background: transparent;
`;

if (mobileCheck() && isIphone()) {
  rootDiv.style.cssText = `
    position: fixed;
    inset: 0;
    z-index: 99999999999;
    border: none;
    width: 100%;
    height: 100dvh;
    overflow: hidden;
    background: transparent;
  `;
}

const iframeContent = `
  <html style="display:block; height:100%; width:100%;">
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
      <script charset="UTF-8" src="${url}/chatbot.js?v=${timeStamp}" async></script>
      <script src="${process.env.ANALY_P_TOOLS}" type="text/javascript" defer></script>
    </head>
    <body style="margin:0; padding:0; width:100%; height:100%; overflow:hidden;">
      <div id="${idChatBot}" style="display:block; width:100%; height:100%;"></div>
    </body>
  </html>
`;

rootDiv.appendChild(iframe);
document.body.appendChild(rootDiv);

const iframeContentDocument = iframe.contentDocument;

if (iframeContentDocument) {
  iframeContentDocument.open();
  iframeContentDocument.write(iframeContent);
  iframeContentDocument.close();

  iframe.style.cssText = `
    display: block;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: ${mobileCheck() ? "0px" : "10px"};
  `;
} else {
  console.error("iframe content document not available");
}

console.log("init iframe success", new Date().getTime());
