console.log("init app", new Date().getTime());

import "./assets/css/main.scss";

import { createPinia, storeToRefs } from "pinia";

import { createApp, h } from "vue";

import { useScenarioSettingStore } from "@/stores/scenarioSetting";
import { useScenarioStore } from "@/stores/scenario";
import { addStyleTag, addScriptTag } from "@/ultilities/helper.js";

// =============COMPONENTS SUPPORT=============
import BootstrapVueNext from "bootstrap-vue-next";
import FormValidator from "@/components/shared/FormValidator.vue";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

import ElementPlus from "element-plus";

import vSelect from "vue-select";

import App from "./App.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const pinia = createPinia();

const app = createApp({
  setup() {},
  render: () => h(App),
});

app.use(pinia);
app.use(BootstrapVueNext);
app.use(ElementPlus);

app.component("FormValidator", FormValidator);
app.component("VSelect", vSelect);
app.component("FontAwesomeIcon", FontAwesomeIcon);

VueDatePicker.props.format.default = "dd/MM/yyyy";
VueDatePicker.props.actionRow.default = {
  showCancel: false,
  showPreview: false,
  showSelect: false,
};
VueDatePicker.props.autoApply.default = true;
app.component("DatePicker", VueDatePicker);

// Define Stores
const scenarioSettingStore = useScenarioSettingStore();
const scenarioStore = useScenarioStore();

// Destructure scenarioSetting
const { scenarioSetting } = storeToRefs(scenarioSettingStore);

// Execute asynchronous functions
await scenarioStore.removeAllExpiredKeys();
await scenarioStore.fetchLastUsedScenario();
// Uncomment if needed for fetching scenario details

// Fetch the scenario setting
await scenarioSettingStore.fetchScenarioSetting();

// Load additional scripts in development mode
if (process.env.NODE_ENV === "development") {
  addScriptTag(process.env.ANALY_P_TOOLS, "link");
}

// Check if scenarioSetting has any keys
if (Object.keys(scenarioSetting.value).length) {
  const idChatBot = process.env.ID_CHAT_BOT;
  const { cssCustomize, javascriptCustomize } =
    scenarioSetting.value.scenarioDesignSetting || {};

  // Add custom CSS and JavaScript if available
  if (cssCustomize) addStyleTag(cssCustomize);
  if (javascriptCustomize) addScriptTag(javascriptCustomize);

  app.mount(`#${idChatBot}`);
}
