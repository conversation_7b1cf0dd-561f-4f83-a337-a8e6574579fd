import { pick } from "lodash";

export const NODE_TYPE = {
  MESSAGE: "message",
  INPUT: "input",
  MODAL: "modal",
  BUTTON: "button",
  BUTTON_V2: "button_v2",
  CONDITION: "condition",
  HTML_TASK: "html_tasks",
  HEADLESS_TASK: "headless_tasks",
  SET_VALUE: "set_value",
};

export const NODE_TYPE_HIDDEN = pick(NODE_TYPE, [
  "CONDITION",
  "HTML_TASK",
  "HEADLESS_TASK",
  "SET_VALUE",
]);

export const MODULE_TYPE = {
  QUANTITY: "quantity",
  VARIANT: "variant",
  NAME: "name",
  FULL_NAME: "full_name",
  PASSWORD: "password",
  TEXT: "text",
  SELECT: "select",
  EMAIL_AND_PASSWORD: "email_and_password",
  ADDRESS: "address",
  SEX_AND_BIRTHDAY: "sex_and_birthday",
  TEL_EMAIL_PASSWORD: "tel_email_password",
  PAYMENT_METHOD: "payment_method",
  CREDIT_CARD: "credit_card",
  BUTTON: "button",
  RADIO_BUTTON: "radio_button",
  RADIO_BUTTON_RESELECTABLE: "radio_button_reselectable",
  SCHEDULED_DELIVERY: "scheduled_delivery",
  NAME_SEX_BIRTHDAY: "name_sex_birthday",
  CHECKBOX: "checkbox",
  RADIO_BUTTON_GRID: "radio_button_grid",
  RADIO_BUTTONS_MULTI_SELECT: "radio_buttons_multi_select",
  NAME_BIRTHDAY_TEL_EMAIL: "name_birthday_tel_email",
  INFORMATION: "information",
};

export const MESSAGE_TYPE = {
  TEXT: "text",
  IMAGE: "image",
  IMAGES: "images",
  VIDEO: "video",
};

export const INPUT_TYPE = {
  SELECT: "select",
  INPUT: "input",
  RADIO: "radio",
  CHECKBOX: "checkbox",
  DATE: "date",
};

export const LAYOUT = {
  HORIZONTAL: "horizontal",
  VERTICAL: "vertical",
};

export const HALF_WIDTH_INPUT_FIELDS = [
  "card_number",
  "zipcode",
  "tel",
  "tel1",
  "tel2",
  "tel3",
  "card_cvv",
];

export const BUTTON_TYPE = {
  TEMPLATE: "template",
  BUTTON: "button",
  POLICY: "policy",
  CHECKBOX: "checkbox",
};

export const ANALY_ACTION = {
  DISPLAY: "display",
  RESUCCESS: "re-success",
  SUCCESS: "success",
  VALIDATION_ERROR: "validation-error",
  RE_VALIDATION_ERROR: "re-validation-error",
  IMPRESSION: "impression",
  ENTRY: "entry",
  COMPLETE: "complete",
  CONVERSION: "conversion",
};

export const ELEMENT_SCROLL = [
  "zipcode",
  "tel",
  "tel1",
  "tel2",
  "tel3",
  "address02",
];
