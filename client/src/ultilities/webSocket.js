import { useGlobalStore } from "@/stores/global";
import { useScenarioStore } from "@/stores/scenario";
import { storeToRefs } from "pinia";
import { ref } from "vue";

const channel = ref(null);

import ActionCable from "actioncable";

export default function webSocket() {
  const globalStore = useGlobalStore();
  const { socketResult } = storeToRefs(globalStore);

  const scenarioStore = useScenarioStore();
  const { scenario } = storeToRefs(scenarioStore);
  const dataAccount =
    window.parent.document
      .querySelector("script[data-account]")
      ?.getAttribute("data-account") || process.env.DATA_ACCOUNT;

  const cable = ActionCable.createConsumer(
    process.env.WEBSOCKET_HOST +
      `?dataAccount=${dataAccount}` +
      `&ssid=${scenario.value.ssid}`
  );

  const createChannel = function () {
    channel.value = cable.subscriptions.create(
      {
        channel: "SystemMessageChannel",
        dataAccount: dataAccount,
        ssid: scenario.value.ssid,
      },
      {
        received: (result) => {
          socketResult.value = result;
          console.log(result);
        },
        connected: () => {
          console.log("WebSocket connected");
        },
        rejected: () => {
          cable.disconnect();
        },
      }
    );
  };
  // WebSocket connection
  const tryConnectWebSocket = function () {
    try {
      if (!channel.value) {
        createChannel();
      }
    } catch (e) {
      console.log("e", e);
    }
  };

  //  WebSocket Data Task Runner on the server
  const sendDataTaskRunnerToSystemMessageChannel = function (runnerData) {
    try {
      if (channel.value) {
        channel.value.perform("task_runner", runnerData);
      } else {
        console.log("Channel not initialized. Please connect WebSocket first.");
      }
    } catch (e) {
      console.log("Error:", e);
    }
  };

  //  WebSocket Data Task Runner on the server
  const sendDataTaskCrawlerToSystemMessageChannel = function (crawlerData) {
    try {
      if (channel.value) {
        channel.value.perform("task_crawler", crawlerData);
      } else {
        console.log("Channel not initialized. Please connect WebSocket first.");
      }
    } catch (e) {
      console.log("Error:", e);
    }
  };

  //  WebSocket Post on the server
  const sendPostToSystemMessageChannel = function (postData) {
    try {
      if (channel.value) {
        channel.value.perform("post", postData);
      } else {
        console.log("Channel not initialized. Please connect WebSocket first.");
      }
    } catch (e) {
      console.log("Error:", e);
    }
  };

  return {
    sendPostToSystemMessageChannel,
    tryConnectWebSocket,
    sendDataTaskRunnerToSystemMessageChannel,
    sendDataTaskCrawlerToSystemMessageChannel,
  };
}
